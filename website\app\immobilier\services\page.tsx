import { <PERSON>ada<PERSON> } from "next";
import Link from "next/link";
import Image from "next/image";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import {
  ArrowRight,
  Home,
  Building2,
  MapPin,
  Key,
  Users,
  Shield,
  CheckCircle,
  Clock,
  Star,
  Scale,
  Compass
} from "lucide-react";
import { getDetailedImmobilierServices, getImmobilierContent } from "@/app/cms/utils/immobilier";

const immobilierData = getImmobilierContent();
const detailedServices = getDetailedImmobilierServices();

export const metadata: Metadata = {
  title: "Nos Services Immobiliers - Charlie Oscar Consulting",
  description: "Découvrez tous nos services immobiliers au Cameroun : achat, vente, location, conseil juridique. Expertise complète pour tous vos projets.",
  keywords: "services immobiliers Cameroun, achat vente immobilier, location immobilier, conseil juridique immobilier",
  openGraph: {
    title: "Nos Services Immobiliers - Charlie Oscar Consulting",
    description: "Expertise complète en immobilier au Cameroun. Achat, vente, location et conseil juridique.",
    type: "website",
    locale: "fr_FR",
    siteName: "Charlie Oscar Consulting"
  },
  alternates: {
    canonical: "/immobilier/services"
  }
};

export default function ImmobilierServicesPage() {

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section - Clean & Minimal */}
      <section className="relative py-32 bg-gradient-to-br from-gray-50 to-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center space-y-12">
            <div className="space-y-8">
              <div className="inline-flex items-center px-6 py-3 bg-white text-blue-700 rounded-full text-sm font-medium shadow-lg border border-blue-100">
                <Home className="w-4 h-4 mr-2" />
                Services Immobiliers
              </div>

              <h1 className="text-5xl lg:text-7xl font-bold text-gray-900 leading-tight">
                Nos Services
                <span className="block text-blue-600">Immobiliers</span>
              </h1>

              <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                Une gamme complète de services pour tous vos besoins immobiliers au Cameroun.
                De l'achat à la vente, de la location au conseil juridique.
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-6 justify-center">
              <Button asChild size="lg" className="bg-blue-600 hover:bg-blue-700 px-8 py-4 text-lg group">
                <Link href="/contact" className="flex items-center">
                  Demander un devis
                  <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
                </Link>
              </Button>

              <Button asChild size="lg" variant="outline" className="border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white px-8 py-4 text-lg">
                <Link href="/contact">
                  Nous contacter
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Services Section with Accordions */}
      <section className="py-32">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto space-y-16">
            {detailedServices.map((service) => (
              <div key={service.id} id={service.id} className="scroll-mt-24">
                <Card className="overflow-hidden border-0 shadow-2xl bg-white">
                  <div className="grid lg:grid-cols-2 gap-0">
                    {/* Image Section */}
                    <div className="relative h-80 lg:h-auto">
                      <Image
                        src={service.image}
                        alt={service.title}
                        fill
                        className="object-cover"
                      />
                      <div className="absolute inset-0 bg-gradient-to-r from-black/20 to-transparent"></div>
                    </div>

                    {/* Content Section */}
                    <div className="p-12 space-y-8">
                      <div className="space-y-6">
                        <div className="flex items-center space-x-4">
                          <Badge className={`
                            ${service.color === 'orange' ? 'bg-orange-100 text-orange-800 border-orange-200' : ''}
                            ${service.color === 'blue' ? 'bg-blue-100 text-blue-800 border-blue-200' : ''}
                            ${service.color === 'green' ? 'bg-green-100 text-green-800 border-green-200' : ''}
                            ${service.color === 'purple' ? 'bg-purple-100 text-purple-800 border-purple-200' : ''}
                            ${service.color === 'yellow' ? 'bg-yellow-100 text-yellow-800 border-yellow-200' : ''}
                            ${service.color === 'red' ? 'bg-red-100 text-red-800 border-red-200' : ''}
                            px-4 py-2 text-sm font-medium
                          `}>
                            {service.icon === 'map' && <MapPin className="w-3 h-3 mr-1" />}
                            {service.icon === 'home' && <Home className="w-3 h-3 mr-1" />}
                            {service.icon === 'building' && <Building2 className="w-3 h-3 mr-1" />}
                            {service.icon === 'key' && <Key className="w-3 h-3 mr-1" />}
                            {service.icon === 'compass' && <Compass className="w-3 h-3 mr-1" />}
                            {service.icon === 'scale' && <Scale className="w-3 h-3 mr-1" />}
                            Service Spécialisé
                          </Badge>
                        </div>

                        <h2 className="text-3xl font-bold text-gray-900">
                          {service.title}
                        </h2>

                        <p className="text-lg text-gray-600 leading-relaxed">
                          {service.description}
                        </p>
                      </div>

                      {/* Services Accordion */}
                      <Accordion type="single" collapsible className="w-full">
                        {service.services.map((subService, subIndex) => (
                          <AccordionItem key={subIndex} value={`item-${subIndex}`} className="border-gray-200">
                            <AccordionTrigger className="text-left hover:no-underline hover:text-blue-600 transition-colors">
                              <span className="font-semibold">{subService.title}</span>
                            </AccordionTrigger>
                            <AccordionContent className="text-gray-600 leading-relaxed pt-2">
                              {subService.description}
                            </AccordionContent>
                          </AccordionItem>
                        ))}
                      </Accordion>

                      <div className="pt-4">
                        <Button asChild className={`
                          ${service.color === 'orange' ? 'bg-orange-600 hover:bg-orange-700' : ''}
                          ${service.color === 'blue' ? 'bg-blue-600 hover:bg-blue-700' : ''}
                          ${service.color === 'green' ? 'bg-green-600 hover:bg-green-700' : ''}
                          ${service.color === 'purple' ? 'bg-purple-600 hover:bg-purple-700' : ''}
                          ${service.color === 'yellow' ? 'bg-yellow-600 hover:bg-yellow-700' : ''}
                          ${service.color === 'red' ? 'bg-red-600 hover:bg-red-700' : ''}
                          text-white group
                        `}>
                          <Link href="/contact" className="flex items-center">
                            Demander ce service
                            <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                          </Link>
                        </Button>
                      </div>
                    </div>
                  </div>
                </Card>
              </div>
            ))}
          </div>
        </div>
      </section>
      {/* CTA Section - Clean & Modern */}
      <section className="py-32 bg-gradient-to-br from-blue-600 via-blue-700 to-blue-800 text-white relative overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-0 left-0 w-full h-full bg-[url('data:image/svg+xml,%3Csvg%20width%3D%2260%22%20height%3D%2260%22%20viewBox%3D%220%200%2060%2060%22%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%3E%3Cg%20fill%3D%22none%22%20fill-rule%3D%22evenodd%22%3E%3Cg%20fill%3D%22%23ffffff%22%20fill-opacity%3D%220.1%22%3E%3Ccircle%20cx%3D%2230%22%20cy%3D%2230%22%20r%3D%222%22/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')]"></div>
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-4xl mx-auto text-center space-y-12">
            <div className="space-y-6">
              <h2 className="text-4xl md:text-5xl font-bold leading-tight">
                Besoin d'un Service Personnalisé ?
              </h2>

              <p className="text-xl text-blue-100 leading-relaxed max-w-3xl mx-auto">
                Chaque projet immobilier est unique. Nos experts analysent votre situation
                et vous proposent la solution la plus adaptée à vos besoins.
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-6 justify-center">
              <Button
                asChild
                size="lg"
                className="bg-white text-blue-600 hover:bg-gray-100 px-8 py-4 text-lg group"
                variant="secondary"
              >
                <Link href="/contact" className="flex items-center">
                  Contactez-nous maintenant !
                  <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
