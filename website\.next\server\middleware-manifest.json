{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_4442ae7e._.js", "server/edge/chunks/[root-of-the-server]__e9ad0545._.js", "server/edge/chunks/edge-wrapper_1f641995.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/api\\/test(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\\\.json)?[\\/#\\?]?$", "originalSource": "/api/test/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/test(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\\\.json)?[\\/#\\?]?$", "originalSource": "/test/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "QZPjl6R8b12JU0ik++o4llEwcy+Uu+mjvuZmYddXz8w=", "__NEXT_PREVIEW_MODE_ID": "5533b149d45a2917b1516a89bc560b47", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "bee90104fc7422d5e957b1c1564cd987087c1bfffc938b7af7b3de2645a70000", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "3d108a88b8866421f624a8369b1d23c3eb45cd1c4c31b0497ab7d9ae83bd1372"}}}, "sortedMiddleware": ["/"], "functions": {}}