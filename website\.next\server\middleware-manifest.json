{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_4442ae7e._.js", "server/edge/chunks/[root-of-the-server]__e9ad0545._.js", "server/edge/chunks/edge-wrapper_1f641995.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/api\\/test(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\\\.json)?[\\/#\\?]?$", "originalSource": "/api/test/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/test(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\\\.json)?[\\/#\\?]?$", "originalSource": "/test/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "QZPjl6R8b12JU0ik++o4llEwcy+Uu+mjvuZmYddXz8w=", "__NEXT_PREVIEW_MODE_ID": "b53a748fc469c1fa190025ba9c5d653c", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "350eb51e11a7855869b32906bdd82de0ed507786019ce8188ea8e9d49bd1c7a0", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "10f7e9847ce45d8de179c1247a3374ae8c1bbed5cfb61a68244bb67ab156e07e"}}}, "sortedMiddleware": ["/"], "functions": {}}