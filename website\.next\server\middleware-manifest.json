{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_4442ae7e._.js", "server/edge/chunks/[root-of-the-server]__e9ad0545._.js", "server/edge/chunks/edge-wrapper_1f641995.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/api\\/test(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\\\.json)?[\\/#\\?]?$", "originalSource": "/api/test/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/test(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\\\.json)?[\\/#\\?]?$", "originalSource": "/test/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "QZPjl6R8b12JU0ik++o4llEwcy+Uu+mjvuZmYddXz8w=", "__NEXT_PREVIEW_MODE_ID": "1481117540761955d077497484edce63", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "49c12d7863c73d8801d1650c0237126a323cf66ea7eb13082eb7ccad508f6644", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "2114a81df3c3320085c4a5ab636c8f95f3865ff52a86bb9c0ca84e683b7a3fc5"}}}, "sortedMiddleware": ["/"], "functions": {}}