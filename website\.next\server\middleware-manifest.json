{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_4442ae7e._.js", "server/edge/chunks/[root-of-the-server]__e9ad0545._.js", "server/edge/chunks/edge-wrapper_1f641995.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/api\\/test(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\\\.json)?[\\/#\\?]?$", "originalSource": "/api/test/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/test(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\\\.json)?[\\/#\\?]?$", "originalSource": "/test/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "QZPjl6R8b12JU0ik++o4llEwcy+Uu+mjvuZmYddXz8w=", "__NEXT_PREVIEW_MODE_ID": "6d685d2a28adfd17f925257deef7c1ec", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "f243845328493b8a2ad4e23ec9ee6adeaf1d36c470fa7e3e6ac99dba3c8eab52", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "f922ce7bcaa087d84bffa1ced549a62ab1985f7b731ea7f65864b29f8eca0575"}}}, "sortedMiddleware": ["/"], "functions": {}}