import { Metadata } from "next";
import { HeroSection } from "@/components/homepage/hero-section";
import { CompanyIntro } from "@/components/homepage/company-intro";
import { DomainsSection } from "@/components/homepage/domains-section";
import { FoncierServices } from "@/components/homepage/foncier-services";
import { ProjectsShowcase } from "@/components/homepage/projects-showcase";
import { WhyChooseUs } from "@/components/homepage/why-choose-us";
import { BlogSection } from "@/components/homepage/blog-section";
import { ContactForm } from "@/components/homepage/contact-form";
import { CTASection } from "@/components/homepage/cta-section";
import { getHomeContent } from "@/app/cms/utils/home";
import { generateMetadata as generateSEOMetadata, generateOrganizationSchema, generateWebsiteSchema } from "@/app/lib/seo-config";

// Generate metadata for homepage
export const metadata: Metadata = generateSEOMetadata({
  title: "Accueil",
  description: "<PERSON>ting - Votre partenaire de confiance en foncier, immobilier et construction au Cameroun. Expertise complète pour sécuriser vos droits fonciers, gérer vos biens immobiliers et réaliser vos projets de construction.",
  keywords: [
    "consultation foncière gratuite",
    "expert foncier Cameroun",
    "services immobiliers Cameroun",
    "construction Cameroun",
    "titre foncier rapide",
    "sécurisation foncière"
  ],
  canonical: "/",
  ogImage: "/images/seo/og-default.jpg"
});

export default function Home() {
  const content = getHomeContent();

  // Generate structured data
  const organizationSchema = generateOrganizationSchema();
  const websiteSchema = generateWebsiteSchema();

  return (
    <>
      {/* Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(organizationSchema)
        }}
      />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(websiteSchema)
        }}
      />

      <div className="min-h-screen py-16 sm:py-8">
        <HeroSection content={content.hero} />
        <CompanyIntro content={content.about} />
        <DomainsSection content={content.services} section={content.domainsSection} />
        <FoncierServices />
        {/* <ProjectsShowcase /> To be implemented later */}
        <WhyChooseUs content={content.whyChooseUs} />
        <CTASection content={content.ctaSection} />
        {/* <BlogSection /> */}
        <ContactForm content={content.contactForm} />
      </div>
    </>
  );
}
