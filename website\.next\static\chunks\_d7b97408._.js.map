{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/charlie-oscar-consulting/website/app/translations/fr.ts"], "sourcesContent": ["// French translations\r\nconst fr = {\r\n  fill:\"Merci de remplir soigneusement ce formulaire. Un expert de <PERSON> Consulting vous contactera dans les plus brefs délais pour vous accompagner dans votre projet.\",\r\n  common: {\r\n    next: \"Continuer\",\r\n    previous: \"Précédent\",\r\n    submit: \"Soumettre ma demande\",\r\n    processing: \"Traitement en cours...\",\r\n    required: \"*\",\r\n    optional: \"Facultatif\",\r\n    fileSize: \"La taille maximale est de 2 Mo\",\r\n    filePreview: \"Aperçu:\",\r\n    fileSelected: \"Fichier sélectionné:\",\r\n    fileFormats: \".pdf, .jpg, .png, max 2 Mo par fichier\",\r\n    notProvided: \"Non renseigné\",\r\n    notSpecified: \"Non précisé\",\r\n    uploading: \"Téléchargement...\",\r\n    uploaded: \"Téléchargé\",\r\n    uploadFailed: \"Échec du téléchargement\",\r\n    retry: \"Réessayer\",\r\n    // success message for succesfull form upload\r\n    success:\"Votre demande a été soumise avec succès. Un conseiller de Charlie Oscar Consulting vous contactera dans les plus brefs délais.\",\r\n    online: \"En ligne\",\r\n    offline: \"Hors ligne\",\r\n    connectionLost: \"Connexion Internet perdue. Certaines fonctionnalités peuvent être indisponibles.\",\r\n    connectionRestored: \"Connexion Internet rétablie.\",\r\n    fileUploadOffline:\"Vous ne pouvez pas uploader des fichier quand vous êtes hors ligne.\",\r\n    waitForUploads: \"Veuillez attendre la fin des téléchargements...\",\r\n    form: \"Formulaire\",\r\n    apiDocs: \"Documentation API\",\r\n    testTools: \"Outils de test\",\r\n    dropFilesHere: \"Déposez vos fichiers ici ou cliquez pour parcourir\",\r\n    maxFileSize: \"Taille maximale: 2 Mo\",\r\n    fileTooLarge: \"Le fichier est trop volumineux. La taille maximale est de 2 Mo\",\r\n    invalidFileType: \"Type de fichier non pris en charge\",\r\n    fileNameTooShort: \"Le nom du fichier doit comporter au moins 3 caractères\",\r\n    cancel: \"Annuler\",\r\n  },\r\n\r\n  validation: {\r\n    required: \"Ce champ est obligatoire\",\r\n    minLength: \"Doit contenir au moins {min} caractères\",\r\n    email: \"Veuillez entrer une adresse email valide\",\r\n    phone: \"Veuillez entrer un numéro de téléphone valide\",\r\n    consent: \"Vous devez donner votre consentement pour continuer\",\r\n    date: \"Veuillez entrer une date valide\",\r\n    selection: \"Veuillez faire une sélection\",\r\n    fileRequired: \"Veuillez téléverser un fichier\",\r\n    fileSize: \"La taille du fichier doit être inférieure à {size}\",\r\n    fileType: \"Type de fichier non pris en charge\",\r\n  },\r\n\r\n  sidebar: {\r\n    title: \"Charlie Oscar\",\r\n    subtitle: \"Consulting\",\r\n    step: \"Étape\",\r\n    of: \"/\",\r\n    currentStep: \"Étape actuelle\",\r\n  },\r\n\r\n  steps: {\r\n    personal: {\r\n      name: \"Informations Personnelles\",\r\n      description: \"Entrez vos informations personnelles pour nous permettre de mieux vous connaître.\",\r\n    },\r\n    emergency: {\r\n      name: \"Contacts d'urgence et procédure\",\r\n      description: \"Indiquez vos contacts d'urgence et la procédure souhaitée.\",\r\n    },\r\n    documents: {\r\n      name: \"Documents et localisation du terrain\",\r\n      description: \"Fournissez les documents et les informations sur la localisation du terrain.\",\r\n    },\r\n    summary: {\r\n      name: \"Récapitulatif et confirmation\",\r\n      description: \"Vérifiez toutes les informations avant de soumettre votre demande.\",\r\n    },\r\n  },\r\n\r\n  stepPersonal: {\r\n    title: \"Vos Informations Personnelles\",\r\n    intro: \"Veuillez remplir tous les champs obligatoires marqués d'un astérisque (*) avec précision.\",\r\n    dataProtection: {\r\n      title: \"Protection des données\",\r\n      description: \"Les informations collectées sont strictement confidentielles et utilisées uniquement dans le cadre du traitement de votre demande foncière par Charlie Oscar Consulting SARL.\",\r\n      consent: \"J'accepte que mes informations soient traitées par Charlie Oscar Consulting SARL\",\r\n    },\r\n    personalInfo: {\r\n      title: \"Informations personnelles\",\r\n      fullName: {\r\n        label: \"Nom & prénoms\",\r\n        placeholder: \"Ex: MBARGA Paul Serge\",\r\n      },\r\n      gender: {\r\n        label: \"Sexe\",\r\n        male: \"Masculin\",\r\n        female: \"Féminin\",\r\n      },\r\n      birthDate: {\r\n        label: \"Date de naissance\",\r\n      },\r\n      birthPlace: {\r\n        label: \"Lieu de naissance\",\r\n        placeholder: \"Ex: Yaoundé, Cameroun\",\r\n      },\r\n      nationality: {\r\n        label: \"Nationalité\",\r\n        placeholder: \"Sélectionnez votre nationalité\",\r\n        options: {\r\n          cameroon: \"Camerounaise\",\r\n          france: \"Française\",\r\n          senegal: \"Sénégalaise\",\r\n          ivoryCoast: \"Ivoirienne\",\r\n          gabon: \"Gabonaise\",\r\n          congo: \"Congolaise\",\r\n          other: \"Autre\",\r\n        },\r\n      },\r\n      profession: {\r\n        label: \"Profession\",\r\n        placeholder: \"Ex: Enseignant, Commerçant\",\r\n      },\r\n    },\r\n    contactInfo: {\r\n      title: \"Informations de contact\",\r\n      primaryPhone: {\r\n        label: \"Téléphone principal\",\r\n        placeholder: \"Ex: +237 6XXXXXXXX\",\r\n      },\r\n      secondaryPhone: {\r\n        label: \"Téléphone secondaire\",\r\n        placeholder: \"Ex: +237 6XXXXXXXX\",\r\n      },\r\n      email: {\r\n        label: \"Email\",\r\n        placeholder: \"<EMAIL>\",\r\n      },\r\n      address: {\r\n        label: \"Adresse actuelle\",\r\n        placeholder: \"Ex: Quartier Bastos, Rue 1.890, Yaoundé\",\r\n      },\r\n      idDocument: {\r\n        label: \"Document d'identité\",\r\n        description: \"Téléversez une copie claire et lisible de votre CNI ou passeport\",\r\n        notUploaded: \"Aucun document téléversé\",\r\n        nameRequired: \"Veuillez d'abord saisir votre nom complet\",\r\n      },\r\n    },\r\n  },\r\n\r\n  stepDocumentsLocation: {\r\n    title: \"Documents et localisation du terrain\",\r\n    documents: {\r\n      title: \"Documents\",\r\n      intro: \"Veuillez indiquer et téléverser les documents que vous possédez déjà concernant cette procédure. Le plan de localisation est fortement recommandé s'il est disponible.\",\r\n      availableDocs: {\r\n        label: \"Documents disponibles\",\r\n        help: \"Cochez tous les documents que vous pouvez fournir\",\r\n        options: {\r\n          customaryProperty: \"Attestation de propriété coutumière\",\r\n          saleAct: \"Acte de vente / donation\",\r\n          occupationPermit: \"Autorisation d'occupation\",\r\n          locationPlan: \"Plan de localisation / croquis du terrain\",\r\n          landTitle: \"Titre foncier\",\r\n          otherDocs: \"Autres documents\",\r\n        },\r\n      },\r\n      upload: {\r\n        label: \"Téléversement de documents\",\r\n        documentLabel: \"Document\",\r\n        description: \"Téléversez vos documents les plus pertinents pour votre demande une a la fois. Vous pourrez fournir des documents supplémentaires ultérieurement.\",\r\n      },\r\n      details: {\r\n        label: \"Précisions sur les documents\",\r\n        placeholder: \"Ex: Le titre foncier date de 2015, l'acte de vente notarié est joint...\",\r\n        help: \"Facultatif : précisez la nature ou l'état des documents fournis\",\r\n      },\r\n    },\r\n    location: {\r\n      title: \"Localisation\",\r\n      intro: \"Veuillez fournir les informations précises concernant la localisation du terrain concerné par la procédure.\",\r\n      zoneType: {\r\n        label: \"Type de zone\",\r\n        urban: \"Urbaine\",\r\n        rural: \"Rurale\",\r\n        help: \"Sélectionnez le type d'environnement où se situe le terrain\",\r\n      },\r\n      region: {\r\n        label: \"Région\",\r\n        placeholder: \"Sélectionnez une région\",\r\n        options: [\r\n          \"Centre\", \"Littoral\", \"Sud\", \"Est\", \"Ouest\",\r\n          \"Nord-Ouest\", \"Sud-Ouest\", \"Nord\", \"Extrême-Nord\", \"Adamaoua\"\r\n        ],\r\n      },\r\n      department: {\r\n        label: \"Département\",\r\n        placeholder: \"Sélectionnez ou preciser un département\",\r\n        options: {\r\n          centre: [\"Mfoundi\", \"Nyong-et-Kellé\", \"Nyong-et-So'o\", \"Lekié\", \"Mbam-et-Inoubou\"],\r\n          littoral: [\"Wouri\", \"Sanaga-Maritime\", \"Nkam\", \"Moungo\"],\r\n          south: [\"Océan\", \"Vallée-du-Ntem\", \"Mvila\", \"Dja-et-Lobo\"],\r\n          east: [\"Haut-Nyong\", \"Kadey\", \"Lom-et-Djérem\", \"Boumba-et-Ngoko\"],\r\n          west: [\"Mifi\", \"Menoua\", \"Bamboutos\", \"Haut-Nkam\", \"Ndé\", \"Koung-Khi\"],\r\n        },\r\n      },\r\n      subdivision: {\r\n        label: \"Arrondissement\",\r\n        placeholder: \"Ex: Yaoundé II, Douala III, Bafoussam I\",\r\n        help: \"Précisez l'arrondissement où se situe le terrain\",\r\n      },\r\n      neighborhood: {\r\n        label: \"Quartier ou village\",\r\n        placeholder: \"Ex: Mokolo, Bonapriso, Banengo...\",\r\n      },\r\n      locationDetails: {\r\n        label: \"Lieu-dit\",\r\n        placeholder: \"Ex: À proximité de l'église Saint-Jean, derrière le marché central...\",\r\n        help: \"Repère géographique spécifique facilitant la localisation du terrain\",\r\n      },\r\n      area: {\r\n        label: \"Superficie estimée\",\r\n        placeholder: \"Ex: 500\",\r\n        unit: \"m²\",\r\n        help: \"Surface approximative du terrain en mètres carrés\",\r\n      },\r\n    },\r\n  },\r\n\r\n  stepEmergencyProcedure: {\r\n    title: \"Procédure et urgence\",\r\n    emergencyContacts: {\r\n      title: \"Contacts d'urgence\",\r\n      intro: \"Veuillez indiquer au moins une personne à contacter en cas d'indisponibilité.\",\r\n      contact1: {\r\n        title: \"Personne 1 (obligatoire)\",\r\n        name: {\r\n          label: \"Nom & prénoms\",\r\n          placeholder: \"Ex: ATANGANA Marie Claire\",\r\n        },\r\n        phone: {\r\n          label: \"Téléphone\",\r\n          placeholder: \"Ex: +237 6XXXXXXXX\",\r\n        },\r\n        relation: {\r\n          label: \"Lien avec le demandeur\",\r\n          placeholder: \"Ex: Frère, Conjoint(e), Collègue\",\r\n          help: \"Précisez votre relation avec cette personne\",\r\n        },\r\n      },\r\n      contact2: {\r\n        title: \"Personne 2 (facultatif)\",\r\n        name: {\r\n          label: \"Nom & prénoms\",\r\n          placeholder: \"Ex: ESSOMBA Jean\",\r\n        },\r\n        phone: {\r\n          label: \"Téléphone\",\r\n          placeholder: \"Ex: +237 6XXXXXXXX\",\r\n        },\r\n        relation: {\r\n          label: \"Lien avec le demandeur\",\r\n          placeholder: \"Ex: Sœur, Parent, Ami(e)\",\r\n        },\r\n      },\r\n    },\r\n    landStatus: {\r\n      title: \"Statut foncier\",\r\n      intro: \"Veuillez préciser votre statut par rapport au terrain concerné par la procédure.\",\r\n      label: \"Votre statut\",\r\n      options: {\r\n        owner: \"Propriétaire\",\r\n        heir: \"Héritier\",\r\n        buyer: \"Acheteur\",\r\n        applicant: \"Demandeur d'attribution\",\r\n        other: \"Autre\",\r\n      },\r\n      otherLabel: \"Précision du statut\",\r\n      otherPlaceholder: \"Veuillez préciser votre statut\",\r\n    },\r\n    procedureType: {\r\n      title: \"Type de procédure\",\r\n      intro: \"Veuillez indiquer le type de procédure foncière que vous souhaitez entreprendre.\",\r\n      label: \"Type de procédure souhaitée\",\r\n      help: \"Cette sélection déterminera les documents et procédures nécessaires pour votre dossier\",\r\n      options: [\r\n        \"Dérogation ministérielle spéciale\",\r\n        \"Immatriculation directe\",\r\n        \"Immatriculation par voie de concession\",\r\n        \"Décision de gré à gré\",\r\n        \"Protocole d'accord de bonne volonté\",\r\n        \"Morcellement\",\r\n        \"Mutation totale\",\r\n        \"Mutation par décès\",\r\n        \"Protocole d'achat d'un terrain non titré\",\r\n        \"Dossier technique\",\r\n        \"Indemnisation\",\r\n        \"Rétrocession\",\r\n        \"Recours gracieux\",\r\n        \"Réhabilitation de titre foncier\",\r\n        \"Autre\"\r\n      ],\r\n      otherLabel: \"Précisions sur la procédure\",\r\n      otherPlaceholder: \"Veuillez préciser la nature exacte de votre demande\",\r\n      otherHelp: \"Donnez le maximum de détails pour nous permettre de préparer votre dossier\",\r\n    },\r\n    additionalInfo: {\r\n      label: \"Informations complémentaires\",\r\n      placeholder: \"Saisissez ici toute information complémentaire concernant votre demande\",\r\n      help: \"Facultatif : tout contexte ou détail pouvant nous aider à mieux comprendre votre situation\",\r\n    },\r\n  },\r\n\r\n  stepSummary: {\r\n    title: \"Récapitulatif et soumission\",\r\n    intro: \"Veuillez vérifier les informations saisies avant de soumettre votre demande.\",\r\n    sections: {\r\n      personal: {\r\n        title: \"Informations personnelles\",\r\n      },\r\n      emergency: {\r\n        title: \"Contacts d'urgence\",\r\n      },\r\n      procedure: {\r\n        title: \"Procédure\",\r\n      },\r\n      documents: {\r\n        title: \"Documents fournis\",\r\n      },\r\n      location: {\r\n        title: \"Localisation du terrain\",\r\n      },\r\n      editButton: \"Modifier cette section\",\r\n    },\r\n    additionalComments: {\r\n      label: \"Commentaires additionnels\",\r\n      placeholder: \"Ajoutez ici toute information complémentaire concernant votre demande\",\r\n      help: \"Facultatif : précisez toute information qui pourrait nous aider à mieux traiter votre demande\",\r\n    },\r\n    finalConsent: {\r\n      dataProtection: \"En soumettant ce formulaire, vous acceptez que les informations fournies soient traitées par Charlie Oscar Consulting SARL dans le strict cadre de votre demande foncière, conformément à la législation camerounaise sur la protection des données personnelles.\",\r\n      label: \"Je confirme l'exactitude des informations fournies et accepte de soumettre ma demande\",\r\n    },\r\n    submit: {\r\n      button: \"Soumettre ma demande\",\r\n      processing: \"Traitement en cours...\",\r\n    },\r\n    confirmation: {\r\n      title: \"Demande soumise avec succès\",\r\n      message: \"Votre demande a été enregistrée. Un conseiller de Charlie Oscar Consulting vous contactera dans les plus brefs délais.\",\r\n      referenceLabel: \"Référence de votre demande\",\r\n      referenceHelp: \"Veuillez conserver cette référence pour toute communication future concernant votre dossier.\",\r\n      downloadButton: \"Télécharger le récapitulatif\",\r\n      newFormButton: \"Commencer un nouveau formulaire\",\r\n    },\r\n    personalInfo: {\r\n      title: \"Informations personnelles\",\r\n      name: \"Nom & prénoms\",\r\n      gender: \"Sexe\",\r\n      birthDate: \"Date de naissance\",\r\n      birthPlace: \"Lieu de naissance\",\r\n      nationality: \"Nationalité\",\r\n      profession: \"Profession\",\r\n    },\r\n    contactInfo: {\r\n      title: \"Informations de contact\",\r\n      phone: \"Téléphone\",\r\n      email: \"Email\",\r\n      address: \"Adresse\",\r\n    },\r\n    locationInfo: {\r\n      title: \"Localisation du terrain\",\r\n      region: \"Région\",\r\n      department: \"Département\",\r\n      subdivision: \"Arrondissement\",\r\n      neighborhood: \"Quartier/Village\",\r\n      area: \"Superficie\",\r\n    },\r\n    procedureInfo: {\r\n      title: \"Procédure\",\r\n      status: \"Statut\",\r\n      type: \"Type de procédure\",\r\n    },\r\n    documents: {\r\n      title: \"Documents fournis\",\r\n      none: \"Aucun document fourni\",\r\n    },\r\n  },\r\n\r\n  uploadPage: {\r\n    title: \"Téléverser des documents\",\r\n    description: \"Téléversez votre formulaire complété et les documents justificatifs pour votre demande foncière.\",\r\n    mainFile: \"Document principal\",\r\n    mainFileDescription: \"Téléversez votre formulaire complété ou document principal (PDF uniquement).\",\r\n    mainFileRequired: \"Veuillez téléverser votre document principal avant de soumettre.\",\r\n    relatedFiles: \"Documents justificatifs\",\r\n    relatedFilesDescription: \"Téléversez tous les documents justificatifs liés à votre demande.\",\r\n    addFiles: \"Ajouter des fichiers\",\r\n    dragOrClick: \"Glissez-déposez les fichiers ici ou cliquez pour parcourir\",\r\n    uploadedFiles: \"Fichiers téléversés\",\r\n    uploadSuccess: \"Vos documents ont été téléversés avec succès. Un conseiller de Charlie Oscar Consulting vous contacteras tres prochainement.\",\r\n    uploadSuccessWithRef: \"Vos documents ont été téléversés avec succès avec le numéro de référence : {ref}. Un conseiller de Charlie Oscar Consulting vous contacteras tres prochainement.\",\r\n    dropMainFile: \"Déposez votre document principal ici ou cliquez pour parcourir\",\r\n    selectFile: \"Sélectionnez un fichier à téléverser\",\r\n    fileName: \"Nom/Description du fichier\",\r\n    fileNamePlaceholder: \"Entrez un nom ou une description pour ce fichier\",\r\n    pdfPreview: \"Aperçu PDF\",\r\n    confirmWithErrors: \"Certains fichiers présentent des erreurs de validation. Voulez-vous continuer sans eux ?\",\r\n    validationErrors: \"Certains fichiers présentent des erreurs de validation et seront exclus de la soumission.\",\r\n    mainFileValidationError: \"Le document principal présente des erreurs de validation. Veuillez les corriger avant de soumettre.\",\r\n  },\r\n};\r\n\r\nexport default fr;\r\n"], "names": [], "mappings": "AAAA,sBAAsB;;;;AACtB,MAAM,KAAK;IACT,MAAK;IACL,QAAQ;QACN,MAAM;QACN,UAAU;QACV,QAAQ;QACR,YAAY;QACZ,UAAU;QACV,UAAU;QACV,UAAU;QACV,aAAa;QACb,cAAc;QACd,aAAa;QACb,aAAa;QACb,cAAc;QACd,WAAW;QACX,UAAU;QACV,cAAc;QACd,OAAO;QACP,6CAA6C;QAC7C,SAAQ;QACR,QAAQ;QACR,SAAS;QACT,gBAAgB;QAChB,oBAAoB;QACpB,mBAAkB;QAClB,gBAAgB;QAChB,MAAM;QACN,SAAS;QACT,WAAW;QACX,eAAe;QACf,aAAa;QACb,cAAc;QACd,iBAAiB;QACjB,kBAAkB;QAClB,QAAQ;IACV;IAEA,YAAY;QACV,UAAU;QACV,WAAW;QACX,OAAO;QACP,OAAO;QACP,SAAS;QACT,MAAM;QACN,WAAW;QACX,cAAc;QACd,UAAU;QACV,UAAU;IACZ;IAEA,SAAS;QACP,OAAO;QACP,UAAU;QACV,MAAM;QACN,IAAI;QACJ,aAAa;IACf;IAEA,OAAO;QACL,UAAU;YACR,MAAM;YACN,aAAa;QACf;QACA,WAAW;YACT,MAAM;YACN,aAAa;QACf;QACA,WAAW;YACT,MAAM;YACN,aAAa;QACf;QACA,SAAS;YACP,MAAM;YACN,aAAa;QACf;IACF;IAEA,cAAc;QACZ,OAAO;QACP,OAAO;QACP,gBAAgB;YACd,OAAO;YACP,aAAa;YACb,SAAS;QACX;QACA,cAAc;YACZ,OAAO;YACP,UAAU;gBACR,OAAO;gBACP,aAAa;YACf;YACA,QAAQ;gBACN,OAAO;gBACP,MAAM;gBACN,QAAQ;YACV;YACA,WAAW;gBACT,OAAO;YACT;YACA,YAAY;gBACV,OAAO;gBACP,aAAa;YACf;YACA,aAAa;gBACX,OAAO;gBACP,aAAa;gBACb,SAAS;oBACP,UAAU;oBACV,QAAQ;oBACR,SAAS;oBACT,YAAY;oBACZ,OAAO;oBACP,OAAO;oBACP,OAAO;gBACT;YACF;YACA,YAAY;gBACV,OAAO;gBACP,aAAa;YACf;QACF;QACA,aAAa;YACX,OAAO;YACP,cAAc;gBACZ,OAAO;gBACP,aAAa;YACf;YACA,gBAAgB;gBACd,OAAO;gBACP,aAAa;YACf;YACA,OAAO;gBACL,OAAO;gBACP,aAAa;YACf;YACA,SAAS;gBACP,OAAO;gBACP,aAAa;YACf;YACA,YAAY;gBACV,OAAO;gBACP,aAAa;gBACb,aAAa;gBACb,cAAc;YAChB;QACF;IACF;IAEA,uBAAuB;QACrB,OAAO;QACP,WAAW;YACT,OAAO;YACP,OAAO;YACP,eAAe;gBACb,OAAO;gBACP,MAAM;gBACN,SAAS;oBACP,mBAAmB;oBACnB,SAAS;oBACT,kBAAkB;oBAClB,cAAc;oBACd,WAAW;oBACX,WAAW;gBACb;YACF;YACA,QAAQ;gBACN,OAAO;gBACP,eAAe;gBACf,aAAa;YACf;YACA,SAAS;gBACP,OAAO;gBACP,aAAa;gBACb,MAAM;YACR;QACF;QACA,UAAU;YACR,OAAO;YACP,OAAO;YACP,UAAU;gBACR,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,MAAM;YACR;YACA,QAAQ;gBACN,OAAO;gBACP,aAAa;gBACb,SAAS;oBACP;oBAAU;oBAAY;oBAAO;oBAAO;oBACpC;oBAAc;oBAAa;oBAAQ;oBAAgB;iBACpD;YACH;YACA,YAAY;gBACV,OAAO;gBACP,aAAa;gBACb,SAAS;oBACP,QAAQ;wBAAC;wBAAW;wBAAkB;wBAAiB;wBAAS;qBAAkB;oBAClF,UAAU;wBAAC;wBAAS;wBAAmB;wBAAQ;qBAAS;oBACxD,OAAO;wBAAC;wBAAS;wBAAkB;wBAAS;qBAAc;oBAC1D,MAAM;wBAAC;wBAAc;wBAAS;wBAAiB;qBAAkB;oBACjE,MAAM;wBAAC;wBAAQ;wBAAU;wBAAa;wBAAa;wBAAO;qBAAY;gBACxE;YACF;YACA,aAAa;gBACX,OAAO;gBACP,aAAa;gBACb,MAAM;YACR;YACA,cAAc;gBACZ,OAAO;gBACP,aAAa;YACf;YACA,iBAAiB;gBACf,OAAO;gBACP,aAAa;gBACb,MAAM;YACR;YACA,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,MAAM;gBACN,MAAM;YACR;QACF;IACF;IAEA,wBAAwB;QACtB,OAAO;QACP,mBAAmB;YACjB,OAAO;YACP,OAAO;YACP,UAAU;gBACR,OAAO;gBACP,MAAM;oBACJ,OAAO;oBACP,aAAa;gBACf;gBACA,OAAO;oBACL,OAAO;oBACP,aAAa;gBACf;gBACA,UAAU;oBACR,OAAO;oBACP,aAAa;oBACb,MAAM;gBACR;YACF;YACA,UAAU;gBACR,OAAO;gBACP,MAAM;oBACJ,OAAO;oBACP,aAAa;gBACf;gBACA,OAAO;oBACL,OAAO;oBACP,aAAa;gBACf;gBACA,UAAU;oBACR,OAAO;oBACP,aAAa;gBACf;YACF;QACF;QACA,YAAY;YACV,OAAO;YACP,OAAO;YACP,OAAO;YACP,SAAS;gBACP,OAAO;gBACP,MAAM;gBACN,OAAO;gBACP,WAAW;gBACX,OAAO;YACT;YACA,YAAY;YACZ,kBAAkB;QACpB;QACA,eAAe;YACb,OAAO;YACP,OAAO;YACP,OAAO;YACP,MAAM;YACN,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;YACZ,kBAAkB;YAClB,WAAW;QACb;QACA,gBAAgB;YACd,OAAO;YACP,aAAa;YACb,MAAM;QACR;IACF;IAEA,aAAa;QACX,OAAO;QACP,OAAO;QACP,UAAU;YACR,UAAU;gBACR,OAAO;YACT;YACA,WAAW;gBACT,OAAO;YACT;YACA,WAAW;gBACT,OAAO;YACT;YACA,WAAW;gBACT,OAAO;YACT;YACA,UAAU;gBACR,OAAO;YACT;YACA,YAAY;QACd;QACA,oBAAoB;YAClB,OAAO;YACP,aAAa;YACb,MAAM;QACR;QACA,cAAc;YACZ,gBAAgB;YAChB,OAAO;QACT;QACA,QAAQ;YACN,QAAQ;YACR,YAAY;QACd;QACA,cAAc;YACZ,OAAO;YACP,SAAS;YACT,gBAAgB;YAChB,eAAe;YACf,gBAAgB;YAChB,eAAe;QACjB;QACA,cAAc;YACZ,OAAO;YACP,MAAM;YACN,QAAQ;YACR,WAAW;YACX,YAAY;YACZ,aAAa;YACb,YAAY;QACd;QACA,aAAa;YACX,OAAO;YACP,OAAO;YACP,OAAO;YACP,SAAS;QACX;QACA,cAAc;YACZ,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,aAAa;YACb,cAAc;YACd,MAAM;QACR;QACA,eAAe;YACb,OAAO;YACP,QAAQ;YACR,MAAM;QACR;QACA,WAAW;YACT,OAAO;YACP,MAAM;QACR;IACF;IAEA,YAAY;QACV,OAAO;QACP,aAAa;QACb,UAAU;QACV,qBAAqB;QACrB,kBAAkB;QAClB,cAAc;QACd,yBAAyB;QACzB,UAAU;QACV,aAAa;QACb,eAAe;QACf,eAAe;QACf,sBAAsB;QACtB,cAAc;QACd,YAAY;QACZ,UAAU;QACV,qBAAqB;QACrB,YAAY;QACZ,mBAAmB;QACnB,kBAAkB;QAClB,yBAAyB;IAC3B;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 460, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/charlie-oscar-consulting/website/app/translations/en.ts"], "sourcesContent": ["// English translations\r\nconst en = {\r\n  fill:\"Thank you for carefully filling out this form. An expert from <PERSON> will contact you as soon as possible to assist you with your project.\",\r\n  common: {\r\n    next: \"Continue\",\r\n    previous: \"Previous\",\r\n    submit: \"Submit my request\",\r\n    processing: \"Processing...\",\r\n    required: \"*\",\r\n    optional: \"Optional\",\r\n    fileSize: \"Maximum size is 2 MB\",\r\n    filePreview: \"Preview:\",\r\n    fileSelected: \"File selected:\",\r\n    fileFormats: \".pdf, .jpg, .png, max 2 MB per file\",\r\n    notProvided: \"Not provided\",\r\n    notSpecified: \"Not specified\",\r\n    uploading: \"Uploading...\",\r\n    uploaded: \"Uploaded\",\r\n    uploadFailed: \"Upload failed\",\r\n    retry: \"Retry\",\r\n    success: \"Your request has been submitted successfully. A <PERSON> Consulting advisor will contact you as soon as possible.\",\r\n    online: \"Online\",\r\n    offline: \"Offline\",\r\n    connectionLost: \"Internet connection lost. Some features may be unavailable.\",\r\n    connectionRestored: \"Internet connection restored.\",\r\n    fileUploadOffline:\"You cannot upload files when you are offline.\",\r\n    waitForUploads: \"Please wait for uploads to complete...\",\r\n    form: \"Form\",\r\n    apiDocs: \"API Docs\",\r\n    testTools: \"Test Tools\",\r\n    dropFilesHere: \"Drop your files here or click to browse\",\r\n    maxFileSize: \"Max file size: 2 MB\",\r\n    fileTooLarge: \"File is too large. Maximum size is 2 MB\",\r\n    invalidFileType: \"File type not supported\",\r\n    fileNameTooShort: \"File name must be at least 3 characters\",\r\n    cancel: \"Cancel\",\r\n  },\r\n\r\n  validation: {\r\n    required: \"This field is required\",\r\n    minLength: \"Must be at least {min} characters\",\r\n    email: \"Please enter a valid email address\",\r\n    phone: \"Please enter a valid phone number\",\r\n    consent: \"You must consent to proceed\",\r\n    date: \"Please enter a valid date\",\r\n    selection: \"Please make a selection\",\r\n    fileRequired: \"Please upload a file\",\r\n    fileSize: \"File size must be less than {size}\",\r\n    fileType: \"File type not supported\",\r\n  },\r\n\r\n  sidebar: {\r\n    title: \"Charlie Oscar\",\r\n    subtitle: \"Consulting\",\r\n    step: \"Step\",\r\n    of: \"/\",\r\n    currentStep: \"Current step\",\r\n  },\r\n\r\n  steps: {\r\n    personal: {\r\n      name: \"Personal Information\",\r\n      description: \"Enter your personal information to help us get to know you better.\",\r\n    },\r\n    emergency: {\r\n      name: \"Emergency Contacts and Procedure\",\r\n      description: \"Provide your emergency contacts and desired procedure.\",\r\n    },\r\n    documents: {\r\n      name: \"Documents and Land Location\",\r\n      description: \"Provide documents and information about the land location.\",\r\n    },\r\n    summary: {\r\n      name: \"Summary and Confirmation\",\r\n      description: \"Verify all information before submitting your request.\",\r\n    },\r\n  },\r\n\r\n  stepPersonal: {\r\n    title: \"Your Personal Information\",\r\n    intro: \"Please fill in all required fields marked with an asterisk (*) accurately.\",\r\n    dataProtection: {\r\n      title: \"Data Protection\",\r\n      description: \"The information collected is strictly confidential and used only for processing your land request by Charlie Oscar Consulting SARL.\",\r\n      consent: \"I agree that my information will be processed by Charlie Oscar Consulting SARL\",\r\n    },\r\n    personalInfo: {\r\n      title: \"Personal Information\",\r\n      fullName: {\r\n        label: \"Full Name\",\r\n        placeholder: \"Ex: MBARGA Paul Serge\",\r\n      },\r\n      gender: {\r\n        label: \"Gender\",\r\n        male: \"Male\",\r\n        female: \"Female\",\r\n      },\r\n      birthDate: {\r\n        label: \"Date of Birth\",\r\n      },\r\n      birthPlace: {\r\n        label: \"Place of Birth\",\r\n        placeholder: \"Ex: Yaoundé, Cameroon\",\r\n      },\r\n      nationality: {\r\n        label: \"Nationality\",\r\n        placeholder: \"Select your nationality\",\r\n        options: {\r\n          cameroon: \"Cameroonian\",\r\n          france: \"French\",\r\n          senegal: \"Senegalese\",\r\n          ivoryCoast: \"Ivorian\",\r\n          gabon: \"Gabonese\",\r\n          congo: \"Congolese\",\r\n          other: \"Other\",\r\n        },\r\n      },\r\n      profession: {\r\n        label: \"Profession\",\r\n        placeholder: \"Ex: Teacher, Merchant\",\r\n      },\r\n    },\r\n    contactInfo: {\r\n      title: \"Contact Information\",\r\n      primaryPhone: {\r\n        label: \"Primary Phone\",\r\n        placeholder: \"Ex: +237 6XXXXXXXX\",\r\n      },\r\n      secondaryPhone: {\r\n        label: \"Secondary Phone\",\r\n        placeholder: \"Ex: +237 6XXXXXXXX\",\r\n      },\r\n      email: {\r\n        label: \"Email\",\r\n        placeholder: \"<EMAIL>\",\r\n      },\r\n      address: {\r\n        label: \"Current Address\",\r\n        placeholder: \"Ex: Bastos District, Street 1.890, Yaoundé\",\r\n      },\r\n      idDocument: {\r\n        label: \"ID Document\",\r\n        description: \"Upload a clear and legible copy of your ID card or passport\",\r\n        notUploaded: \"No document uploaded\",\r\n        nameRequired: \"Please enter your full name first\",\r\n      },\r\n    },\r\n  },\r\n\r\n  stepDocumentsLocation: {\r\n    title: \"Documents and Land Location\",\r\n    documents: {\r\n      title: \"Documents\",\r\n      intro: \"Please indicate and upload the documents you already have regarding this procedure. The location plan is highly recommended if available.\",\r\n      availableDocs: {\r\n        label: \"Available Documents\",\r\n        help: \"Check all documents you can provide\",\r\n        options: {\r\n          customaryProperty: \"Customary Property Certificate\",\r\n          saleAct: \"Sale/Donation Deed\",\r\n          occupationPermit: \"Occupation Permit\",\r\n          locationPlan: \"Location Plan / Land Sketch\",\r\n          landTitle: \"Land Title\",\r\n          otherDocs: \"Other Documents\",\r\n        },\r\n      },\r\n      upload: {\r\n        label: \"Document Upload\",\r\n        documentLabel: \"Document\",\r\n        description: \"Upload your most relevant documents for your request once at the time. You can provide additional documents later.\",\r\n      },\r\n      details: {\r\n        label: \"Document Details\",\r\n        placeholder: \"Ex: The land title dates from 2015, the notarized deed of sale is attached...\",\r\n        help: \"Optional: specify the nature or status of the documents provided\",\r\n      },\r\n    },\r\n    location: {\r\n      title: \"Location\",\r\n      intro: \"Please provide precise information regarding the location of the land concerned by the procedure.\",\r\n      zoneType: {\r\n        label: \"Zone Type\",\r\n        urban: \"Urban\",\r\n        rural: \"Rural\",\r\n        help: \"Select the type of environment where the land is located\",\r\n      },\r\n      region: {\r\n        label: \"Region\",\r\n        placeholder: \"Select a region\",\r\n        options: [\r\n          \"Centre\", \"Littoral\", \"South\", \"East\", \"West\",\r\n          \"North-West\", \"South-West\", \"North\", \"Far North\", \"Adamawa\"\r\n        ],\r\n      },\r\n      department: {\r\n        label: \"Department\",\r\n        placeholder: \"Select or specify a department\",\r\n        options: {\r\n          centre: [\"Mfoundi\", \"Nyong-et-Kellé\", \"Nyong-et-So'o\", \"Lekié\", \"Mbam-et-Inoubou\"],\r\n          littoral: [\"Wouri\", \"Sanaga-Maritime\", \"Nkam\", \"Moungo\"],\r\n          south: [\"Océan\", \"Vallée-du-Ntem\", \"Mvila\", \"Dja-et-Lobo\"],\r\n          east: [\"Haut-Nyong\", \"Kadey\", \"Lom-et-Djérem\", \"Boumba-et-Ngoko\"],\r\n          west: [\"Mifi\", \"Menoua\", \"Bamboutos\", \"Haut-Nkam\", \"Ndé\", \"Koung-Khi\"],\r\n        },\r\n      },\r\n      subdivision: {\r\n        label: \"Subdivision\",\r\n        placeholder: \"Ex: Yaoundé II, Douala III, Bafoussam I\",\r\n        help: \"Specify the subdivision where the land is located\",\r\n      },\r\n      neighborhood: {\r\n        label: \"Neighborhood or Village\",\r\n        placeholder: \"Ex: Mokolo, Bonapriso, Banengo...\",\r\n      },\r\n      locationDetails: {\r\n        label: \"Locality\",\r\n        placeholder: \"Ex: Near Saint-Jean Church, behind the central market...\",\r\n        help: \"Specific geographical landmark facilitating the location of the land\",\r\n      },\r\n      area: {\r\n        label: \"Estimated Area\",\r\n        placeholder: \"Ex: 500\",\r\n        unit: \"m²\",\r\n        help: \"Approximate land area in square meters\",\r\n      },\r\n    },\r\n  },\r\n\r\n  stepEmergencyProcedure: {\r\n    title: \"Procedure and Urgency\",\r\n    emergencyContacts: {\r\n      title: \"Emergency Contacts\",\r\n      intro: \"Please indicate at least one person to contact in case of unavailability.\",\r\n      contact1: {\r\n        title: \"Person 1 (required)\",\r\n        name: {\r\n          label: \"Full Name\",\r\n          placeholder: \"Ex: ATANGANA Marie Claire\",\r\n        },\r\n        phone: {\r\n          label: \"Phone\",\r\n          placeholder: \"Ex: +237 6XXXXXXXX\",\r\n        },\r\n        relation: {\r\n          label: \"Relationship to applicant\",\r\n          placeholder: \"Ex: Brother, Spouse, Colleague\",\r\n          help: \"Specify your relationship with this person\",\r\n        },\r\n      },\r\n      contact2: {\r\n        title: \"Person 2 (optional)\",\r\n        name: {\r\n          label: \"Full Name\",\r\n          placeholder: \"Ex: ESSOMBA Jean\",\r\n        },\r\n        phone: {\r\n          label: \"Phone\",\r\n          placeholder: \"Ex: +237 6XXXXXXXX\",\r\n        },\r\n        relation: {\r\n          label: \"Relationship to applicant\",\r\n          placeholder: \"Ex: Sister, Parent, Friend\",\r\n        },\r\n      },\r\n    },\r\n    landStatus: {\r\n      title: \"Land Status\",\r\n      intro: \"Please specify your status in relation to the land concerned by the procedure.\",\r\n      label: \"Your Status\",\r\n      options: {\r\n        owner: \"Owner\",\r\n        heir: \"Heir\",\r\n        buyer: \"Buyer\",\r\n        applicant: \"Allocation Applicant\",\r\n        other: \"Other\",\r\n      },\r\n      otherLabel: \"Status Details\",\r\n      otherPlaceholder: \"Please specify your status\",\r\n    },\r\n    procedureType: {\r\n      title: \"Procedure Type\",\r\n      intro: \"Please indicate the type of land procedure you wish to undertake.\",\r\n      label: \"Desired Procedure Type\",\r\n      help: \"This selection will determine the documents and procedures needed for your file\",\r\n      options: [\r\n        \"Special Ministerial Derogation\",\r\n        \"Direct Registration\",\r\n        \"Registration by Concession\",\r\n        \"Amicable Decision\",\r\n        \"Goodwill Protocol Agreement\",\r\n        \"Land Division\",\r\n        \"Total Transfer\",\r\n        \"Transfer by Death\",\r\n        \"Protocol for Purchasing Unregistered Land\",\r\n        \"Technical File\",\r\n        \"Compensation\",\r\n        \"Retrocession\",\r\n        \"Administrative Appeal\",\r\n        \"Land Title Rehabilitation\",\r\n        \"Other\"\r\n      ],\r\n      otherLabel: \"Procedure Details\",\r\n      otherPlaceholder: \"Please specify the exact nature of your request\",\r\n      otherHelp: \"Provide as much detail as possible to help us prepare your file\",\r\n    },\r\n    additionalInfo: {\r\n      label: \"Additional Information\",\r\n      placeholder: \"Enter any additional information regarding your request here\",\r\n      help: \"Optional: any context or details that can help us better understand your situation\",\r\n    },\r\n  },\r\n\r\n  stepSummary: {\r\n    title: \"Summary and Submission\",\r\n    intro: \"Please verify the information entered before submitting your request.\",\r\n    sections: {\r\n      personal: {\r\n        title: \"Personal Information\",\r\n      },\r\n      emergency: {\r\n        title: \"Emergency Contacts\",\r\n      },\r\n      procedure: {\r\n        title: \"Procedure\",\r\n      },\r\n      documents: {\r\n        title: \"Documents Provided\",\r\n      },\r\n      location: {\r\n        title: \"Land Location\",\r\n      },\r\n      editButton: \"Edit this section\",\r\n    },\r\n    additionalComments: {\r\n      label: \"Additional Comments\",\r\n      placeholder: \"Add any additional information regarding your request here\",\r\n      help: \"Optional: provide any information that might help us better process your request\",\r\n    },\r\n    finalConsent: {\r\n      dataProtection: \"By submitting this form, you agree that the information provided will be processed by Charlie Oscar Consulting SARL strictly for your land request, in accordance with Cameroonian legislation on personal data protection.\",\r\n      label: \"I confirm the accuracy of the information provided and agree to submit my request\",\r\n    },\r\n    submit: {\r\n      button: \"Submit my request\",\r\n      processing: \"Processing...\",\r\n    },\r\n    confirmation: {\r\n      title: \"Request Successfully Submitted\",\r\n      message: \"Your request has been registered. A Charlie Oscar Consulting advisor will contact you as soon as possible.\",\r\n      referenceLabel: \"Your request reference\",\r\n      referenceHelp: \"Please keep this reference for any future communication regarding your file.\",\r\n      downloadButton: \"Download summary\",\r\n      newFormButton: \"Start a new form\",\r\n    },\r\n    personalInfo: {\r\n      title: \"Personal Information\",\r\n      name: \"Full Name\",\r\n      gender: \"Gender\",\r\n      birthDate: \"Date of Birth\",\r\n      birthPlace: \"Place of Birth\",\r\n      nationality: \"Nationality\",\r\n      profession: \"Profession\",\r\n    },\r\n    contactInfo: {\r\n      title: \"Contact Information\",\r\n      phone: \"Phone\",\r\n      email: \"Email\",\r\n      address: \"Address\",\r\n    },\r\n    locationInfo: {\r\n      title: \"Land Location\",\r\n      region: \"Region\",\r\n      department: \"Department\",\r\n      subdivision: \"Subdivision\",\r\n      neighborhood: \"Neighborhood/Village\",\r\n      area: \"Area\",\r\n    },\r\n    procedureInfo: {\r\n      title: \"Procedure\",\r\n      status: \"Status\",\r\n      type: \"Procedure Type\",\r\n    },\r\n    documents: {\r\n      title: \"Documents Provided\",\r\n      none: \"No documents provided\",\r\n    },\r\n  },\r\n\r\n  uploadPage: {\r\n    title: \"Upload Documents\",\r\n    description: \"Upload your completed form and supporting documents for your land request.\",\r\n    mainFile: \"Main Document\",\r\n    mainFileDescription: \"Upload your completed form or main document (PDF only).\",\r\n    mainFileRequired: \"Please upload your main document before submitting.\",\r\n    relatedFiles: \"Supporting Documents\",\r\n    relatedFilesDescription: \"Upload any supporting documents related to your request.\",\r\n    addFiles: \"Add Files\",\r\n    dragOrClick: \"Drag and drop files here or click to browse\",\r\n    uploadedFiles: \"Uploaded Files\",\r\n    uploadSuccess: \"Your documents have been successfully uploaded. A Charlie Oscar Consulting advisor will contact you very soon.\",\r\n    uploadSuccessWithRef: \"Your documents have been successfully uploaded with reference number: {ref}. A Charlie Oscar Consulting advisor will contact you very soon.\",\r\n    dropMainFile: \"Drop your main document here or click to browse\",\r\n    selectFile: \"Select a file to upload\",\r\n    fileName: \"File Name/Description\",\r\n    fileNamePlaceholder: \"Enter a name or description for this file\",\r\n    pdfPreview: \"PDF Preview\",\r\n    confirmWithErrors: \"Some files have validation errors. Do you want to continue without them?\",\r\n    validationErrors: \"Some files have validation errors and will be excluded from submission.\",\r\n    mainFileValidationError: \"The main document has validation errors. Please fix them before submitting.\",\r\n  },\r\n};\r\n\r\nexport default en;\r\n"], "names": [], "mappings": "AAAA,uBAAuB;;;;AACvB,MAAM,KAAK;IACT,MAAK;IACL,QAAQ;QACN,MAAM;QACN,UAAU;QACV,QAAQ;QACR,YAAY;QACZ,UAAU;QACV,UAAU;QACV,UAAU;QACV,aAAa;QACb,cAAc;QACd,aAAa;QACb,aAAa;QACb,cAAc;QACd,WAAW;QACX,UAAU;QACV,cAAc;QACd,OAAO;QACP,SAAS;QACT,QAAQ;QACR,SAAS;QACT,gBAAgB;QAChB,oBAAoB;QACpB,mBAAkB;QAClB,gBAAgB;QAChB,MAAM;QACN,SAAS;QACT,WAAW;QACX,eAAe;QACf,aAAa;QACb,cAAc;QACd,iBAAiB;QACjB,kBAAkB;QAClB,QAAQ;IACV;IAEA,YAAY;QACV,UAAU;QACV,WAAW;QACX,OAAO;QACP,OAAO;QACP,SAAS;QACT,MAAM;QACN,WAAW;QACX,cAAc;QACd,UAAU;QACV,UAAU;IACZ;IAEA,SAAS;QACP,OAAO;QACP,UAAU;QACV,MAAM;QACN,IAAI;QACJ,aAAa;IACf;IAEA,OAAO;QACL,UAAU;YACR,MAAM;YACN,aAAa;QACf;QACA,WAAW;YACT,MAAM;YACN,aAAa;QACf;QACA,WAAW;YACT,MAAM;YACN,aAAa;QACf;QACA,SAAS;YACP,MAAM;YACN,aAAa;QACf;IACF;IAEA,cAAc;QACZ,OAAO;QACP,OAAO;QACP,gBAAgB;YACd,OAAO;YACP,aAAa;YACb,SAAS;QACX;QACA,cAAc;YACZ,OAAO;YACP,UAAU;gBACR,OAAO;gBACP,aAAa;YACf;YACA,QAAQ;gBACN,OAAO;gBACP,MAAM;gBACN,QAAQ;YACV;YACA,WAAW;gBACT,OAAO;YACT;YACA,YAAY;gBACV,OAAO;gBACP,aAAa;YACf;YACA,aAAa;gBACX,OAAO;gBACP,aAAa;gBACb,SAAS;oBACP,UAAU;oBACV,QAAQ;oBACR,SAAS;oBACT,YAAY;oBACZ,OAAO;oBACP,OAAO;oBACP,OAAO;gBACT;YACF;YACA,YAAY;gBACV,OAAO;gBACP,aAAa;YACf;QACF;QACA,aAAa;YACX,OAAO;YACP,cAAc;gBACZ,OAAO;gBACP,aAAa;YACf;YACA,gBAAgB;gBACd,OAAO;gBACP,aAAa;YACf;YACA,OAAO;gBACL,OAAO;gBACP,aAAa;YACf;YACA,SAAS;gBACP,OAAO;gBACP,aAAa;YACf;YACA,YAAY;gBACV,OAAO;gBACP,aAAa;gBACb,aAAa;gBACb,cAAc;YAChB;QACF;IACF;IAEA,uBAAuB;QACrB,OAAO;QACP,WAAW;YACT,OAAO;YACP,OAAO;YACP,eAAe;gBACb,OAAO;gBACP,MAAM;gBACN,SAAS;oBACP,mBAAmB;oBACnB,SAAS;oBACT,kBAAkB;oBAClB,cAAc;oBACd,WAAW;oBACX,WAAW;gBACb;YACF;YACA,QAAQ;gBACN,OAAO;gBACP,eAAe;gBACf,aAAa;YACf;YACA,SAAS;gBACP,OAAO;gBACP,aAAa;gBACb,MAAM;YACR;QACF;QACA,UAAU;YACR,OAAO;YACP,OAAO;YACP,UAAU;gBACR,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,MAAM;YACR;YACA,QAAQ;gBACN,OAAO;gBACP,aAAa;gBACb,SAAS;oBACP;oBAAU;oBAAY;oBAAS;oBAAQ;oBACvC;oBAAc;oBAAc;oBAAS;oBAAa;iBACnD;YACH;YACA,YAAY;gBACV,OAAO;gBACP,aAAa;gBACb,SAAS;oBACP,QAAQ;wBAAC;wBAAW;wBAAkB;wBAAiB;wBAAS;qBAAkB;oBAClF,UAAU;wBAAC;wBAAS;wBAAmB;wBAAQ;qBAAS;oBACxD,OAAO;wBAAC;wBAAS;wBAAkB;wBAAS;qBAAc;oBAC1D,MAAM;wBAAC;wBAAc;wBAAS;wBAAiB;qBAAkB;oBACjE,MAAM;wBAAC;wBAAQ;wBAAU;wBAAa;wBAAa;wBAAO;qBAAY;gBACxE;YACF;YACA,aAAa;gBACX,OAAO;gBACP,aAAa;gBACb,MAAM;YACR;YACA,cAAc;gBACZ,OAAO;gBACP,aAAa;YACf;YACA,iBAAiB;gBACf,OAAO;gBACP,aAAa;gBACb,MAAM;YACR;YACA,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,MAAM;gBACN,MAAM;YACR;QACF;IACF;IAEA,wBAAwB;QACtB,OAAO;QACP,mBAAmB;YACjB,OAAO;YACP,OAAO;YACP,UAAU;gBACR,OAAO;gBACP,MAAM;oBACJ,OAAO;oBACP,aAAa;gBACf;gBACA,OAAO;oBACL,OAAO;oBACP,aAAa;gBACf;gBACA,UAAU;oBACR,OAAO;oBACP,aAAa;oBACb,MAAM;gBACR;YACF;YACA,UAAU;gBACR,OAAO;gBACP,MAAM;oBACJ,OAAO;oBACP,aAAa;gBACf;gBACA,OAAO;oBACL,OAAO;oBACP,aAAa;gBACf;gBACA,UAAU;oBACR,OAAO;oBACP,aAAa;gBACf;YACF;QACF;QACA,YAAY;YACV,OAAO;YACP,OAAO;YACP,OAAO;YACP,SAAS;gBACP,OAAO;gBACP,MAAM;gBACN,OAAO;gBACP,WAAW;gBACX,OAAO;YACT;YACA,YAAY;YACZ,kBAAkB;QACpB;QACA,eAAe;YACb,OAAO;YACP,OAAO;YACP,OAAO;YACP,MAAM;YACN,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;YACZ,kBAAkB;YAClB,WAAW;QACb;QACA,gBAAgB;YACd,OAAO;YACP,aAAa;YACb,MAAM;QACR;IACF;IAEA,aAAa;QACX,OAAO;QACP,OAAO;QACP,UAAU;YACR,UAAU;gBACR,OAAO;YACT;YACA,WAAW;gBACT,OAAO;YACT;YACA,WAAW;gBACT,OAAO;YACT;YACA,WAAW;gBACT,OAAO;YACT;YACA,UAAU;gBACR,OAAO;YACT;YACA,YAAY;QACd;QACA,oBAAoB;YAClB,OAAO;YACP,aAAa;YACb,MAAM;QACR;QACA,cAAc;YACZ,gBAAgB;YAChB,OAAO;QACT;QACA,QAAQ;YACN,QAAQ;YACR,YAAY;QACd;QACA,cAAc;YACZ,OAAO;YACP,SAAS;YACT,gBAAgB;YAChB,eAAe;YACf,gBAAgB;YAChB,eAAe;QACjB;QACA,cAAc;YACZ,OAAO;YACP,MAAM;YACN,QAAQ;YACR,WAAW;YACX,YAAY;YACZ,aAAa;YACb,YAAY;QACd;QACA,aAAa;YACX,OAAO;YACP,OAAO;YACP,OAAO;YACP,SAAS;QACX;QACA,cAAc;YACZ,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,aAAa;YACb,cAAc;YACd,MAAM;QACR;QACA,eAAe;YACb,OAAO;YACP,QAAQ;YACR,MAAM;QACR;QACA,WAAW;YACT,OAAO;YACP,MAAM;QACR;IACF;IAEA,YAAY;QACV,OAAO;QACP,aAAa;QACb,UAAU;QACV,qBAAqB;QACrB,kBAAkB;QAClB,cAAc;QACd,yBAAyB;QACzB,UAAU;QACV,aAAa;QACb,eAAe;QACf,eAAe;QACf,sBAAsB;QACtB,cAAc;QACd,YAAY;QACZ,UAAU;QACV,qBAAqB;QACrB,YAAY;QACZ,mBAAmB;QACnB,kBAAkB;QAClB,yBAAyB;IAC3B;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 912, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/charlie-oscar-consulting/website/app/translations/index.ts"], "sourcesContent": ["import fr from './fr';\r\nimport en from './en';\r\n\r\nexport const translations = {\r\n  fr,\r\n  en,\r\n};\r\n\r\nexport type Language = keyof typeof translations;\r\nexport type TranslationKeys = typeof fr;\r\n\r\nexport default translations;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,MAAM,eAAe;IAC1B,IAAA,4HAAA,CAAA,UAAE;IACF,IAAA,4HAAA,CAAA,UAAE;AACJ;uCAKe", "debugId": null}}, {"offset": {"line": 934, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/charlie-oscar-consulting/website/app/translations/language-context.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { createContext, useContext, useState, ReactNode } from 'react';\r\nimport { translations, Language } from './index';\r\n\r\ntype LanguageContextType = {\r\n  language: Language;\r\n  setLanguage: (lang: Language) => void;\r\n  t: (key: string) => string;\r\n};\r\n\r\nconst defaultLanguage: Language = 'fr';\r\n\r\nconst LanguageContext = createContext<LanguageContextType>({\r\n  language: defaultLanguage,\r\n  setLanguage: () => {},\r\n  t: () => '',\r\n});\r\n\r\nexport const useLanguage = () => useContext(LanguageContext);\r\n\r\nexport const LanguageProvider = ({ children }: { children: ReactNode }) => {\r\n  const [language, setLanguage] = useState<Language>(defaultLanguage);\r\n\r\n  // Function to get a translation by key (using dot notation)\r\n  const t = (key: string): string => {\r\n    const keys = key.split('.');\r\n    let value: any = translations[language];\r\n    \r\n    for (const k of keys) {\r\n      if (value === undefined) return key;\r\n      value = value[k];\r\n    }\r\n    \r\n    return value !== undefined ? value : key;\r\n  };\r\n\r\n  return (\r\n    <LanguageContext.Provider value={{ language, setLanguage, t }}>\r\n      {children}\r\n    </LanguageContext.Provider>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;;;AAHA;;;AAWA,MAAM,kBAA4B;AAElC,MAAM,gCAAkB,CAAA,GAAA,4RAAA,CAAA,gBAAa,AAAD,EAAuB;IACzD,UAAU;IACV,aAAa,KAAO;IACpB,GAAG,IAAM;AACX;AAEO,MAAM,cAAc;;IAAM,OAAA,CAAA,GAAA,4RAAA,CAAA,aAAU,AAAD,EAAE;AAAe;GAA9C;AAEN,MAAM,mBAAmB,CAAC,EAAE,QAAQ,EAA2B;;IACpE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAY;IAEnD,4DAA4D;IAC5D,MAAM,IAAI,CAAC;QACT,MAAM,OAAO,IAAI,KAAK,CAAC;QACvB,IAAI,QAAa,+HAAA,CAAA,eAAY,CAAC,SAAS;QAEvC,KAAK,MAAM,KAAK,KAAM;YACpB,IAAI,UAAU,WAAW,OAAO;YAChC,QAAQ,KAAK,CAAC,EAAE;QAClB;QAEA,OAAO,UAAU,YAAY,QAAQ;IACvC;IAEA,qBACE,4TAAC,gBAAgB,QAAQ;QAAC,OAAO;YAAE;YAAU;YAAa;QAAE;kBACzD;;;;;;AAGP;IArBa;KAAA", "debugId": null}}, {"offset": {"line": 996, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/charlie-oscar-consulting/website/app/contexts/online-status-context.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { createContext, useContext, useEffect, useState, ReactNode } from \"react\";\r\n\r\n// Define the context type\r\ninterface OnlineStatusContextType {\r\n  isOnline: boolean;\r\n}\r\n\r\n// Create the context with a default value\r\nconst OnlineStatusContext = createContext<OnlineStatusContextType>({ isOnline: true });\r\n\r\n// Provider component\r\nexport function OnlineStatusProvider({ children }: { children: ReactNode }) {\r\n  // Default to true (optimistic) and update once we're on the client\r\n  const [isOnline, setIsOnline] = useState<boolean>(true);\r\n\r\n  useEffect(() => {\r\n    // Update the online status initially\r\n    setIsOnline(navigator.onLine);\r\n\r\n    // Event handlers for online and offline events\r\n    const handleOnline = () => {\r\n      console.log('Network status: Online');\r\n      setIsOnline(true);\r\n    };\r\n\r\n    const handleOffline = () => {\r\n      console.log('Network status: Offline');\r\n      setIsOnline(false);\r\n    };\r\n\r\n    // Add event listeners\r\n    window.addEventListener('online', handleOnline);\r\n    window.addEventListener('offline', handleOffline);\r\n\r\n    // Clean up event listeners on unmount\r\n    return () => {\r\n      window.removeEventListener('online', handleOnline);\r\n      window.removeEventListener('offline', handleOffline);\r\n    };\r\n  }, []);\r\n\r\n  return (\r\n    <OnlineStatusContext.Provider value={{ isOnline }}>\r\n      {children}\r\n    </OnlineStatusContext.Provider>\r\n  );\r\n}\r\n\r\n// Custom hook to use the online status context\r\nexport function useOnlineStatus() {\r\n  const context = useContext(OnlineStatusContext);\r\n  if (context === undefined) {\r\n    throw new Error(\"useOnlineStatus must be used within an OnlineStatusProvider\");\r\n  }\r\n  return context;\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;;;AAFA;;AASA,0CAA0C;AAC1C,MAAM,oCAAsB,CAAA,GAAA,4RAAA,CAAA,gBAAa,AAAD,EAA2B;IAAE,UAAU;AAAK;AAG7E,SAAS,qBAAqB,EAAE,QAAQ,EAA2B;;IACxE,mEAAmE;IACnE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAW;IAElD,CAAA,GAAA,4RAAA,CAAA,YAAS,AAAD;0CAAE;YACR,qCAAqC;YACrC,YAAY,UAAU,MAAM;YAE5B,+CAA+C;YAC/C,MAAM;+DAAe;oBACnB,QAAQ,GAAG,CAAC;oBACZ,YAAY;gBACd;;YAEA,MAAM;gEAAgB;oBACpB,QAAQ,GAAG,CAAC;oBACZ,YAAY;gBACd;;YAEA,sBAAsB;YACtB,OAAO,gBAAgB,CAAC,UAAU;YAClC,OAAO,gBAAgB,CAAC,WAAW;YAEnC,sCAAsC;YACtC;kDAAO;oBACL,OAAO,mBAAmB,CAAC,UAAU;oBACrC,OAAO,mBAAmB,CAAC,WAAW;gBACxC;;QACF;yCAAG,EAAE;IAEL,qBACE,4TAAC,oBAAoB,QAAQ;QAAC,OAAO;YAAE;QAAS;kBAC7C;;;;;;AAGP;GAnCgB;KAAA;AAsCT,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,4RAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB", "debugId": null}}, {"offset": {"line": 1076, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/charlie-oscar-consulting/website/app/components/connection-status-toast.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useEffect, useState } from \"react\";\r\nimport { useOnlineStatus } from \"../contexts/online-status-context\";\r\nimport { useLanguage } from \"../translations/language-context\";\r\n\r\nexport function ConnectionStatusToast() {\r\n  const { isOnline } = useOnlineStatus();\r\n  const { t } = useLanguage();\r\n  const [showToast, setShowToast] = useState(false);\r\n  const [previousOnlineStatus, setPreviousOnlineStatus] = useState(true);\r\n\r\n  useEffect(() => {\r\n    // Only show toast when status changes, not on initial render\r\n    if (previousOnlineStatus !== isOnline) {\r\n      setShowToast(true);\r\n      const timer = setTimeout(() => {\r\n        setShowToast(false);\r\n      }, 5000); // Hide after 5 seconds\r\n\r\n      return () => clearTimeout(timer);\r\n    }\r\n    \r\n    setPreviousOnlineStatus(isOnline);\r\n  }, [isOnline, previousOnlineStatus]);\r\n\r\n  if (!showToast) return null;\r\n\r\n  return (\r\n    <div\r\n      className={`fixed bottom-4 right-4 z-50 p-4 rounded-md shadow-lg max-w-sm transition-all duration-300 ${\r\n        isOnline ? \"bg-green-100 text-green-800\" : \"bg-red-100 text-red-800\"\r\n      }`}\r\n      role=\"alert\"\r\n      data-testid=\"connection-status-toast\"\r\n    >\r\n      <div className=\"flex items-center\">\r\n        <div className=\"flex-shrink-0\">\r\n          {isOnline ? (\r\n            <svg\r\n              className=\"h-5 w-5 text-green-500\"\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n              viewBox=\"0 0 20 20\"\r\n              fill=\"currentColor\"\r\n            >\r\n              <path\r\n                fillRule=\"evenodd\"\r\n                d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\"\r\n                clipRule=\"evenodd\"\r\n              />\r\n            </svg>\r\n          ) : (\r\n            <svg\r\n              className=\"h-5 w-5 text-red-500\"\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n              viewBox=\"0 0 20 20\"\r\n              fill=\"currentColor\"\r\n            >\r\n              <path\r\n                fillRule=\"evenodd\"\r\n                d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\"\r\n                clipRule=\"evenodd\"\r\n              />\r\n            </svg>\r\n          )}\r\n        </div>\r\n        <div className=\"ml-3\">\r\n          <p className=\"text-sm font-medium\">\r\n            {isOnline\r\n              ? t('common.connectionRestored')\r\n              : t('common.connectionLost')}\r\n          </p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMO,SAAS;;IACd,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,kJAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,cAAW,AAAD;IACxB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IAEjE,CAAA,GAAA,4RAAA,CAAA,YAAS,AAAD;2CAAE;YACR,6DAA6D;YAC7D,IAAI,yBAAyB,UAAU;gBACrC,aAAa;gBACb,MAAM,QAAQ;6DAAW;wBACvB,aAAa;oBACf;4DAAG,OAAO,uBAAuB;gBAEjC;uDAAO,IAAM,aAAa;;YAC5B;YAEA,wBAAwB;QAC1B;0CAAG;QAAC;QAAU;KAAqB;IAEnC,IAAI,CAAC,WAAW,OAAO;IAEvB,qBACE,4TAAC;QACC,WAAW,CAAC,0FAA0F,EACpG,WAAW,gCAAgC,2BAC3C;QACF,MAAK;QACL,eAAY;kBAEZ,cAAA,4TAAC;YAAI,WAAU;;8BACb,4TAAC;oBAAI,WAAU;8BACZ,yBACC,4TAAC;wBACC,WAAU;wBACV,OAAM;wBACN,SAAQ;wBACR,MAAK;kCAEL,cAAA,4TAAC;4BACC,UAAS;4BACT,GAAE;4BACF,UAAS;;;;;;;;;;6CAIb,4TAAC;wBACC,WAAU;wBACV,OAAM;wBACN,SAAQ;wBACR,MAAK;kCAEL,cAAA,4TAAC;4BACC,UAAS;4BACT,GAAE;4BACF,UAAS;;;;;;;;;;;;;;;;8BAKjB,4TAAC;oBAAI,WAAU;8BACb,cAAA,4TAAC;wBAAE,WAAU;kCACV,WACG,EAAE,+BACF,EAAE;;;;;;;;;;;;;;;;;;;;;;AAMlB;GAtEgB;;QACO,kJAAA,CAAA,kBAAe;QACtB,8IAAA,CAAA,cAAW;;;KAFX", "debugId": null}}, {"offset": {"line": 1212, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/charlie-oscar-consulting/website/app/components/toast-container.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\nimport { ConnectionStatusToast } from \"./connection-status-toast\";\r\n\r\nexport function ToastContainer() {\r\n  return <ConnectionStatusToast />;\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAKO,SAAS;IACd,qBAAO,4TAAC,sJAAA,CAAA,wBAAqB;;;;;AAC/B;KAFgB", "debugId": null}}, {"offset": {"line": 1239, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/charlie-oscar-consulting/website/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,4NAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,yLAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 1258, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/charlie-oscar-consulting/website/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\r\n        destructive:\r\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\r\n        outline:\r\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\r\n        secondary:\r\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\r\n        ghost:\r\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\r\n        link: \"text-primary underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\r\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\r\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\r\n        icon: \"size-9\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nfunction Button({\r\n  className,\r\n  variant,\r\n  size,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"button\"> &\r\n  VariantProps<typeof buttonVariants> & {\r\n    asChild?: boolean\r\n  }) {\r\n  const Comp = asChild ? Slot : \"button\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"button\"\r\n      className={cn(buttonVariants({ variant, size, className }))}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Button, buttonVariants }\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,8OAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,uSAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,4TAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 1321, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/charlie-oscar-consulting/website/components/ui/sheet.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SheetPrimitive from \"@radix-ui/react-dialog\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\nimport { X } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Sheet = SheetPrimitive.Root\r\n\r\nconst SheetTrigger = SheetPrimitive.Trigger\r\n\r\nconst SheetClose = SheetPrimitive.Close\r\n\r\nconst SheetPortal = SheetPrimitive.Portal\r\n\r\nconst sheetVariants = cva(\r\n  \"fixed z-50 gap-4 bg-background p-6 shadow-lg transition ease-in-out data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:duration-300 data-[state=open]:duration-500\",\r\n  {\r\n    variants: {\r\n      side: {\r\n        top: \"inset-x-0 top-0 border-b data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top\",\r\n        bottom:\r\n          \"inset-x-0 bottom-0 border-t data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom\",\r\n        left: \"inset-y-0 left-0 h-full w-3/4 border-r data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left sm:max-w-sm\",\r\n        right:\r\n          \"inset-y-0 right-0 h-full w-3/4  border-l data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right sm:max-w-sm\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      side: \"right\",\r\n    },\r\n  }\r\n)\r\n\r\ninterface SheetContentProps\r\n  extends React.ComponentPropsWithoutRef<typeof SheetPrimitive.Content>,\r\n    VariantProps<typeof sheetVariants> {}\r\n\r\nconst SheetContent = React.forwardRef<\r\n  React.ElementRef<typeof SheetPrimitive.Content>,\r\n  SheetContentProps\r\n>(({ side = \"right\", className, children, ...props }, ref) => (\r\n  <SheetPortal>\r\n    <SheetOverlay />\r\n    <SheetPrimitive.Content\r\n      ref={ref}\r\n      className={cn(sheetVariants({ side }), className)}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <SheetPrimitive.Close className=\"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-secondary\">\r\n        <X className=\"h-4 w-4\" />\r\n        <span className=\"sr-only\">Close</span>\r\n      </SheetPrimitive.Close>\r\n    </SheetPrimitive.Content>\r\n  </SheetPortal>\r\n))\r\nSheetContent.displayName = SheetPrimitive.Content.displayName\r\n\r\nconst SheetOverlay = React.forwardRef<\r\n  React.ElementRef<typeof SheetPrimitive.Overlay>,\r\n  React.ComponentPropsWithoutRef<typeof SheetPrimitive.Overlay>\r\n>(({ className, ...props }, ref) => (\r\n  <SheetPrimitive.Overlay\r\n    className={cn(\r\n      \"fixed inset-0 z-50 bg-background/80 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\r\n      className\r\n    )}\r\n    {...props}\r\n    ref={ref}\r\n  />\r\n))\r\nSheetOverlay.displayName = SheetPrimitive.Overlay.displayName\r\n\r\nconst SheetHeader = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      \"flex flex-col space-y-2 text-center sm:text-left\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n)\r\nSheetHeader.displayName = \"SheetHeader\"\r\n\r\nconst SheetFooter = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n)\r\nSheetFooter.displayName = \"SheetFooter\"\r\n\r\nconst SheetTitle = React.forwardRef<\r\n  React.ElementRef<typeof SheetPrimitive.Title>,\r\n  React.ComponentPropsWithoutRef<typeof SheetPrimitive.Title>\r\n>(({ className, ...props }, ref) => (\r\n  <SheetPrimitive.Title\r\n    ref={ref}\r\n    className={cn(\"text-lg font-semibold text-foreground\", className)}\r\n    {...props}\r\n  />\r\n))\r\nSheetTitle.displayName = SheetPrimitive.Title.displayName\r\n\r\nconst SheetDescription = React.forwardRef<\r\n  React.ElementRef<typeof SheetPrimitive.Description>,\r\n  React.ComponentPropsWithoutRef<typeof SheetPrimitive.Description>\r\n>(({ className, ...props }, ref) => (\r\n  <SheetPrimitive.Description\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n))\r\nSheetDescription.displayName = SheetPrimitive.Description.displayName\r\n\r\nexport {\r\n  Sheet,\r\n  SheetPortal,\r\n  SheetOverlay,\r\n  SheetTrigger,\r\n  SheetClose,\r\n  SheetContent,\r\n  SheetHeader,\r\n  SheetFooter,\r\n  SheetTitle,\r\n  SheetDescription,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AACA;AAEA;AAPA;;;;;;;AASA,MAAM,QAAQ,+XAAA,CAAA,OAAmB;AAEjC,MAAM,eAAe,+XAAA,CAAA,UAAsB;AAE3C,MAAM,aAAa,+XAAA,CAAA,QAAoB;AAEvC,MAAM,cAAc,+XAAA,CAAA,SAAqB;AAEzC,MAAM,gBAAgB,CAAA,GAAA,8OAAA,CAAA,MAAG,AAAD,EACtB,oMACA;IACE,UAAU;QACR,MAAM;YACJ,KAAK;YACL,QACE;YACF,MAAM;YACN,OACE;QACJ;IACF;IACA,iBAAiB;QACf,MAAM;IACR;AACF;AAOF,MAAM,6BAAe,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,OAGlC,CAAC,EAAE,OAAO,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpD,4TAAC;;0BACC,4TAAC;;;;;0BACD,4TAAC,+XAAA,CAAA,UAAsB;gBACrB,KAAK;gBACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,cAAc;oBAAE;gBAAK,IAAI;gBACtC,GAAG,KAAK;;oBAER;kCACD,4TAAC,+XAAA,CAAA,QAAoB;wBAAC,WAAU;;0CAC9B,4TAAC,mRAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;0CACb,4TAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;;AAKlC,aAAa,WAAW,GAAG,+XAAA,CAAA,UAAsB,CAAC,WAAW;AAE7D,MAAM,6BAAe,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,EAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC,+XAAA,CAAA,UAAsB;QACrB,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,gLACA;QAED,GAAG,KAAK;QACT,KAAK;;;;;;MAVH;AAaN,aAAa,WAAW,GAAG,+XAAA,CAAA,UAAsB,CAAC,WAAW;AAE7D,MAAM,cAAc,CAAC,EACnB,SAAS,EACT,GAAG,OACkC,iBACrC,4TAAC;QACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,oDACA;QAED,GAAG,KAAK;;;;;;MATP;AAYN,YAAY,WAAW,GAAG;AAE1B,MAAM,cAAc,CAAC,EACnB,SAAS,EACT,GAAG,OACkC,iBACrC,4TAAC;QACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;MATP;AAYN,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC,+XAAA,CAAA,QAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,yCAAyC;QACtD,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG,+XAAA,CAAA,QAAoB,CAAC,WAAW;AAEzD,MAAM,iCAAmB,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,QAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC,+XAAA,CAAA,cAA0B;QACzB,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,iBAAiB,WAAW,GAAG,+XAAA,CAAA,cAA0B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1489, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/charlie-oscar-consulting/website/components/layout/header.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect } from \"react\";\r\nimport Link from \"next/link\";\r\nimport { usePathname } from \"next/navigation\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Sheet, SheetContent, SheetTrigger } from \"@/components/ui/sheet\";\r\nimport { LanguageSwitcher } from \"@/app/components/language-switcher\";\r\nimport {\r\n  Menu,\r\n  X,\r\n  ChevronDown,\r\n  Building2,\r\n  Home,\r\n  Hammer,\r\n  Phone,\r\n  Mail,\r\n  Search,\r\n  Facebook,\r\n  Twitter,\r\n  Linkedin,\r\n  Instagram,\r\n  FileText,\r\n  Users,\r\n  Calculator,\r\n  ClipboardCheck,\r\n  MessageCircle\r\n} from \"lucide-react\";\r\n\r\nexport function Header() {\r\n  const [isScrolled, setIsScrolled] = useState(false);\r\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\r\n  const [activeDropdown, setActiveDropdown] = useState<string | null>(null);\r\n  const [searchQuery, setSearchQuery] = useState(\"\");\r\n  const pathname = usePathname();\r\n\r\n  useEffect(() => {\r\n    const handleScroll = () => {\r\n      setIsScrolled(window.scrollY > 100);\r\n    };\r\n\r\n    window.addEventListener(\"scroll\", handleScroll);\r\n    return () => window.removeEventListener(\"scroll\", handleScroll);\r\n  }, []);\r\n\r\n  const navigation = [\r\n    {\r\n      name: \"Accueil\",\r\n      href: \"/\",\r\n      current: pathname === \"/\"\r\n    },\r\n    {\r\n      name: \"Services\",\r\n      href: \"#\",\r\n      current: false,\r\n      dropdown: [\r\n        {\r\n          name: \"Foncier\",\r\n          href: \"/foncier\",\r\n          description: \"Sécurisation et conseil foncier\",\r\n          icon: Building2,\r\n          featured: true\r\n        },\r\n        {\r\n          name: \"Immobilier\",\r\n          href: \"/immobilier\",\r\n          description: \"Conseil et investissement immobilier\",\r\n          icon: Home\r\n        },\r\n        {\r\n          name: \"Construction\",\r\n          href: \"/construction\",\r\n          description: \"Expertise technique et suivi\",\r\n          icon: Hammer\r\n        }\r\n      ]\r\n    },\r\n    // {\r\n    //   name: \"Formulaires\",\r\n    //   href: \"#\",\r\n    //   current: false,\r\n    //   dropdown: [\r\n    //     {\r\n    //       name: \"Formulaire Foncier\",\r\n    //       href: \"/formulaire\",\r\n    //       description: \"Demande d'accompagnement foncier\",\r\n    //       icon: FileText,\r\n    //       featured: true\r\n    //     },\r\n    //     {\r\n    //       name: \"Évaluation Immobilière\",\r\n    //       href: \"/formulaire/evaluation\",\r\n    //       description: \"Demande d'évaluation de bien\",\r\n    //       icon: Calculator\r\n    //     },\r\n    //     {\r\n    //       name: \"Consultation Générale\",\r\n    //       href: \"/formulaire/consultation\",\r\n    //       description: \"Demande de consultation\",\r\n    //       icon: ClipboardCheck\r\n    //     }\r\n    //   ]\r\n    // },\r\n    {\r\n      name: \"À propos\",\r\n      href: \"/a-propos\",\r\n      current: pathname === \"/a-propos\"\r\n    },\r\n    // {\r\n    //   name: \"Projets\",\r\n    //   href: \"/projets\",\r\n    //   current: pathname === \"/projets\"\r\n    // },\r\n    {\r\n      name: \"Blog\",\r\n      href: \"/blog\",\r\n      current: pathname === \"/blog\"\r\n    }\r\n  ];\r\n\r\n  const socialLinks = [\r\n    { name: \"Facebook\", href: \"https://www.facebook.com/charlieoscarconsulting\", icon: Facebook },\r\n    { name: \"LinkedIn\", href: \"https://www.linkedin.com/company/charlie-oscar-consulting\", icon: Linkedin },\r\n\r\n  ];\r\n\r\n  return (\r\n    pathname.startsWith(\"/m\") ? null : // Hide header for /m routes\r\n    <header className=\"fixed top-0 left-0 right-0 z-50\">\r\n      {/* Top Level - Contact & Social */}\r\n      <div\r\n        className={`bg-gray-900 text-white transition-all duration-500 ease-in-out ${\r\n          isScrolled ? 'h-0 overflow-hidden opacity-0' : 'h-12 opacity-100'\r\n        }`}\r\n      >\r\n        <div className=\"container mx-auto px-4\">\r\n          <div className=\"flex items-center justify-between h-12\">\r\n            {/* Contact Info */}\r\n            <div className=\"flex items-center space-x-6 text-sm\">\r\n              <a href=\"tel:+237682658037\" className=\"flex items-center space-x-2 hover:text-primary transition-colors\">\r\n                <Phone className=\"w-4 h-4\" />\r\n                <span>+237682658037</span>\r\n              </a>\r\n              <a href=\"mailto:<EMAIL>\" className=\"flex items-center space-x-2 hover:text-primary transition-colors\">\r\n                <Mail className=\"w-4 h-4\" />\r\n                <span className=\"hidden md:inline\"><EMAIL></span>\r\n              </a>\r\n            </div>\r\n\r\n            {/* Search & Social */}\r\n            <div className=\"flex items-center space-x-4\">\r\n              {/* Search Bar */}\r\n              {/* <div className=\"relative hidden md:block\">\r\n                <Input\r\n                  type=\"text\"\r\n                  placeholder=\"Rechercher...\"\r\n                  value={searchQuery}\r\n                  onChange={(e) => setSearchQuery(e.target.value)}\r\n                  className=\"w-64 h-8 bg-gray-800 border-gray-700 text-white placeholder:text-gray-400 focus:border-primary text-sm\"\r\n                />\r\n                <Search className=\"absolute right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400\" />\r\n              </div> */}\r\n\r\n              {/* Social Links */}\r\n              <div className=\"flex items-center space-x-2\">\r\n                {socialLinks.map((social) => {\r\n                  const Icon = social.icon;\r\n                  return (\r\n                    <a\r\n                      key={social.name}\r\n                      href={social.href}\r\n                      className=\"w-8 h-8 flex items-center justify-center hover:bg-primary rounded transition-colors\"\r\n                      aria-label={social.name}\r\n                      title={`Suivez-nous sur ${social.name}`}\r\n                      target=\"_blank\"\r\n                    >\r\n                      <Icon className=\"w-4 h-4\" />\r\n                    </a>\r\n                  );\r\n                })}\r\n                <a\r\n                      href=\"https://wa.me/+237682658037\"\r\n                      className=\"w-8 h-8 flex items-center justify-center hover:bg-primary rounded transition-colors\"\r\n                      aria-label=\"WhatsApp\"\r\n                      target=\"_blank\"\r\n                      title=\"Contactez-nous sur WhatsApp\"\r\n                  >\r\n                  <MessageCircle className=\"w-4 h-4\" />\r\n                </a>\r\n                {/* <LanguageSwitcher className=\"w-8 h-8 flex items-center justify-center hover:bg-primary rounded transition-colors\" /> */}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Bottom Level - Logo & Navigation */}\r\n      <div\r\n        className={`bg-white/95 backdrop-blur-md shadow-lg border-b border-gray-200 transition-all duration-500 ease-in-out ${\r\n          isScrolled ? 'translate-y-0' : 'translate-y-0'\r\n        }`}\r\n      >\r\n        <div className=\"container mx-auto px-4\">\r\n          <div className=\"flex items-center justify-between h-20\">\r\n            {/* Logo */}\r\n            <Link href=\"/\" className=\"flex items-center space-x-3 group\">\r\n              <img\r\n                src=\"/logo.svg\"\r\n                alt=\"Charlie Oscar Consulting Logo\"\r\n                className=\"w-40 h-40 sm:w-52 sm:h-52  rounded-lg transition-transform duration-300 group-hover:scale-110 object-contain\"\r\n              />\r\n            </Link>\r\n\r\n            {/* Desktop Navigation */}\r\n            <nav className=\"hidden lg:flex items-center space-x-1\">\r\n              {navigation.map((item) => (\r\n                <div key={item.name} className=\"relative\">\r\n                  {item.dropdown ? (\r\n                    <div\r\n                      className=\"relative\"\r\n                      onMouseEnter={() => setActiveDropdown(item.name)}\r\n                      onMouseLeave={() => setActiveDropdown(null)}\r\n                    >\r\n                      <button\r\n                        className={`flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 ${\r\n                          item.current\r\n                            ? 'text-primary bg-primary/10'\r\n                            : 'text-gray-700 hover:text-primary hover:bg-gray-50'\r\n                        }`}\r\n                      >\r\n                        {item.name}\r\n                        <ChevronDown className={`ml-1 h-4 w-4 transition-transform duration-200 ${\r\n                          activeDropdown === item.name ? 'rotate-180' : ''\r\n                        }`} />\r\n                      </button>\r\n\r\n                      {/* Dropdown Menu */}\r\n                      {activeDropdown === item.name && (\r\n                        <div className=\"absolute top-full left-0 mt-2 w-80 bg-white rounded-xl shadow-xl border border-gray-200 py-4 z-50\">\r\n                          <div className=\"px-4 pb-3 border-b border-gray-100\">\r\n                            <h3 className=\"text-sm font-semibold text-gray-900\">\r\n                              {item.name === \"Services\" ? \"Nos domaines d'expertise\" : \"Nos formulaires\"}\r\n                            </h3>\r\n                          </div>\r\n                          <div className=\"py-2\">\r\n                            {item.dropdown.map((dropdownItem) => {\r\n                              const Icon = dropdownItem.icon;\r\n                              return (\r\n                                <Link\r\n                                  key={dropdownItem.name}\r\n                                  href={dropdownItem.href}\r\n                                  className={`flex items-start space-x-3 px-4 py-3 hover:bg-gray-50 transition-colors duration-200 ${\r\n                                    dropdownItem.featured ? 'bg-primary/5' : ''\r\n                                  }`}\r\n                                >\r\n                                  <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${\r\n                                    dropdownItem.featured\r\n                                      ? 'bg-primary/10 text-primary'\r\n                                      : 'bg-gray-100 text-gray-600'\r\n                                  }`}>\r\n                                    <Icon className=\"w-5 h-5\" />\r\n                                  </div>\r\n                                  <div className=\"flex-1\">\r\n                                    <div className={`font-medium ${\r\n                                      dropdownItem.featured ? 'text-primary' : 'text-gray-900'\r\n                                    }`}>\r\n                                      {dropdownItem.name}\r\n                                      {dropdownItem.featured && (\r\n                                        <span className=\"ml-2 text-xs bg-primary text-white px-2 py-0.5 rounded-full\">\r\n                                          Principal\r\n                                        </span>\r\n                                      )}\r\n                                    </div>\r\n                                    <div className=\"text-sm text-gray-600\">\r\n                                      {dropdownItem.description}\r\n                                    </div>\r\n                                  </div>\r\n                                </Link>\r\n                              );\r\n                            })}\r\n                          </div>\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n                  ) : (\r\n                    <Link\r\n                      href={item.href}\r\n                      className={`px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 ${\r\n                        item.current\r\n                          ? 'text-primary bg-primary/10'\r\n                          : 'text-gray-700 hover:text-primary hover:bg-gray-50'\r\n                      }`}\r\n                    >\r\n                      {item.name}\r\n                    </Link>\r\n                  )}\r\n                </div>\r\n              ))}\r\n            </nav>\r\n\r\n            {/* CTA Button */}\r\n            <div className=\"hidden lg:flex items-center space-x-4\">\r\n              <Button asChild className=\"group\">\r\n                <Link href=\"/formulaire\" className=\"flex items-center\">\r\n                  Demander un accompagnement\r\n                  <ChevronDown className=\"ml-2 h-4 w-4 rotate-[-90deg] transition-transform group-hover:translate-x-1\" />\r\n                </Link>\r\n              </Button>\r\n            </div>\r\n\r\n            {/* Mobile Menu Button */}\r\n            <Sheet open={isMobileMenuOpen} onOpenChange={setIsMobileMenuOpen}>\r\n              <SheetTrigger asChild>\r\n                <Button variant=\"ghost\" size=\"icon\" className=\"lg:hidden\">\r\n                  <Menu className=\"h-6 w-6\" />\r\n                </Button>\r\n              </SheetTrigger>\r\n\r\n              <SheetContent side=\"right\" className=\"w-full sm:w-80\">\r\n                <div className=\"flex flex-col h-full\">\r\n                  {/* Mobile Header */}\r\n                  <div className=\"flex items-center justify-between pb-6 border-b border-gray-200\">\r\n                    <Link href=\"/\" className=\"flex items-center space-x-3\" onClick={() => setIsMobileMenuOpen(false)}>\r\n                      <div className=\"w-32 h-24  bg-white rounded-lg flex items-center justify-center\">\r\n                        <img src=\"/logo.svg\" alt=\"Charlie Oscar Consulting Logo\" className=\"w-full h-full\" />\r\n                      </div>\r\n                    </Link>\r\n                  </div>\r\n\r\n                  {/* Mobile Navigation */}\r\n                  <nav className=\"flex-1 py-6 space-y-2\">\r\n                    {navigation.map((item) => (\r\n                      <div key={item.name}>\r\n                        {item.dropdown ? (\r\n                          <div className=\"space-y-2\">\r\n                            <div className=\"text-sm font-semibold text-gray-900 px-3 py-2\">\r\n                              {item.name}\r\n                            </div>\r\n                            <div className=\"space-y-1 pl-4\">\r\n                              {item.dropdown.map((dropdownItem) => {\r\n                                const Icon = dropdownItem.icon;\r\n                                return (\r\n                                  <Link\r\n                                    key={dropdownItem.name}\r\n                                    href={dropdownItem.href}\r\n                                    className=\"flex items-center space-x-3 px-3 py-2 text-gray-700 hover:text-primary hover:bg-gray-50 rounded-lg transition-colors\"\r\n                                    onClick={() => setIsMobileMenuOpen(false)}\r\n                                  >\r\n                                    <Icon className=\"w-5 h-5\" />\r\n                                    <span>{dropdownItem.name}</span>\r\n                                    {dropdownItem.featured && (\r\n                                      <span className=\"text-xs bg-primary text-white px-2 py-0.5 rounded-full\">\r\n                                        Principal\r\n                                      </span>\r\n                                    )}\r\n                                  </Link>\r\n                                );\r\n                              })}\r\n                            </div>\r\n                          </div>\r\n                        ) : (\r\n                          <Link\r\n                            href={item.href}\r\n                            className={`block px-3 py-2 text-sm font-medium rounded-lg transition-colors ${\r\n                              item.current\r\n                                ? 'text-primary bg-primary/10'\r\n                                : 'text-gray-700 hover:text-primary hover:bg-gray-50'\r\n                            }`}\r\n                            onClick={() => setIsMobileMenuOpen(false)}\r\n                          >\r\n                            {item.name}\r\n                          </Link>\r\n                        )}\r\n                      </div>\r\n                    ))}\r\n                  </nav>\r\n\r\n                  {/* Mobile Contact & Search */}\r\n                  <div className=\"border-t border-gray-200 pt-6 space-y-4\">\r\n                    {/* Mobile Search */}\r\n                    {/* <div className=\"relative\">\r\n                      <Input\r\n                        type=\"text\"\r\n                        placeholder=\"Rechercher...\"\r\n                        value={searchQuery}\r\n                        onChange={(e) => setSearchQuery(e.target.value)}\r\n                        className=\"w-full\"\r\n                      />\r\n                      <Search className=\"absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400\" />\r\n                    </div> */}\r\n\r\n                    {/* Contact Info */}\r\n                    <div className=\"space-y-3\">\r\n                      <a href=\"tel:+237682658037\" className=\"flex items-center space-x-3 text-gray-700 hover:text-primary transition-colors\">\r\n                        <Phone className=\"w-5 h-5\" />\r\n                        <span>+237682658037</span>\r\n                      </a>\r\n                      <a href=\"mailto:<EMAIL>\" className=\"flex items-center space-x-3 text-gray-700 hover:text-primary transition-colors\">\r\n                        <Mail className=\"w-5 h-5\" />\r\n                        <span className=\"\"><EMAIL></span>\r\n                      </a>\r\n                    </div>\r\n\r\n                    {/* Social Links */}\r\n                    <div className=\"flex items-center space-x-3\">\r\n                      {socialLinks.map((social) => {\r\n                        const Icon = social.icon;\r\n                        return (\r\n                          <a\r\n                            key={social.name}\r\n                            href={social.href}\r\n                            className=\"w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center hover:bg-primary hover:text-white transition-colors\"\r\n                            target=\"_blank\"\r\n                            aria-label={social.name}\r\n                          >\r\n                            <Icon className=\"w-5 h-5\" />\r\n                          </a>\r\n                        );\r\n                      })}\r\n                      <a\r\n                        href=\"https://wa.me/+237682658037\"\r\n                        className=\"w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center hover:bg-primary hover:text-white transition-colors\"\r\n                        aria-label=\"WhatsApp\"\r\n                        target=\"_blank\"\r\n                        title=\"Contactez-nous sur WhatsApp\"\r\n                      >\r\n                        <MessageCircle className=\"w-4 h-4\" />\r\n                      </a>\r\n                    </div>\r\n\r\n                    <Button asChild className=\"w-full\">\r\n                      <Link href=\"/formulaire\" onClick={() => setIsMobileMenuOpen(false)}>\r\n                        Demander un accompagnement\r\n                      </Link>\r\n                    </Button>\r\n                  </div>\r\n                </div>\r\n              </SheetContent>\r\n            </Sheet>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </header>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAEA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AATA;;;;;;;AA8BO,SAAS;;IACd,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAiB;IACpE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,WAAW,CAAA,GAAA,oQAAA,CAAA,cAAW,AAAD;IAE3B,CAAA,GAAA,4RAAA,CAAA,YAAS,AAAD;4BAAE;YACR,MAAM;iDAAe;oBACnB,cAAc,OAAO,OAAO,GAAG;gBACjC;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAClC;oCAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;2BAAG,EAAE;IAEL,MAAM,aAAa;QACjB;YACE,MAAM;YACN,MAAM;YACN,SAAS,aAAa;QACxB;QACA;YACE,MAAM;YACN,MAAM;YACN,SAAS;YACT,UAAU;gBACR;oBACE,MAAM;oBACN,MAAM;oBACN,aAAa;oBACb,MAAM,uSAAA,CAAA,YAAS;oBACf,UAAU;gBACZ;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,aAAa;oBACb,MAAM,0RAAA,CAAA,OAAI;gBACZ;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,aAAa;oBACb,MAAM,6RAAA,CAAA,SAAM;gBACd;aACD;QACH;QACA,IAAI;QACJ,yBAAyB;QACzB,eAAe;QACf,oBAAoB;QACpB,gBAAgB;QAChB,QAAQ;QACR,oCAAoC;QACpC,6BAA6B;QAC7B,yDAAyD;QACzD,wBAAwB;QACxB,uBAAuB;QACvB,SAAS;QACT,QAAQ;QACR,wCAAwC;QACxC,wCAAwC;QACxC,qDAAqD;QACrD,yBAAyB;QACzB,SAAS;QACT,QAAQ;QACR,uCAAuC;QACvC,0CAA0C;QAC1C,gDAAgD;QAChD,6BAA6B;QAC7B,QAAQ;QACR,MAAM;QACN,KAAK;QACL;YACE,MAAM;YACN,MAAM;YACN,SAAS,aAAa;QACxB;QACA,IAAI;QACJ,qBAAqB;QACrB,sBAAsB;QACtB,qCAAqC;QACrC,KAAK;QACL;YACE,MAAM;YACN,MAAM;YACN,SAAS,aAAa;QACxB;KACD;IAED,MAAM,cAAc;QAClB;YAAE,MAAM;YAAY,MAAM;YAAmD,MAAM,iSAAA,CAAA,WAAQ;QAAC;QAC5F;YAAE,MAAM;YAAY,MAAM;YAA6D,MAAM,iSAAA,CAAA,WAAQ;QAAC;KAEvG;IAED,OACE,SAAS,UAAU,CAAC,QAAQ,qBAC5B,4TAAC;QAAO,WAAU;;0BAEhB,4TAAC;gBACC,WAAW,CAAC,+DAA+D,EACzE,aAAa,kCAAkC,oBAC/C;0BAEF,cAAA,4TAAC;oBAAI,WAAU;8BACb,cAAA,4TAAC;wBAAI,WAAU;;0CAEb,4TAAC;gCAAI,WAAU;;kDACb,4TAAC;wCAAE,MAAK;wCAAoB,WAAU;;0DACpC,4TAAC,2RAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,4TAAC;0DAAK;;;;;;;;;;;;kDAER,4TAAC;wCAAE,MAAK;wCAA4C,WAAU;;0DAC5D,4TAAC,yRAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,4TAAC;gDAAK,WAAU;0DAAmB;;;;;;;;;;;;;;;;;;0CAKvC,4TAAC;gCAAI,WAAU;0CAcb,cAAA,4TAAC;oCAAI,WAAU;;wCACZ,YAAY,GAAG,CAAC,CAAC;4CAChB,MAAM,OAAO,OAAO,IAAI;4CACxB,qBACE,4TAAC;gDAEC,MAAM,OAAO,IAAI;gDACjB,WAAU;gDACV,cAAY,OAAO,IAAI;gDACvB,OAAO,CAAC,gBAAgB,EAAE,OAAO,IAAI,EAAE;gDACvC,QAAO;0DAEP,cAAA,4TAAC;oDAAK,WAAU;;;;;;+CAPX,OAAO,IAAI;;;;;wCAUtB;sDACA,4TAAC;4CACK,MAAK;4CACL,WAAU;4CACV,cAAW;4CACX,QAAO;4CACP,OAAM;sDAEV,cAAA,4TAAC,+SAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUrC,4TAAC;gBACC,WAAW,CAAC,wGAAwG,EAClH,aAAa,kBAAkB,iBAC/B;0BAEF,cAAA,4TAAC;oBAAI,WAAU;8BACb,cAAA,4TAAC;wBAAI,WAAU;;0CAEb,4TAAC,8RAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;0CACvB,cAAA,4TAAC;oCACC,KAAI;oCACJ,KAAI;oCACJ,WAAU;;;;;;;;;;;0CAKd,4TAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC,qBACf,4TAAC;wCAAoB,WAAU;kDAC5B,KAAK,QAAQ,iBACZ,4TAAC;4CACC,WAAU;4CACV,cAAc,IAAM,kBAAkB,KAAK,IAAI;4CAC/C,cAAc,IAAM,kBAAkB;;8DAEtC,4TAAC;oDACC,WAAW,CAAC,uFAAuF,EACjG,KAAK,OAAO,GACR,+BACA,qDACJ;;wDAED,KAAK,IAAI;sEACV,4TAAC,2SAAA,CAAA,cAAW;4DAAC,WAAW,CAAC,+CAA+C,EACtE,mBAAmB,KAAK,IAAI,GAAG,eAAe,IAC9C;;;;;;;;;;;;gDAIH,mBAAmB,KAAK,IAAI,kBAC3B,4TAAC;oDAAI,WAAU;;sEACb,4TAAC;4DAAI,WAAU;sEACb,cAAA,4TAAC;gEAAG,WAAU;0EACX,KAAK,IAAI,KAAK,aAAa,6BAA6B;;;;;;;;;;;sEAG7D,4TAAC;4DAAI,WAAU;sEACZ,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC;gEAClB,MAAM,OAAO,aAAa,IAAI;gEAC9B,qBACE,4TAAC,8RAAA,CAAA,UAAI;oEAEH,MAAM,aAAa,IAAI;oEACvB,WAAW,CAAC,qFAAqF,EAC/F,aAAa,QAAQ,GAAG,iBAAiB,IACzC;;sFAEF,4TAAC;4EAAI,WAAW,CAAC,sDAAsD,EACrE,aAAa,QAAQ,GACjB,+BACA,6BACJ;sFACA,cAAA,4TAAC;gFAAK,WAAU;;;;;;;;;;;sFAElB,4TAAC;4EAAI,WAAU;;8FACb,4TAAC;oFAAI,WAAW,CAAC,YAAY,EAC3B,aAAa,QAAQ,GAAG,iBAAiB,iBACzC;;wFACC,aAAa,IAAI;wFACjB,aAAa,QAAQ,kBACpB,4TAAC;4FAAK,WAAU;sGAA8D;;;;;;;;;;;;8FAKlF,4TAAC;oFAAI,WAAU;8FACZ,aAAa,WAAW;;;;;;;;;;;;;mEAzBxB,aAAa,IAAI;;;;;4DA8B5B;;;;;;;;;;;;;;;;;iEAMR,4TAAC,8RAAA,CAAA,UAAI;4CACH,MAAM,KAAK,IAAI;4CACf,WAAW,CAAC,qEAAqE,EAC/E,KAAK,OAAO,GACR,+BACA,qDACJ;sDAED,KAAK,IAAI;;;;;;uCA7EN,KAAK,IAAI;;;;;;;;;;0CAqFvB,4TAAC;gCAAI,WAAU;0CACb,cAAA,4TAAC,8HAAA,CAAA,SAAM;oCAAC,OAAO;oCAAC,WAAU;8CACxB,cAAA,4TAAC,8RAAA,CAAA,UAAI;wCAAC,MAAK;wCAAc,WAAU;;4CAAoB;0DAErD,4TAAC,2SAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;0CAM7B,4TAAC,6HAAA,CAAA,QAAK;gCAAC,MAAM;gCAAkB,cAAc;;kDAC3C,4TAAC,6HAAA,CAAA,eAAY;wCAAC,OAAO;kDACnB,cAAA,4TAAC,8HAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAQ,MAAK;4CAAO,WAAU;sDAC5C,cAAA,4TAAC,yRAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;;;;;;kDAIpB,4TAAC,6HAAA,CAAA,eAAY;wCAAC,MAAK;wCAAQ,WAAU;kDACnC,cAAA,4TAAC;4CAAI,WAAU;;8DAEb,4TAAC;oDAAI,WAAU;8DACb,cAAA,4TAAC,8RAAA,CAAA,UAAI;wDAAC,MAAK;wDAAI,WAAU;wDAA8B,SAAS,IAAM,oBAAoB;kEACxF,cAAA,4TAAC;4DAAI,WAAU;sEACb,cAAA,4TAAC;gEAAI,KAAI;gEAAY,KAAI;gEAAgC,WAAU;;;;;;;;;;;;;;;;;;;;;8DAMzE,4TAAC;oDAAI,WAAU;8DACZ,WAAW,GAAG,CAAC,CAAC,qBACf,4TAAC;sEACE,KAAK,QAAQ,iBACZ,4TAAC;gEAAI,WAAU;;kFACb,4TAAC;wEAAI,WAAU;kFACZ,KAAK,IAAI;;;;;;kFAEZ,4TAAC;wEAAI,WAAU;kFACZ,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC;4EAClB,MAAM,OAAO,aAAa,IAAI;4EAC9B,qBACE,4TAAC,8RAAA,CAAA,UAAI;gFAEH,MAAM,aAAa,IAAI;gFACvB,WAAU;gFACV,SAAS,IAAM,oBAAoB;;kGAEnC,4TAAC;wFAAK,WAAU;;;;;;kGAChB,4TAAC;kGAAM,aAAa,IAAI;;;;;;oFACvB,aAAa,QAAQ,kBACpB,4TAAC;wFAAK,WAAU;kGAAyD;;;;;;;+EARtE,aAAa,IAAI;;;;;wEAc5B;;;;;;;;;;;qFAIJ,4TAAC,8RAAA,CAAA,UAAI;gEACH,MAAM,KAAK,IAAI;gEACf,WAAW,CAAC,iEAAiE,EAC3E,KAAK,OAAO,GACR,+BACA,qDACJ;gEACF,SAAS,IAAM,oBAAoB;0EAElC,KAAK,IAAI;;;;;;2DAtCN,KAAK,IAAI;;;;;;;;;;8DA8CvB,4TAAC;oDAAI,WAAU;;sEAcb,4TAAC;4DAAI,WAAU;;8EACb,4TAAC;oEAAE,MAAK;oEAAoB,WAAU;;sFACpC,4TAAC,2RAAA,CAAA,QAAK;4EAAC,WAAU;;;;;;sFACjB,4TAAC;sFAAK;;;;;;;;;;;;8EAER,4TAAC;oEAAE,MAAK;oEAA4C,WAAU;;sFAC5D,4TAAC,yRAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;sFAChB,4TAAC;4EAAK,WAAU;sFAAG;;;;;;;;;;;;;;;;;;sEAKvB,4TAAC;4DAAI,WAAU;;gEACZ,YAAY,GAAG,CAAC,CAAC;oEAChB,MAAM,OAAO,OAAO,IAAI;oEACxB,qBACE,4TAAC;wEAEC,MAAM,OAAO,IAAI;wEACjB,WAAU;wEACV,QAAO;wEACP,cAAY,OAAO,IAAI;kFAEvB,cAAA,4TAAC;4EAAK,WAAU;;;;;;uEANX,OAAO,IAAI;;;;;gEAStB;8EACA,4TAAC;oEACC,MAAK;oEACL,WAAU;oEACV,cAAW;oEACX,QAAO;oEACP,OAAM;8EAEN,cAAA,4TAAC,+SAAA,CAAA,gBAAa;wEAAC,WAAU;;;;;;;;;;;;;;;;;sEAI7B,4TAAC,8HAAA,CAAA,SAAM;4DAAC,OAAO;4DAAC,WAAU;sEACxB,cAAA,4TAAC,8RAAA,CAAA,UAAI;gEAAC,MAAK;gEAAc,SAAS,IAAM,oBAAoB;0EAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAa1F;GA/ZgB;;QAKG,oQAAA,CAAA,cAAW;;;KALd", "debugId": null}}, {"offset": {"line": 2297, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/charlie-oscar-consulting/website/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\r\n  return (\r\n    <input\r\n      type={type}\r\n      data-slot=\"input\"\r\n      className={cn(\r\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\r\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Input }\r\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,4TAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 2334, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/charlie-oscar-consulting/website/app/cms/utils/contact.ts"], "sourcesContent": ["import contact from \"@/app/cms/data/contact.json\";\r\n\r\nexport function getContactInfo() {\r\n  return contact;\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;;AAEO,SAAS;IACd,OAAO,qGAAA,CAAA,UAAO;AAChB", "debugId": null}}, {"offset": {"line": 2351, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/charlie-oscar-consulting/website/lib/foncier-services-data.ts"], "sourcesContent": ["export interface ProcessStep {\r\n  id: number;\r\n  title: string;\r\n  description: string;\r\n  duration?: string;\r\n  documents?: string[];\r\n}\r\n\r\nexport interface Testimonial {\r\n  id: number;\r\n  name: string;\r\n  role: string;\r\n  location: string;\r\n  content: string;\r\n  rating: number;\r\n}\r\n\r\nexport interface FAQ {\r\n  id: number;\r\n  question: string;\r\n  answer: string;\r\n}\r\n\r\nexport interface FoncierService {\r\n  id: string;\r\n  slug: string;\r\n  title: string;\r\n  shortDescription: string;\r\n  detailedDescription: string;\r\n  documents?: string[];\r\n  process?: ProcessStep[];\r\n  testimonials?: Testimonial[];\r\n  faq?: FAQ[];\r\n  featured: boolean;\r\n  category: string;\r\n  estimatedDuration: string;\r\n  complexity: 'Simple' | 'Modéré' | 'Complexe';\r\n  prestations?: { title: string; description: string }[];\r\n  advantages?: { title: string; description: string }[];\r\n  whenToCall?: string[];\r\n}\r\n\r\n\r\nexport const foncierServices: FoncierService[] = [\r\n  {\r\n    id: \"1\",\r\n    slug: \"derogation-speciale\",\r\n    title: \"Dérogation Spéciale\",\r\n    shortDescription: \"Obtenez l'autorisation ministérielle pour initier votre immatriculation directe ou votre concession domaniale\",\r\n    detailedDescription: \"Une dérogation spéciale est une autorisation administrative émise par le ministre des Domaines, autorisant par dérogation l'immatriculation cadastrale ou l'octroi d'une concession domaniale dans le cadre du droit foncier. \",\r\n    category: \"Procédures spéciales\",\r\n    estimatedDuration: \"3-6 mois\",\r\n    complexity: \"Complexe\",\r\n    featured: true,\r\n    prestations: [\r\n      { title: \"Analyse approfondie de l'éligibilité\", description: \"Évaluation de la situation de votre terrain et de votre projet pour déterminer la faisabilité et les fondements juridiques d'une dérogation.\" },\r\n      { title: \"Constitution et dépôt du dossier ministériel\", description: \"Préparation exhaustive de tous les documents requis et dépôt officiel auprès du Ministère des Domaines, du Cadastre et des Affaires Foncières (MINDCAF).\" },\r\n      { title: \"Suivi personnalisé et relances administratives\", description: \"Coordination et suivi proactif de votre dossier auprès des différentes administrations impliquées, avec des relances régulières pour accélérer le processus.\" },\r\n      { title: \"Rédaction de requêtes et mémoires justificatifs\", description: \"Élaboration des arguments juridiques et des courriers officiels pour appuyer votre demande de dérogation.\" },\r\n      { title: \"Conseils stratégiques et juridiques\", description: \"Orientation sur les meilleures approches et solutions pour surmonter les obstacles et sécuriser l'obtention de votre dérogation.\" }\r\n    ],\r\n    advantages: [\r\n      { title: \"Légalisation de situations foncières complexes\", description: \"Permet de régulariser des projets ou des occupations de terrain qui sortent du cadre réglementaire habituel.\" },\r\n      { title: \"Sécurisation de vos futurs droits fonciers\", description: \"La dérogation est la première étape essentielle pour obtenir un titre foncier ou une concession sur un bien atypique.\" },\r\n      { title: \"Accélération des processus d'immatriculation/concession\", description: \"Notre expertise des rouages administratifs réduit les délais et les risques de blocage.\" },\r\n      { title: \"Conformité administrative garantie\", description: \"La procédure est menée dans le respect strict des textes de loi, évitant les litiges ultérieurs.\" }\r\n    ],\r\n    whenToCall: [\r\n      \"Votre projet foncier (construction, développement) ne respecte pas les normes urbanistiques ou foncières standard et nécessite une autorisation exceptionnelle.\",\r\n      \"Vous souhaitez immatriculer un terrain ou obtenir une concession sur un bien présentant des spécificités juridiques ou des contraintes particulières.\",\r\n      \"Vous avez besoin d'une décision ministérielle pour débloquer une situation foncière complexe ou initiée de manière non conventionnelle.\"\r\n    ]\r\n  },\r\n  {\r\n    id: \"2\",\r\n    slug: \"dossier-technique\",\r\n    title: \"Dossier Technique\",\r\n    shortDescription: \"Études et validation technique nécessaires pour les projets fonciers.\",\r\n    detailedDescription: \"Un dossier technique est un document essentiel lors de l'acquisition d'un terrain. Il contient des informations cruciales telles que la délimitation précise du terrain, les éventuelles servitudes et le certificat de propriété de la parcelle. Ce dossier est établi par un géomètre agréé et permet de garantir l'exactitude des dimensions et des limites du terrain. \",\r\n    category: \"Études techniques\",\r\n    estimatedDuration: \"2-4 mois\",\r\n    complexity: \"Modéré\",\r\n    featured: false,\r\n    prestations: [\r\n      { title: \"Bornage et délimitation officielle\", description: \"Mise en place ou vérification des bornes de votre terrain par un géomètre assermenté pour définir ses limites exactes.\" },\r\n      { title: \"Établissement du plan topographique\", description: \"Création d'un plan détaillé de votre parcelle incluant les caractéristiques du terrain et des environs.\" },\r\n      { title: \"Rédaction du procès-verbal de bornage\", description: \"Formalisation du document officiel attestant des limites du terrain, signé par toutes les parties concernées.\" },\r\n      { title: \"Obtention du certificat de propriété\", description: \"Vérification et obtention des documents prouvant la propriété et les éventuelles charges foncières.\" },\r\n      { title: \"Validation et enregistrement cadastral\", description: \"Dépôt et suivi du dossier auprès des services du Cadastre pour son approbation et son enregistrement.\" }\r\n    ],\r\n    advantages: [\r\n      { title: \"Sécurisation de vos acquisitions foncières\", description: \"Assure l'exactitude des dimensions et limites du terrain, évitant les surprises et les litiges post-achat.\" },\r\n      { title: \"Clarté des limites de propriété\", description: \"Définit sans équivoque les frontières de votre parcelle, prévenant les conflits avec les voisins.\" },\r\n      { title: \"Conformité légale et réglementaire\", description: \"Indispensable pour toute transaction ou projet foncier, garantissant la validité juridique de vos démarches.\" },\r\n      { title: \"Facilite les démarches ultérieures\", description: \"Un dossier technique à jour et validé simplifie les demandes de permis de construire, morcellement, ou vente.\" }\r\n    ],\r\n    whenToCall: [\r\n      \"Vous envisagez d'acheter ou de vendre un terrain et avez besoin de la certitude de ses limites et de sa superficie.\",\r\n      \"Vous souhaitez construire sur votre terrain et avez besoin d'un plan précis et conforme.\",\r\n      \"Vous avez des doutes ou des désaccords avec un voisin concernant les limites de votre propriété.\",\r\n      \"Vous préparez un dossier pour l'obtention d'un titre foncier ou pour un morcellement.\"\r\n    ]\r\n  },\r\n  {\r\n    id: \"3\",\r\n    slug: \"immatriculation-directe\",\r\n    title: \"Immatriculation Directe\",\r\n    shortDescription: \"Nous facilitons l'immatriculation directe de votre terrain occupé pour sécuriser votre bien.\",\r\n    detailedDescription: \"L’immatriculation directe est un processus administratif qui permet d’obtenir un titre foncier ( une reconnaissance officielle des droits sur le terrain.) pour un terrain appartenant au domaine national de première catégorie (occupé avant le 5 août 1974), ou dont l’occupation antérieure a été mise en valeur.\",\r\n    category: \"Immobilier\",\r\n    estimatedDuration: \"3-6 mois\",\r\n    complexity: \"Modéré\",\r\n    featured: true,\r\n    prestations: [\r\n      { title: \"Constitution complète du dossier de demande\", description: \"Collecte, vérification et organisation de tous les documents requis (demande timbrée, croquis, attestations d'occupation, etc.).\" },\r\n      { title: \"Dépôt et suivi diligent de la procédure\", description: \"Enregistrement de votre dossier auprès des services fonciers et suivi rigoureux à chaque étape administrative.\" },\r\n      { title: \"Accompagnement lors de la descente de la commission consultative\", description: \"Préparation et assistance durant la visite de la commission chargée de constater l'occupation et la mise en valeur de votre terrain.\" },\r\n      { title: \"Gestion des publications légales et délais d'opposition\", description: \"Veille et suivi des publications officielles, et gestion des éventuelles oppositions dans les délais légaux.\" },\r\n      { title: \"Formalités de bornage et paiement des redevances\", description: \"Prise en charge des démarches de bornage et gestion des paiements des taxes et redevances foncières.\" }\r\n    ],\r\n    advantages: [\r\n      { title: \"Obtention d'un Titre Foncier définitif\", description: \"Votre propriété est reconnue officiellement et devient inattaquable, vous conférant des droits absolus.\" },\r\n      { title: \"Sécurisation juridique de votre patrimoine\", description: \"Protège votre bien contre les spoliations, les litiges et les revendications de tiers, assurant une possession paisible.\" },\r\n      { title: \"Valorisation de votre bien immobilier\", description: \"Un terrain titré prend de la valeur et est plus facile à vendre, à léguer ou à utiliser comme garantie.\" },\r\n      { title: \"Accès facilité aux services bancaires\", description: \"Le titre foncier est une garantie acceptée par les banques pour l'obtention de crédits et de financements.\" }\r\n    ],\r\n    whenToCall: [\r\n      \"Vous occupez un terrain non titré depuis de nombreuses années (avant le 5 août 1974) et l'avez mis en valeur de manière significative.\",\r\n      \"Vous avez hérité d'un terrain dont le statut foncier n'a jamais été régularisé et vous souhaitez en devenir le propriétaire officiel.\",\r\n      \"Vous désirez vendre votre terrain, mais celui-ci n'est pas encore titré et vous voulez le sécuriser pour la transaction.\"\r\n    ]\r\n  },\r\n  {\r\n    id: \"4\",\r\n    slug: \"achat-terrain-non-titre\",\r\n    title: \"Achat de Terrain Non Titré\",\r\n    shortDescription: \" Acquérez un terrain non immatriculé en toute sécurité.\",\r\n    detailedDescription: \"L’achat d’un terrain non titré comporte des risques. Nous vérifions les antécédents fonciers, sécurisons la transaction et vous accompagnons dans les démarches administratives pour obtenir un titre foncier. \",\r\n    category: \"Transaction\",\r\n    estimatedDuration: \"2-5 mois\",\r\n    complexity: \"Modéré\",\r\n    featured: false,\r\n    prestations: [\r\n      { title: \"Audit foncier approfondi et vérification des antécédents\", description: \"Recherche minutieuse de l'historique du terrain, des propriétaires précédents et identification des risques de litiges ou de doubles ventes.\" },\r\n      { title: \"Assistance à la négociation et rédaction des actes de cession\", description: \"Accompagnement dans les discussions avec le vendeur et élaboration des documents juridiques pertinents (convention de vente, abandon de droits coutumiers).\" },\r\n      { title: \"Obtention des visas administratifs nécessaires\", description: \"Démarches pour l'obtention des autorisations requises, notamment pour les acquéreurs étrangers ou non-nationaux.\" },\r\n      { title: \"Légalisation et authentification des documents\", description: \"Validation des actes de vente auprès des autorités locales (chefferies, sous-préfectures) pour leur conférer une force probante.\" },\r\n      { title: \"Accompagnement vers l'immatriculation du terrain\", description: \"Prise en charge des démarches post-acquisition pour transformer le terrain non titré en un bien doté d'un titre foncier.\" }\r\n    ],\r\n    advantages: [\r\n      { title: \"Réduction drastique des risques d'arnaque\", description: \"Notre expertise vous protège des vendeurs frauduleux et des litiges coûteux après l'achat.\" },\r\n      { title: \"Sécurisation progressive de votre investissement\", description: \"Nous mettons en place des garanties juridiques pour protéger votre acquisition, même avant l'obtention du titre foncier.\" },\r\n      { title: \"Obtention d'un titre foncier à terme\", description: \"Notre accompagnement vise l'immatriculation de votre terrain, garantissant sa sécurité et sa valorisation à long terme.\" },\r\n      { title: \"Tranquillité d'esprit et confiance\", description: \"Vous achetez votre terrain en toute sérénité, sachant que toutes les précautions ont été prises pour protéger votre bien.\" }\r\n    ],\r\n    whenToCall: [\r\n      \"Vous avez trouvé un terrain à acquérir qui ne possède pas encore de titre foncier.\",\r\n      \"Vous souhaitez acheter un terrain et vous voulez être sûr d'éviter les problèmes juridiques et les arnaques foncières.\",\r\n      \"Vous êtes un investisseur ou un particulier cherchant à acquérir un terrain non titré et vous avez besoin d'un cadre légal sécurisé.\",\r\n      \"Vous désirez un accompagnement complet, de la vérification initiale à l'obtention du titre foncier.\"\r\n    ]\r\n  },\r\n  {\r\n    id: \"5\",\r\n    slug: \"concession-domaniale\",\r\n    title: \"Concession Domaniale\",\r\n    shortDescription: \"Nous vous guidons pour obtenir une concession temporaire ou définitive sur un terrain du domaine public ou national de 2nde catégorie.\",\r\n    detailedDescription: \"La concession domaniale au Cameroun est la procédure par laquelle l'État cède un terrain du domaine public ou national à une personne, pour un projet de développement, moyennant le respect d'un cahier des charges et le paiement de redevances foncières. Elle se déroule en deux phases : provisoire et définitive, et implique des démarches administratives auprès des services des domaines.\",\r\n    category: \"Domaine public\",\r\n    estimatedDuration: \"6-12 mois\",\r\n    complexity: \"Complexe\",\r\n    featured: true,\r\n    prestations: [\r\n      { title: \"Élaboration du dossier de concession provisoire\", description: \"Préparation minutieuse des documents requis, incluant le projet de mise en valeur détaillé et le plan de situation du terrain.\" },\r\n      { title: \"Suivi auprès des administrations (Préfecture, MINDCAF)\", description: \"Dépôt du dossier et suivi rigoureux auprès des services préfectoraux et du Ministère des Domaines pour l'obtention de l'arrêté de concession provisoire.\" },\r\n      { title: \"Accompagnement pour le constat de mise en valeur\", description: \"Préparation de votre dossier et de votre site pour la visite de la commission chargée de vérifier l'effectivité de votre projet.\" },\r\n      { title: \"Constitution du dossier de concession définitive\", description: \"Préparation des pièces et formalités pour la transformation de la concession provisoire en concession définitive, après validation de la mise en valeur.\" },\r\n      { title: \"Gestion des redevances et obligations contractuelles\", description: \"Calcul et gestion des paiements des redevances foncières et suivi du respect des clauses du cahier des charges.\" }\r\n    ],\r\n    advantages: [\r\n      { title: \"Accès à des terrains adaptés aux projets d'envergure\", description: \"Permet de développer des projets agricoles, industriels, ou d'infrastructures sur des superficies importantes du domaine national.\" },\r\n      { title: \"Sécurisation de l'usage du terrain sur le long terme\", description: \"La concession offre un cadre légal pour l'exploitation et la jouissance du terrain, avec la possibilité d'une immatriculation future.\" },\r\n      { title: \"Reconnaissance et soutien institutionnel\", description: \"L'obtention d'une concession prouve la validation et le soutien de l'État pour votre projet, facilitant d'autres démarches.\" },\r\n      { title: \"Possibilité de conversion en Titre Foncier\", description: \"Après la phase de mise en valeur et l'obtention de la concession définitive, le terrain peut être immatriculé à votre nom.\" }\r\n    ],\r\n    whenToCall: [\r\n      \"Vous avez un projet de développement (agricole, industriel, immobilier) qui nécessite une parcelle de grande taille, située sur le domaine public ou national de 2nde catégorie.\",\r\n      \"Vous souhaitez obtenir une autorisation légale d'occuper et d'exploiter un terrain appartenant à l'État.\",\r\n      \"Votre projet implique un engagement à mettre en valeur le terrain et à respecter un cahier des charges spécifique.\"\r\n    ]\r\n  },\r\n  {\r\n    id: \"6\",\r\n    slug: \"achat-gre-a-gre\",\r\n    title: \"Achat de Gré à Gré (Lotissement Domanial)\",\r\n    shortDescription: \"devenez propriétaire d'une parcelle issue du domaine privé de l'État\",\r\n    detailedDescription: \"L'achat d'un terrain par 'gré à gré' dans un lotissement domanial au Cameroun implique une procédure spécifique. Il s'agit d'une vente directe entre l'État (représenté par les services du domaine) et l'acquéreur, sans passer par une adjudication publique\",\r\n    category: \"Transaction\",\r\n    estimatedDuration: \"3-6 mois\",\r\n    complexity: \"Modéré\",\r\n    featured: false,\r\n    prestations: [\r\n      { title: \"Dépôt de la demande d'acquisition\", description: \"Préparation et soumission de votre demande d'attribution de terrain par vente de gré à gré au Ministre des Domaines, sous couvert du Préfet.\" },\r\n      { title: \"Obtention des autorisations préfectorales et ministérielles\", description: \"Suivi actif du dossier pour obtenir les arrêtés préfectoraux et les décisions ministérielles autorisant la vente directe.\" },\r\n      { title: \"Gestion des frais et redevances foncières\", description: \"Prise en charge du calcul et du paiement des frais d'ouverture de dossier et des redevances dues à l'État.\" },\r\n      { title: \"Établissement et approbation de l'acte de vente\", description: \"Rédaction de l'acte de vente officiel entre l'État et vous, et assurance de son approbation par les autorités compétentes.\" },\r\n      { title: \"Finalisation de l'obtention du titre foncier\", description: \"Accompagnement jusqu'à l'enregistrement de l'acte de vente et la délivrance du titre foncier définitif à votre nom.\" }\r\n    ],\r\n    advantages: [\r\n      { title: \"Acquisition sécurisée d'une propriété de l'État\", description: \"Vous devenez propriétaire d'un terrain dont l'origine est officielle, incontestable et garantie par l'État.\" },\r\n      { title: \"Processus d'acquisition simplifié et direct\", description: \"Évitez les complexités et les incertitudes des adjudications publiques, en optant pour une vente directe.\" },\r\n      { title: \"Accès à des parcelles dans des lotissements officiels\", description: \"Bénéficiez de terrains déjà viabilisés ou intégrés dans des plans d'urbanisme, facilitant vos projets de construction.\" },\r\n      { title: \"Titre Foncier définitif et garanti\", description: \"La procédure aboutit à l'obtention d'un titre foncier en bonne et due forme, sécurisant pleinement votre investissement.\" }\r\n    ],\r\n    whenToCall: [\r\n      \"Vous souhaitez acquérir une parcelle située dans un lotissement domanial (terrain appartenant au domaine privé de l'État).\",\r\n      \"Vous préférez une transaction directe et sécurisée avec l'État plutôt que de passer par des enchères publiques.\",\r\n      \"Vous recherchez un terrain dont la légitimité et l'historique sont garantis par l'administration foncière.\",\r\n      \"Vous avez un projet de construction ou d'investissement nécessitant un terrain officiel et facilement titrable.\"\r\n    ]\r\n  },\r\n  {\r\n    id: \"7\",\r\n    slug: \"rehabilitation-titres-fonciers\",\r\n    title: \"Réhabilitation des Titres Fonciers\",\r\n    shortDescription: \"Restauration de titres fonciers perdus, abîmés ou irréguliers\",\r\n    detailedDescription: \"La réhabilitation des titres fonciers est un processus visant à corriger les irrégularités ou erreurs dans les titres fonciers existants, afin d'assurer la légitimité des droits de propriété.\",\r\n    category: \"Procédures correctives\",\r\n    estimatedDuration: \"2-6 mois\",\r\n    complexity: \"Modéré\",\r\n    featured: false,\r\n    prestations: [\r\n      { title: \"Analyse approfondie des irrégularités\", description: \"Identification précise des erreurs, omissions, chevauchements ou cas de duplicata de votre titre foncier.\" },\r\n      { title: \"Constitution du dossier de recours/rectification\", description: \"Préparation des documents justificatifs, preuves et arguments juridiques pour corriger les anomalies de votre titre.\" },\r\n      { title: \"Dépôt et suivi auprès du MINDCAF et des tribunaux\", description: \"Enregistrement de la demande auprès des services des Domaines, Cadastre et Affaires Foncières, et suivi actif de la procédure, y compris les démarches judiciaires si nécessaire.\" },\r\n      { title: \"Rédaction des requêtes et plaidoyers\", description: \"Élaboration des correspondances officielles, mémoires et argumentaires pour défendre vos droits devant les autorités compétentes.\" },\r\n      { title: \"Obtention du titre foncier réhabilité/corrigé\", description: \"Accompagnement jusqu'à la délivrance d'un nouveau titre foncier, débarrassé de toute irrégularité et pleinement valide.\" }\r\n    ],\r\n    advantages: [\r\n      { title: \"Sécurité et validité juridique retrouvées\", description: \"Votre titre foncier redevient un document légalement inattaquable, garantissant vos droits de propriété.\" },\r\n      { title: \"Prévention des litiges et fraudes\", description: \"La correction des irrégularités élimine les risques de contestation, de double vente ou de spoliation de votre bien.\" },\r\n      { title: \"Valorisation de votre patrimoine immobilier\", description: \"Un titre foncier sain facilite grandement la vente, l'hypothèque ou la transmission de votre propriété.\" },\r\n      { title: \"Tranquillité d'esprit et confiance\", description: \"Vous retrouvez la sérénité en sachant que votre propriété est légalement protégée et sans défauts.\" }\r\n    ],\r\n    whenToCall: [\r\n      \"Votre titre foncier comporte des erreurs matérielles (faute de frappe, mauvaise description, superficie inexacte).\",\r\n      \"Vous constatez qu'un même terrain fait l'objet de plusieurs titres fonciers (duplicata).\",\r\n      \"Votre titre foncier a été perdu, volé, ou est illisible/endommagé.\",\r\n      \"Une décision administrative a retiré, annulé ou modifié votre titre foncier et vous souhaitez la contester.\"\r\n    ]\r\n  },\r\n  {\r\n    id: \"12\",\r\n    slug: \"mutation-par-deces\",\r\n    title: \"Mutation par Décès\",\r\n    shortDescription: \"Transférez à votre nom votre terrain hérité grâce à un accompagnement complet du dossier\",\r\n    detailedDescription: \"La mutation par décès désigne le transfert des droits de propriété d'un bien immobilier (immeuble ou terrain) d'une personne décédée à ses héritiers. Ce processus est essentiel pour régulariser la situation foncière après un décès et permettre aux héritiers d'exercer leurs droits sur les biens. \",\r\n    category: \"Succession\",\r\n    estimatedDuration: \"2-6 mois\",\r\n    complexity: \"Modéré\",\r\n    featured: true,\r\n    prestations: [\r\n      { title: \"Analyse complète de la succession\", description: \"Évaluation de la situation familiale, identification de tous les héritiers légaux et vérification des documents de succession (testament, état civil).\" },\r\n      { title: \"Obtention des actes d'hérédité et certificats\", description: \"Assistance pour l'obtention du jugement d'hérédité, du certificat de décès, du certificat de non-opposition et autres documents notariaux requis.\" },\r\n      { title: \"Constitution et dépôt du dossier de mutation\", description: \"Préparation exhaustive de tous les documents nécessaires et dépôt officiel auprès du Conservateur Foncier compétent.\" },\r\n      { title: \"Suivi des publications légales et délais\", description: \"Vérification des publications obligatoires au bulletin foncier et gestion des délais légaux pour les éventuelles oppositions.\" },\r\n      { title: \"Retrait du nouveau titre foncier\", description: \"Accompagnement jusqu'à la délivrance du titre foncier mis à jour, établi au nom des héritiers ou de l'ayant droit.\" }\r\n    ],\r\n    advantages: [\r\n      { title: \"Officialisation de la propriété pour les héritiers\", description: \"Les héritiers deviennent légalement propriétaires du bien, avec un titre foncier incontestable à leur nom.\" },\r\n      { title: \"Sécurisation juridique de l'héritage\", description: \"Protège le bien immobilier des litiges futurs, des spoliations et des revendications infondées, assurant une transmission paisible.\" },\r\n      { title: \"Facilitation de la gestion et de la vente du bien\", description: \"Un titre foncier à jour simplifie toute opération future, qu'il s'agisse de vendre, de louer, de morceler ou d'hypothéquer le terrain.\" },\r\n      { title: \"Prévention des conflits familiaux\", description: \"Une procédure transparente et légale aide à distribuer les biens conformément à la loi, minimisant les désaccords entre cohéritiers.\" }\r\n    ],\r\n    whenToCall: [\r\n      \"Le propriétaire d'un terrain ou d'un immeuble est décédé et le titre foncier est toujours à son nom.\",\r\n      \"Vous êtes un héritier et souhaitez formaliser légalement votre part de propriété sur le terrain hérité.\",\r\n      \"Vous envisagez de vendre, de partager ou de réaliser des opérations sur un bien immobilier issu d'une succession.\"\r\n    ]\r\n  },\r\n  {\r\n    id: \"8\",\r\n    slug: \"morcellement-mutation-achat\",\r\n    title: \"Morcellement & Mutation par Achat\",\r\n    shortDescription: \"Divisez votre terrain en parcelles distinctes et sécurisez la vente ou l'acquisition de chacune d'elles.\",\r\n    detailedDescription: \"Le morcellement implique la division d'un terrain en plusieurs lots, souvent pour la vente, tandis que la mutation par achat concerne le transfert de la propriété d'un terrain existant à un nouvel acquéreur. \",\r\n    category: \"Transaction\",\r\n    estimatedDuration: \"3-7 mois\",\r\n    complexity: \"Modéré\",\r\n    featured: false,\r\n    prestations: [\r\n      { title: \"Étude de faisabilité et élaboration du plan de morcellement\", description: \"Analyse des contraintes urbanistiques et techniques, et conception d'un plan de division qui maximise la valeur de votre terrain.\" },\r\n      { title: \"Bornage et délimitation des nouvelles parcelles\", description: \"Réalisation des opérations de bornage par un géomètre assermenté pour délimiter précisément chaque nouveau lot créé.\" },\r\n      { title: \"Constitution et validation du dossier technique de morcellement\", description: \"Préparation et dépôt de tous les documents techniques et administratifs requis auprès des services du Cadastre.\" },\r\n      { title: \"Rédaction et signature de l'acte de vente notarié\", description: \"Assistance pour la rédaction de l'acte authentique de vente de chaque parcelle et suivi de son enregistrement.\" },\r\n      { title: \"Suivi de la mutation et obtention des nouveaux titres fonciers\", description: \"Accompagnement jusqu'à la délivrance des titres fonciers individuels pour chaque parcelle morcelée et vendue.\" }\r\n    ],\r\n    advantages: [\r\n      { title: \"Optimisation de la valeur foncière de votre bien\", description: \"Diviser un grand terrain en lots plus petits augmente souvent sa valeur marchande et facilite la vente.\" },\r\n      { title: \"Sécurisation totale des transactions de vente\", description: \"Chaque parcelle vendue dispose de son propre titre foncier, offrant une sécurité maximale à l'acheteur et au vendeur.\" },\r\n      { title: \"Gestion simplifiée des propriétés\", description: \"Les nouvelles parcelles sont clairement définies et enregistrées, facilitant leur gestion individuelle.\" },\r\n      { title: \"Conformité légale et urbanistique\", description: \"Toutes les étapes du morcellement et de la mutation sont réalisées dans le strict respect de la réglementation en vigueur.\" }\r\n    ],\r\n    whenToCall: [\r\n      \"Vous êtes propriétaire d'un grand terrain et souhaitez le diviser pour vendre des parcelles séparées.\",\r\n      \"Vous avez acquis une portion d'un terrain plus grand et vous avez besoin d'un titre foncier distinct pour votre part.\",\r\n      \"Vous voulez sécuriser la transaction d'une parcelle nouvellement créée issue d'une division foncière.\",\r\n      \"Vous êtes un promoteur immobilier et souhaitez créer des lots constructibles à partir d'une grande parcelle.\"\r\n    ]\r\n  },\r\n  {\r\n    id: \"9\",\r\n    slug: \"retrocession\",\r\n    title: \"Rétrocession\",\r\n    shortDescription: \"Récupérez un terrain précédemment exproprié ou intégré dans le domaine public suite à une carence administrative ou un projet abandonné.\",\r\n    detailedDescription: \"la rétrocession se réfère au droit pour les anciens propriétaires ou leurs ayants droit de demander la restitution d'un terrain exproprié pour cause d'utilité publique, si ce terrain n'est pas utilisé conformément à la destination prévue dans le délai imparti\",\r\n    category: \"Procédures correctives\",\r\n    estimatedDuration: \"6-18 mois\",\r\n    complexity: \"Complexe\",\r\n    featured: false,\r\n    prestations: [\r\n      { title: \"Analyse de la recevabilité de la demande\", description: \"Vérification des motifs d'expropriation, du non-respect de l'affectation du terrain ou de l'abandon du projet public.\" },\r\n      { title: \"Constitution du dossier de rétrocession\", description: \"Préparation des documents justificatifs prouvant l'expropriation et la non-utilisation du terrain conformément à l'objectif initial.\" },\r\n      { title: \"Dépôt et suivi des recours administratifs\", description: \"Enregistrement de la demande de rétrocession auprès des autorités compétentes et suivi rigoureux du processus.\" },\r\n      { title: \"Négociation et représentation\", description: \"Défense de vos intérêts et dialogue avec l'administration pour parvenir à la restitution de votre terrain.\" },\r\n      { title: \"Accompagnement en cas de contentieux\", description: \"Soutien juridique et assistance si la procédure nécessite une action devant les tribunaux pour faire valoir vos droits.\" }\r\n    ],\r\n    advantages: [\r\n      { title: \"Récupération de votre propriété légitime\", description: \"Vous retrouvez la pleine jouissance d'un bien foncier qui vous a été retiré ou dont l'usage n'a pas été respecté.\" },\r\n      { title: \"Réparation d'un préjudice foncier\", description: \"Obtenez justice et compensation pour la perte ou l'usage abusif de votre terrain par l'administration.\" },\r\n      { title: \"Sécurisation de vos droits fonciers\", description: \"Le terrain est officiellement réintégré dans votre patrimoine, avec toutes les garanties juridiques.\" },\r\n      { title: \"Rétablissement de la justice administrative\", description: \"Faire valoir vos droits face aux manquements ou abus de l'administration, renforçant la sécurité foncière.\" }\r\n    ],\r\n    whenToCall: [\r\n      \"Votre terrain a été exproprié pour un projet public qui n'a pas été réalisé, a été abandonné, ou n'est pas utilisé conformément à l'objectif initial.\",\r\n      \"Vous estimez avoir subi un préjudice foncier suite à une décision administrative contestable ou non respectée.\",\r\n      \"Vous souhaitez faire valoir votre droit à la restitution d'un bien foncier intégré au domaine public sans juste cause ou compensation.\"\r\n    ]\r\n  },\r\n  {\r\n    id: \"10\",\r\n    slug: \"bornage-reconstitution\",\r\n    title: \"Bornage et Reconstitution des Bornes\",\r\n    shortDescription: \"Identifiez et délimitez précisément les limites de votre terrain avec l'aide d'un géomètre assermenté.\",\r\n    detailedDescription: \"le bornage et la reconstitution des bornes sont des procédures cadastrales importantes pour délimiter les propriétés foncières et éviter les conflits de voisinage. Le bornage consiste à fixer les limites d'un terrain à l'aide de bornes, tandis que la reconstitution concerne la remise en place de bornes manquantes ou endommagées.\",\r\n    category: \"Délimitation\",\r\n    estimatedDuration: \"1-2 mois\",\r\n    complexity: \"Modéré\",\r\n    featured: false,\r\n    prestations: [\r\n      { title: \"Analyse documentaire et cadastrale\", description: \"Étude des titres de propriété, plans cadastraux et autres documents pour établir les points de repère et les limites existantes.\" },\r\n      { title: \"Intervention d'un géomètre assermenté sur site\", description: \"Descente sur le terrain avec un expert pour identifier, mesurer et matérialiser les limites précises par la pose de bornes officielles.\" },\r\n      { title: \"Rédaction et homologation du procès-verbal de bornage\", description: \"Établissement du document officiel décrivant les opérations de bornage et les limites fixées, signé par toutes les parties et enregistré.\" },\r\n      { title: \"Gestion des notifications aux propriétaires voisins\", description: \"Information des propriétaires des parcelles contiguës et gestion des éventuelles observations ou contestations.\" },\r\n      { title: \"Conseils pour la prévention et la résolution des litiges\", description: \"Accompagnement pour prévenir ou résoudre à l'amiable les conflits de voisinage liés aux limites foncières.\" }\r\n    ],\r\n    advantages: [\r\n      { title: \"Délimitation claire et incontestable de votre propriété\", description: \"Élimine les incertitudes sur les frontières de votre terrain, évitant les empiètements et les malentendus.\" },\r\n      { title: \"Sécurité juridique de vos limites foncières\", description: \"Le procès-verbal de bornage est un document officiel qui a force probante, protégeant vos droits de propriété.\" },\r\n      { title: \"Prévention et résolution des conflits de voisinage\", description: \"Des limites clairement définies favorisent de bonnes relations et évitent les litiges coûteux avec les propriétés adjacentes.\" },\r\n      { title: \"Valorisation de votre bien immobilier\", description: \"Un terrain clairement borné est plus attractif et sécurisant pour les futurs acquéreurs ou pour tout projet de développement.\" }\r\n    ],\r\n    whenToCall: [\r\n      \"Vous avez des doutes sur les limites exactes de votre propriété ou de celles de vos voisins.\",\r\n      \"Votre terrain n'a jamais été officiellement borné ou les bornes existantes sont manquantes, déplacées ou endommagées.\",\r\n      \"Vous envisagez de construire une clôture, un mur ou de réaliser des aménagements importants en limite de propriété.\",\r\n      \"Vous préparez la vente ou l'acquisition d'un terrain et vous souhaitez garantir la précision de sa superficie et de ses limites.\"\r\n    ]\r\n  },\r\n  {\r\n    id: \"11\",\r\n    slug: \"indemnisation\",\r\n    title: \"Indemnisation\",\r\n    shortDescription: \"Obtenez une compensation équitable en cas d'expropriation ou de préjudice foncier.\",\r\n    detailedDescription: \"L'indemnisation vise à compenser les pertes subies par les individus du fait de la perte de leurs terres ou constructions. \",\r\n    category: \"Procédures correctives\",\r\n    estimatedDuration: \"6-12 mois\",\r\n    complexity: \"Complexe\",\r\n    featured: false,\r\n    prestations: [\r\n      { title: \"Évaluation du préjudice foncier et des droits\", description: \"Analyse approfondie de la situation pour déterminer la nature et l'étendue de votre préjudice et estimer la juste compensation à réclamer.\" },\r\n      { title: \"Constitution et dépôt du dossier d'indemnisation\", description: \"Préparation exhaustive des documents justificatifs (titre foncier, plans, preuves de mise en valeur, etc.) pour appuyer votre demande.\" },\r\n      { title: \"Négociation avec l'administration ou les entités expropriatrices\", description: \"Défense active de vos intérêts et recherche d'un accord amiable sur le montant de l'indemnité.\" },\r\n      { title: \"Suivi devant les commissions d'évaluation et les tribunaux\", description: \"Accompagnement et représentation lors des audiences des commissions d'évaluation et, si nécessaire, devant les juridictions compétentes.\" },\r\n      { title: \"Assistance au paiement et à la réception de l'indemnité\", description: \"Veille au bon déroulement du processus de paiement et à la réception effective de la compensation due.\" }\r\n    ],\r\n    advantages: [\r\n      { title: \"Obtention d'une indemnisation juste et équitable\", description: \"Nous veillons à ce que la compensation versée reflète la valeur réelle de votre bien et les préjudices subis (pertes de revenus, de jouissance, etc.).\" },\r\n      { title: \"Défense experte de vos droits de propriétaire\", description: \"Vous êtes accompagné par des professionnels aguerris face aux procédures d'expropriation souvent complexes et intimidantes.\" },\r\n      { title: \"Accélération du processus d'indemnisation\", description: \"Notre connaissance des procédures et des acteurs réduit les délais d'attente pour l'obtention de votre compensation.\" },\r\n      { title: \"Sécurisation juridique de vos recours\", description: \"Nous garantissons la conformité de vos démarches et la protection de vos droits face à des montants jugés insuffisants ou injustes.\" }\r\n    ],\r\n    whenToCall: [\r\n      \"Votre terrain, votre maison ou vos biens sont menacés d'expropriation pour cause d'utilité publique.\",\r\n      \"Vous avez subi un préjudice lié à un projet public (construction d'une route, d'une infrastructure) qui affecte votre propriété.\",\r\n      \"Le montant d'indemnisation proposé par l'administration est insuffisant, contestable ou ne correspond pas à la valeur de votre bien.\"\r\n    ]\r\n  },\r\n  {\r\n    id: \"13\",\r\n    slug: \"lotissement\",\r\n    title: \"Lotissement\",\r\n    shortDescription: \"Création et aménagement de lotissements en toute conformité\",\r\n    detailedDescription: \"Le lotissement consiste en la division d’un terrain pour créer des parcelles destinées à la vente ou à l’aménagement. Il nécessite une étude de faisabilité, un plan d’implantation, des autorisations urbanistiques, et un suivi des travaux. Charlie Oscar Consulting vous accompagne de la planification à l’obtention de toutes les autorisations nécessaires.\",\r\n    category: \"Aménagement\",\r\n    estimatedDuration: \"6-12 mois\",\r\n    complexity: \"Modéré\",\r\n    featured: false,\r\n    prestations: [\r\n      { title: \"Étude de faisabilité technique et réglementaire\", description: \"Analyse approfondie du terrain, des règles d'urbanisme et des potentialités de division pour votre projet de lotissement.\" },\r\n      { title: \"Conception du plan de lotissement et d'aménagement\", description: \"Élaboration de plans détaillés pour la division des parcelles, la création de voies d'accès, d'espaces verts et d'infrastructures.\" },\r\n      { title: \"Obtention des autorisations urbanistiques (permis de lotir)\", description: \"Dépôt et suivi des demandes de permis et autorisations nécessaires auprès des services d'urbanisme.\" },\r\n      { title: \"Coordination des travaux d'aménagement\", description: \"Accompagnement dans la supervision des travaux de viabilisation (voies, eau, électricité) conformément au plan approuvé.\" },\r\n      { title: \"Suivi jusqu'à la réception définitive du lotissement\", description: \"Assistance pour les démarches finales, y compris la réception des travaux et l'intégration au domaine public des équipements communs.\" }\r\n    ],\r\n    advantages: [\r\n      { title: \"Création de parcelles valorisables et attractives\", description: \"Divisez votre terrain en lots optimisés pour la vente, augmentant significativement leur valeur marchande.\" },\r\n      { title: \"Conformité totale avec les normes urbanistiques\", description: \"Votre lotissement est réalisé dans le respect strict des réglementations, évitant tout risque de blocage ou de sanction.\" },\r\n      { title: \"Sécurité juridique pour les futurs acquéreurs\", description: \"Les lots sont créés avec toutes les garanties légales, facilitant leur vente et l'obtention de titres fonciers individuels.\" },\r\n      { title: \"Gestion simplifiée de projets complexes\", description: \"Un accompagnement expert qui prend en charge toutes les étapes, de la conception à la commercialisation des lots.\" }\r\n    ],\r\n    whenToCall: [\r\n      \"Vous possédez un grand terrain et souhaitez le diviser en plusieurs parcelles constructibles pour les vendre.\",\r\n      \"Vous êtes un promoteur immobilier et avez un projet de développement résidentiel ou commercial nécessitant la création d'un lotissement.\",\r\n      \"Vous avez besoin d'une assistance complète pour les études, les plans, les autorisations et le suivi des travaux d'aménagement d'un terrain.\"\r\n    ]\r\n  },\r\n  {\r\n    id: \"15\",\r\n    slug: \"recours-gracieux\",\r\n    title: \"Recours Gracieux\",\r\n    shortDescription: \"Demande de réexamen administratif pour vos contentieux fonciers\",\r\n    detailedDescription: \"Un recours gracieux est une procédure administrative par laquelle une personne demande à l'administration de reconsidérer une décision qu'elle a prise. C'est une étape préalable obligatoire avant de saisir le juge administratif dans de nombreux cas. Le recours gracieux est un outil de droit public qui permet de résoudre amiablement des litiges administratifs. \",\r\n    category: \"Procédures correctives\",\r\n    estimatedDuration: \"3-6 mois\",\r\n    complexity: \"Modéré\",\r\n    featured: false,\r\n    prestations: [\r\n      { title: \"Analyse approfondie de la décision contestée\", description: \"Évaluation de la légalité et du bien-fondé de la décision administrative que vous souhaitez contester.\" },\r\n      { title: \"Rédaction et formalisation de la requête gracieuse\", description: \"Élaboration d'un recours détaillé et argumenté, incluant toutes les pièces justificatives et les arguments juridiques pertinents.\" },\r\n      { title: \"Dépôt et suivi proactif auprès du Ministère compétent\", description: \"Enregistrement de votre recours au MINDCAF et suivi régulier auprès de la cellule contentieux pour accélérer son traitement.\" },\r\n      { title: \"Négociation et échange avec l'administration\", description: \"Dialogue avec les services concernés pour trouver une solution amiable et obtenir la révision de la décision.\" },\r\n      { title: \"Préparation à un éventuel recours contentieux\", description: \"Si le recours gracieux n'aboutit pas, nous préparons les bases pour une action devant le juge administratif.\" }\r\n    ],\r\n    advantages: [\r\n      { title: \"Solution amiable et rapide\", description: \"Permet de résoudre un litige avec l'administration sans passer par une procédure judiciaire longue et coûteuse.\" },\r\n      { title: \"Préservation de vos droits fonciers\", description: \"Conteste une décision administrative qui affecte votre titre foncier ou vos droits sur un bien immobilier.\" },\r\n      { title: \"Expertise des procédures administratives\", description: \"Nous maîtrisons les exigences formelles et les circuits administratifs pour optimiser vos chances de succès.\" },\r\n      { title: \"Préparation à des actions plus poussées\", description: \"Même en cas d'échec, le recours gracieux est une étape obligatoire qui renforce votre position pour un recours contentieux.\" }\r\n    ],\r\n    whenToCall: [\r\n      \"Un titre foncier vous a été retiré, annulé ou modifié par l'administration sans motif valable.\",\r\n      \"Votre demande d'immatriculation, de concession ou toute autre démarche foncière a été refusée de manière contestable.\",\r\n      \"Vous souhaitez contester une décision administrative relative à un bien foncier sans engager immédiatement une procédure judiciaire.\",\r\n      \"Vous avez subi un préjudice suite à un acte administratif illégal et vous cherchez une réparation.\"\r\n    ]\r\n  }\r\n];\r\n\r\nexport function getFoncierServiceBySlug(slug: string): FoncierService | undefined {\r\n  return foncierServices.find(service => service.slug === slug);\r\n}\r\n\r\nexport function getAllFoncierServices(): FoncierService[] {\r\n  return foncierServices;\r\n}\r\n\r\nexport function getFeaturedFoncierServices(): FoncierService[] {\r\n  return foncierServices.filter(service => service.featured);\r\n}\r\n\r\nexport function getFoncierServicesByCategory(category: string): FoncierService[] {\r\n  return foncierServices.filter(service => service.category === category);\r\n}\r\n"], "names": [], "mappings": ";;;;;;;AA2CO,MAAM,kBAAoC;IAC/C;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,kBAAkB;QAClB,qBAAqB;QACrB,UAAU;QACV,mBAAmB;QACnB,YAAY;QACZ,UAAU;QACV,aAAa;YACX;gBAAE,OAAO;gBAAwC,aAAa;YAA+I;YAC7M;gBAAE,OAAO;gBAAgD,aAAa;YAA2J;YACjO;gBAAE,OAAO;gBAAkD,aAAa;YAA+J;YACvO;gBAAE,OAAO;gBAAmD,aAAa;YAA4G;YACrL;gBAAE,OAAO;gBAAuC,aAAa;YAAmI;SACjM;QACD,YAAY;YACV;gBAAE,OAAO;gBAAkD,aAAa;YAA+G;YACvL;gBAAE,OAAO;gBAA8C,aAAa;YAAwH;YAC5L;gBAAE,OAAO;gBAA2D,aAAa;YAA0F;YAC3K;gBAAE,OAAO;gBAAsC,aAAa;YAAmG;SAChK;QACD,YAAY;YACV;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,kBAAkB;QAClB,qBAAqB;QACrB,UAAU;QACV,mBAAmB;QACnB,YAAY;QACZ,UAAU;QACV,aAAa;YACX;gBAAE,OAAO;gBAAsC,aAAa;YAAyH;YACrL;gBAAE,OAAO;gBAAuC,aAAa;YAA0G;YACvK;gBAAE,OAAO;gBAAyC,aAAa;YAAgH;YAC/K;gBAAE,OAAO;gBAAwC,aAAa;YAAsG;YACpK;gBAAE,OAAO;gBAA0C,aAAa;YAAwG;SACzK;QACD,YAAY;YACV;gBAAE,OAAO;gBAA8C,aAAa;YAA6G;YACjL;gBAAE,OAAO;gBAAmC,aAAa;YAAoG;YAC7J;gBAAE,OAAO;gBAAsC,aAAa;YAA+G;YAC3K;gBAAE,OAAO;gBAAsC,aAAa;YAAgH;SAC7K;QACD,YAAY;YACV;YACA;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,kBAAkB;QAClB,qBAAqB;QACrB,UAAU;QACV,mBAAmB;QACnB,YAAY;QACZ,UAAU;QACV,aAAa;YACX;gBAAE,OAAO;gBAA+C,aAAa;YAAmI;YACxM;gBAAE,OAAO;gBAA2C,aAAa;YAAiH;YAClL;gBAAE,OAAO;gBAAoE,aAAa;YAAuI;YACjO;gBAAE,OAAO;gBAA2D,aAAa;YAA+G;YAChM;gBAAE,OAAO;gBAAoD,aAAa;YAAuG;SAClL;QACD,YAAY;YACV;gBAAE,OAAO;gBAA0C,aAAa;YAA0G;YAC1K;gBAAE,OAAO;gBAA8C,aAAa;YAA2H;YAC/L;gBAAE,OAAO;gBAAyC,aAAa;YAA0G;YACzK;gBAAE,OAAO;gBAAyC,aAAa;YAA6G;SAC7K;QACD,YAAY;YACV;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,kBAAkB;QAClB,qBAAqB;QACrB,UAAU;QACV,mBAAmB;QACnB,YAAY;QACZ,UAAU;QACV,aAAa;YACX;gBAAE,OAAO;gBAA4D,aAAa;YAA+I;YACjO;gBAAE,OAAO;gBAAiE,aAAa;YAA8J;YACrP;gBAAE,OAAO;gBAAkD,aAAa;YAAmH;YAC3L;gBAAE,OAAO;gBAAkD,aAAa;YAAmI;YAC3M;gBAAE,OAAO;gBAAoD,aAAa;YAA2H;SACtM;QACD,YAAY;YACV;gBAAE,OAAO;gBAA6C,aAAa;YAA6F;YAChK;gBAAE,OAAO;gBAAoD,aAAa;YAA2H;YACrM;gBAAE,OAAO;gBAAwC,aAAa;YAA0H;YACxL;gBAAE,OAAO;gBAAsC,aAAa;YAA4H;SACzL;QACD,YAAY;YACV;YACA;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,kBAAkB;QAClB,qBAAqB;QACrB,UAAU;QACV,mBAAmB;QACnB,YAAY;QACZ,UAAU;QACV,aAAa;YACX;gBAAE,OAAO;gBAAmD,aAAa;YAAiI;YAC1M;gBAAE,OAAO;gBAA0D,aAAa;YAA2J;YAC3O;gBAAE,OAAO;gBAAoD,aAAa;YAAmI;YAC7M;gBAAE,OAAO;gBAAoD,aAAa;YAA2J;YACrO;gBAAE,OAAO;gBAAwD,aAAa;YAAkH;SACjM;QACD,YAAY;YACV;gBAAE,OAAO;gBAAwD,aAAa;YAAqI;YACnN;gBAAE,OAAO;gBAAwD,aAAa;YAAwI;YACtN;gBAAE,OAAO;gBAA4C,aAAa;YAA8H;YAChM;gBAAE,OAAO;gBAA8C,aAAa;YAA6H;SAClM;QACD,YAAY;YACV;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,kBAAkB;QAClB,qBAAqB;QACrB,UAAU;QACV,mBAAmB;QACnB,YAAY;QACZ,UAAU;QACV,aAAa;YACX;gBAAE,OAAO;gBAAqC,aAAa;YAA+I;YAC1M;gBAAE,OAAO;gBAA+D,aAAa;YAA4H;YACjN;gBAAE,OAAO;gBAA6C,aAAa;YAA6G;YAChL;gBAAE,OAAO;gBAAmD,aAAa;YAA6H;YACtM;gBAAE,OAAO;gBAAgD,aAAa;YAAsH;SAC7L;QACD,YAAY;YACV;gBAAE,OAAO;gBAAmD,aAAa;YAA8G;YACvL;gBAAE,OAAO;gBAA+C,aAAa;YAA4G;YACjL;gBAAE,OAAO;gBAAyD,aAAa;YAAyH;YACxM;gBAAE,OAAO;gBAAsC,aAAa;YAA2H;SACxL;QACD,YAAY;YACV;YACA;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,kBAAkB;QAClB,qBAAqB;QACrB,UAAU;QACV,mBAAmB;QACnB,YAAY;QACZ,UAAU;QACV,aAAa;YACX;gBAAE,OAAO;gBAAyC,aAAa;YAA4G;YAC3K;gBAAE,OAAO;gBAAoD,aAAa;YAAuH;YACjM;gBAAE,OAAO;gBAAqD,aAAa;YAAoL;YAC/P;gBAAE,OAAO;gBAAwC,aAAa;YAAoI;YAClM;gBAAE,OAAO;gBAAiD,aAAa;YAA0H;SAClM;QACD,YAAY;YACV;gBAAE,OAAO;gBAA6C,aAAa;YAA2G;YAC9K;gBAAE,OAAO;gBAAqC,aAAa;YAAuH;YAClL;gBAAE,OAAO;gBAA+C,aAAa;YAA0G;YAC/K;gBAAE,OAAO;gBAAsC,aAAa;YAAqG;SAClK;QACD,YAAY;YACV;YACA;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,kBAAkB;QAClB,qBAAqB;QACrB,UAAU;QACV,mBAAmB;QACnB,YAAY;QACZ,UAAU;QACV,aAAa;YACX;gBAAE,OAAO;gBAAqC,aAAa;YAAyJ;YACpN;gBAAE,OAAO;gBAAiD,aAAa;YAAoJ;YAC3N;gBAAE,OAAO;gBAAgD,aAAa;YAAuH;YAC7L;gBAAE,OAAO;gBAA4C,aAAa;YAAgI;YAClM;gBAAE,OAAO;gBAAoC,aAAa;YAAqH;SAChL;QACD,YAAY;YACV;gBAAE,OAAO;gBAAsD,aAAa;YAA6G;YACzL;gBAAE,OAAO;gBAAwC,aAAa;YAAsI;YACpM;gBAAE,OAAO;gBAAqD,aAAa;YAAyI;YACpN;gBAAE,OAAO;gBAAqC,aAAa;YAAuI;SACnM;QACD,YAAY;YACV;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,kBAAkB;QAClB,qBAAqB;QACrB,UAAU;QACV,mBAAmB;QACnB,YAAY;QACZ,UAAU;QACV,aAAa;YACX;gBAAE,OAAO;gBAA+D,aAAa;YAAoI;YACzN;gBAAE,OAAO;gBAAmD,aAAa;YAAuH;YAChM;gBAAE,OAAO;gBAAmE,aAAa;YAAkH;YAC3M;gBAAE,OAAO;gBAAqD,aAAa;YAAiH;YAC5L;gBAAE,OAAO;gBAAkE,aAAa;YAAgH;SACzM;QACD,YAAY;YACV;gBAAE,OAAO;gBAAoD,aAAa;YAA0G;YACpL;gBAAE,OAAO;gBAAiD,aAAa;YAAwH;YAC/L;gBAAE,OAAO;gBAAqC,aAAa;YAA0G;YACrK;gBAAE,OAAO;gBAAqC,aAAa;YAA6H;SACzL;QACD,YAAY;YACV;YACA;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,kBAAkB;QAClB,qBAAqB;QACrB,UAAU;QACV,mBAAmB;QACnB,YAAY;QACZ,UAAU;QACV,aAAa;YACX;gBAAE,OAAO;gBAA4C,aAAa;YAAwH;YAC1L;gBAAE,OAAO;gBAA2C,aAAa;YAAuI;YACxM;gBAAE,OAAO;gBAA6C,aAAa;YAAiH;YACpL;gBAAE,OAAO;gBAAiC,aAAa;YAA6G;YACpK;gBAAE,OAAO;gBAAwC,aAAa;YAA0H;SACzL;QACD,YAAY;YACV;gBAAE,OAAO;gBAA4C,aAAa;YAAoH;YACtL;gBAAE,OAAO;gBAAqC,aAAa;YAAyG;YACpK;gBAAE,OAAO;gBAAuC,aAAa;YAAuG;YACpK;gBAAE,OAAO;gBAA+C,aAAa;YAA6G;SACnL;QACD,YAAY;YACV;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,kBAAkB;QAClB,qBAAqB;QACrB,UAAU;QACV,mBAAmB;QACnB,YAAY;QACZ,UAAU;QACV,aAAa;YACX;gBAAE,OAAO;gBAAsC,aAAa;YAAmI;YAC/L;gBAAE,OAAO;gBAAkD,aAAa;YAA0I;YAClN;gBAAE,OAAO;gBAAyD,aAAa;YAA4I;YAC3N;gBAAE,OAAO;gBAAuD,aAAa;YAAkH;YAC/L;gBAAE,OAAO;gBAA4D,aAAa;YAA6G;SAChM;QACD,YAAY;YACV;gBAAE,OAAO;gBAA2D,aAAa;YAA6G;YAC9L;gBAAE,OAAO;gBAA+C,aAAa;YAAiH;YACtL;gBAAE,OAAO;gBAAsD,aAAa;YAAgI;YAC5M;gBAAE,OAAO;gBAAyC,aAAa;YAAgI;SAChM;QACD,YAAY;YACV;YACA;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,kBAAkB;QAClB,qBAAqB;QACrB,UAAU;QACV,mBAAmB;QACnB,YAAY;QACZ,UAAU;QACV,aAAa;YACX;gBAAE,OAAO;gBAAiD,aAAa;YAA6I;YACpN;gBAAE,OAAO;gBAAoD,aAAa;YAAyI;YACnN;gBAAE,OAAO;gBAAoE,aAAa;YAAiG;YAC3L;gBAAE,OAAO;gBAA8D,aAAa;YAA2I;YAC/N;gBAAE,OAAO;gBAA2D,aAAa;YAAyG;SAC3L;QACD,YAAY;YACV;gBAAE,OAAO;gBAAoD,aAAa;YAAyJ;YACnO;gBAAE,OAAO;gBAAiD,aAAa;YAA8H;YACrM;gBAAE,OAAO;gBAA6C,aAAa;YAAuH;YAC1L;gBAAE,OAAO;gBAAyC,aAAa;YAAsI;SACtM;QACD,YAAY;YACV;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,kBAAkB;QAClB,qBAAqB;QACrB,UAAU;QACV,mBAAmB;QACnB,YAAY;QACZ,UAAU;QACV,aAAa;YACX;gBAAE,OAAO;gBAAmD,aAAa;YAA4H;YACrM;gBAAE,OAAO;gBAAsD,aAAa;YAAqI;YACjN;gBAAE,OAAO;gBAA+D,aAAa;YAAsG;YAC3L;gBAAE,OAAO;gBAA0C,aAAa;YAA2H;YAC3L;gBAAE,OAAO;gBAAwD,aAAa;YAAwI;SACvN;QACD,YAAY;YACV;gBAAE,OAAO;gBAAqD,aAAa;YAA6G;YACxL;gBAAE,OAAO;gBAAmD,aAAa;YAA2H;YACpM;gBAAE,OAAO;gBAAiD,aAAa;YAA8H;YACrM;gBAAE,OAAO;gBAA2C,aAAa;YAAoH;SACtL;QACD,YAAY;YACV;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,kBAAkB;QAClB,qBAAqB;QACrB,UAAU;QACV,mBAAmB;QACnB,YAAY;QACZ,UAAU;QACV,aAAa;YACX;gBAAE,OAAO;gBAAgD,aAAa;YAAyG;YAC/K;gBAAE,OAAO;gBAAsD,aAAa;YAAoI;YAChN;gBAAE,OAAO;gBAAyD,aAAa;YAA+H;YAC9M;gBAAE,OAAO;gBAAgD,aAAa;YAAgH;YACtL;gBAAE,OAAO;gBAAiD,aAAa;YAA+G;SACvL;QACD,YAAY;YACV;gBAAE,OAAO;gBAA8B,aAAa;YAAkH;YACtK;gBAAE,OAAO;gBAAuC,aAAa;YAA6G;YAC1K;gBAAE,OAAO;gBAA4C,aAAa;YAA+G;YACjL;gBAAE,OAAO;gBAA2C,aAAa;YAA8H;SAChM;QACD,YAAY;YACV;YACA;YACA;YACA;SACD;IACH;CACD;AAEM,SAAS,wBAAwB,IAAY;IAClD,OAAO,gBAAgB,IAAI,CAAC,CAAA,UAAW,QAAQ,IAAI,KAAK;AAC1D;AAEO,SAAS;IACd,OAAO;AACT;AAEO,SAAS;IACd,OAAO,gBAAgB,MAAM,CAAC,CAAA,UAAW,QAAQ,QAAQ;AAC3D;AAEO,SAAS,6BAA6B,QAAgB;IAC3D,OAAO,gBAAgB,MAAM,CAAC,CAAA,UAAW,QAAQ,QAAQ,KAAK;AAChE", "debugId": null}}, {"offset": {"line": 3172, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/charlie-oscar-consulting/website/components/layout/footer.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport Link from \"next/link\";\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { \r\n  Building2, \r\n  Home, \r\n  Hammer, \r\n  Phone, \r\n  Mail, \r\n  MapPin, \r\n  Clock,\r\n  Facebook,\r\n  Twitter,\r\n  Linkedin,\r\n  Instagram,\r\n  ArrowRight,\r\n  MessageCircle\r\n} from \"lucide-react\";\r\nimport { getContactInfo } from \"@/app/cms/utils/contact\";\r\nimport { getAllFoncierServices } from \"@/lib/foncier-services-data\";\r\nimport { usePathname } from \"next/navigation\";\r\n\r\nexport function Footer() {\r\n  const currentYear = new Date().getFullYear();\r\n  const contact = getContactInfo();\r\n  const contactInfo = contact.contactInfo || {};\r\n  const pathname = usePathname();\r\n\r\n  // Socials from CMS (array of { platform, url, icon })\r\n  const socialLinks = Array.isArray(contactInfo.socials)\r\n    ? contactInfo.socials.map(s => ({\r\n        name: s.platform,\r\n        href: s.url,\r\n        icon: s.icon\r\n      }))\r\n    : [];\r\n\r\n  const foncierServices = getAllFoncierServices();\r\n  const last10FoncierServices = foncierServices.slice(-12).reverse();\r\n\r\n  const services = [\r\n    {\r\n      name: \"Foncier\",\r\n      href: \"/foncier\",\r\n      icon: Building2,\r\n      items: [\r\n        ...last10FoncierServices.map(service => ({\r\n          label: service.title,\r\n          href: `/foncier/services/${service.slug}`\r\n        }))\r\n      ]\r\n    },\r\n    {\r\n      name: \"Immobilier\",\r\n      href: \"/immobilier\",\r\n      icon: Home,\r\n      items: [\r\n      {\r\n        href: \"/immobilier/services/#achat-vente-terrains\",\r\n        label: \"Achat & Vente de Terrains\",\r\n        description: \"Sécurisez vos transactions foncières avec un accompagnement juridique complet et une validation notariée.\",\r\n      },\r\n      {\r\n        href: \"/immobilier/services/#achat-vente-biens\",\r\n        label: \"Achat & Vente de Biens\",\r\n        description: \"Trouvez ou vendez votre maison ou appartement avec une expertise qui optimise votre investissement.\",\r\n      },\r\n      {\r\n        href: \"/immobilier/services/#location-bureaux\",\r\n        label: \"Location de Bureaux\",\r\n        description: \"Des solutions clés en main pour vos espaces professionnels, de la sélection à la négociation de bail.\",\r\n      },\r\n      {\r\n        href: \"/immobilier/services/#location-residentielle\",\r\n        label: \"Location Résidentielle\",\r\n        description: \"Mettez ou trouvez des maisons et appartements (meublés ou non) avec un processus simple et sécurisé.\",\r\n      },\r\n      {\r\n        href: \"/immobilier/services/#location-terrains\",\r\n        label: \"Location de Terrains\",\r\n        description: \"Facilitez la location de terrains pour divers usages, avec une conformité légale assurée.\",\r\n      },\r\n      {\r\n        href: \"/immobilier/services/#conseil-expertise-juridique\",\r\n        label: \"Conseil & Expertise Juridique\",\r\n        description: \"Bénéficiez de conseils de nos juristes expérimentés pour toutes vos questions juridiques immobilières.\",\r\n      }\r\n      ]\r\n    },\r\n    {\r\n      name: \"Construction\",\r\n      href: \"/construction\",\r\n      icon: Hammer,\r\n      items: [\r\n   {\r\n      href: \"/construction/services/#etudes-preliminaires\",\r\n      label: \"Études Préliminaires\",\r\n      description: \"Analyse complète de faisabilité et études techniques préalables\",\r\n    },\r\n    {\r\n      href: \"/construction/services/#conception-architecturale\",\r\n      label: \"Conception Architecturale\",\r\n      description: \"Plans détaillés et design architectural sur mesure\",\r\n    },\r\n    {\r\n      href: \"/construction/services/#ingenierie\",\r\n      label: \"Ingénierie Structure\",\r\n      description: \"Calculs de structure et solutions techniques avancées\",\r\n    },\r\n    {\r\n      href: \"/construction/services/#gestion-projet\",\r\n      label: \"Gestion de Projet\",\r\n      description: \"Coordination complète et suivi de vos projets de construction\",\r\n    },\r\n    {\r\n      href: \"/construction/services/#smart-building\",\r\n      label: \"Smart Building\",\r\n      description: \"Technologies intelligentes et solutions connectées\",\r\n    }\r\n      ]\r\n    }\r\n  ];\r\n\r\n  const quickLinks = [\r\n    { name: \"À propos\", href: \"/a-propos\" },\r\n    // { name: \"Nos projets\", href: \"/projets\" },\r\n    { name: \"Blog\", href: \"/blog\" },\r\n  ];\r\n\r\n  const legalLinks = [\r\n    { name: \"Mentions légales\", href: \"/mentions-legales\" },\r\n    { name: \"Politique de confidentialité\", href: \"/confidentialite\" },\r\n    { name: \"Conditions d'utilisation\", href: \"/conditions\" },\r\n    { name: \"Plan du site\", href: \"/site-map\" }\r\n  ];\r\n\r\n  return (\r\n    pathname.startsWith(\"/m\") ? null : // Hide footer for /m routes\r\n    <footer className=\"bg-gray-900 text-white\">\r\n      {/* Newsletter Section */}\r\n      <div className=\"border-b border-gray-800\">\r\n        <div className=\"container mx-auto px-4 py-12\">\r\n          <div className=\"max-w-4xl mx-auto text-center space-y-6\">\r\n            <h3 className=\"text-2xl font-bold\">\r\n              Restez informé de nos actualités\r\n            </h3>\r\n            <p className=\"text-gray-400 max-w-2xl mx-auto\">\r\n              Recevez nos derniers articles, conseils d'experts et actualités \r\n              du secteur foncier et immobilier camerounais.\r\n            </p>\r\n            <div className=\"flex flex-col sm:flex-row gap-4 max-w-md mx-auto\">\r\n              <Input\r\n                type=\"email\"\r\n                placeholder=\"Votre adresse email\"\r\n                className=\"bg-gray-800 border-gray-700 text-white placeholder:text-gray-400 focus:border-primary\"\r\n              />\r\n              <Button className=\"group\">\r\n                S'abonner\r\n                <ArrowRight className=\"ml-2 h-4 w-4 transition-transform group-hover:translate-x-1\" />\r\n              </Button>\r\n            </div>\r\n            <p className=\"text-xs text-gray-500\">\r\n              Pas de spam, désabonnement possible à tout moment.\r\n            </p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Main Footer Content */}\r\n      <div className=\"container mx-auto px-4 py-16\">\r\n        <div className=\"grid lg:grid-cols-4 gap-12\">\r\n          {/* Company Info */}\r\n          <div className=\"lg:col-span-1 space-y-6\">\r\n            <Link href=\"/\" className=\"flex items-center space-x-3\">\r\n              <img\r\n                src=\"/logo_2.svg\"\r\n                alt=\"Charlie Oscar Consulting Secondary Logo\"\r\n                className=\"w-40 h-40 sm:w-54 sm:h-54 md:w-60 md:h-60 rounded-lg transition-transform duration-300 group-hover:scale-110 object-contain\"\r\n              />\r\n            </Link>\r\n            <p className=\"text-gray-400 leading-relaxed\">\r\n              Votre partenaire de confiance pour tous vos projets fonciers, \r\n              immobiliers et de construction au Cameroun.\r\n            </p>\r\n\r\n            {/* Contact Info */}\r\n            <div className=\"space-y-3\">\r\n              <div className=\"flex items-center space-x-3 text-gray-400\">\r\n                <MapPin className=\"w-5 h-5 text-primary flex-shrink-0\" />\r\n                <span>{contactInfo.locations?.[0] || 'Adresse non renseignée'}</span>\r\n              </div>\r\n              <div className=\"flex items-center space-x-3 text-gray-400\">\r\n                <Phone className=\"w-5 h-5 text-primary flex-shrink-0\" />\r\n                <span>{contactInfo.phone || 'Téléphone non renseigné'}</span>\r\n              </div>\r\n              <div className=\"flex items-center space-x-3 text-gray-400\">\r\n                <Mail className=\"w-5 h-5 text-primary flex-shrink-0\" />\r\n                <span>{contactInfo.email || 'Email non renseigné'}</span>\r\n              </div>\r\n              <div className=\"flex items-center space-x-3 text-gray-400\">\r\n                <MessageCircle className=\"w-5 h-5 text-primary flex-shrink-0\" />\r\n                <span>{contactInfo.whatsapp || 'WhatsApp non renseigné'}</span>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Social Links */}\r\n            <div className=\"flex space-x-4\">\r\n              {socialLinks.map((social, idx) => {\r\n                // Use Lucide icons if available, fallback to <i> if not\r\n                const LucideIcon = {\r\n                  facebook: Facebook,\r\n                  twitter: Twitter,\r\n                  linkedin: Linkedin,\r\n                  instagram: Instagram,\r\n                  whatsapp: MessageCircle\r\n                }[social.icon?.toLowerCase()];\r\n                return (\r\n                  <a\r\n                    key={social.name}\r\n                    href={social.href}\r\n                    className=\"w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-primary transition-colors duration-300 group\"\r\n                    aria-label={social.name}\r\n                    target=\"_blank\"\r\n                    rel=\"noopener noreferrer\"\r\n                  >\r\n                    {LucideIcon ? (\r\n                      <LucideIcon className=\"w-5 h-5 text-gray-400 group-hover:text-white\" />\r\n                    ) : (\r\n                      <i className={`icon-${social.icon} w-5 h-5 text-gray-400 group-hover:text-white`}></i>\r\n                    )}\r\n                  </a>\r\n                );\r\n              })}\r\n            </div>\r\n          </div>\r\n\r\n          {/* Services */}\r\n          <div className=\"lg:col-span-2\">\r\n            <h4 className=\"text-lg font-semibold mb-6\">Nos Services</h4>\r\n            <div className=\"grid md:grid-cols-3 gap-8\">\r\n              {services.map((service) => {\r\n                const Icon = service.icon;\r\n                return (\r\n                  <div key={service.name} className=\"space-y-4\">\r\n                    <Link \r\n                      href={service.href}\r\n                      className=\"flex items-center space-x-3 text-white hover:text-primary transition-colors duration-300 group\"\r\n                    >\r\n                      <Icon className=\"w-5 h-5 text-primary\" />\r\n                      <span className=\"font-medium\">{service.name}</span>\r\n                    </Link>\r\n                    <ul className=\"space-y-2\">\r\n                      {service.items.map((item, index) => (\r\n                        typeof item === 'string' ? (\r\n                          <li key={index}>\r\n                            <Link\r\n                              href={service.href}\r\n                              className=\"text-gray-400 hover:text-white transition-colors duration-300 text-sm\"\r\n                            >\r\n                              {item}\r\n                            </Link>\r\n                          </li>\r\n                        ) : (\r\n                          <li key={item.href}>\r\n                            <Link\r\n                              href={item.href}\r\n                              className=\"text-gray-400 hover:text-white transition-colors duration-300 text-sm\"\r\n                            >\r\n                              {item.label}\r\n                            </Link>\r\n                          </li>\r\n                        )\r\n                      ))}\r\n                    </ul>\r\n                  </div>\r\n                );\r\n              })}\r\n            </div>\r\n          </div>\r\n\r\n          {/* Quick Links */}\r\n          <div className=\"space-y-8\">\r\n            <div>\r\n              <h4 className=\"text-lg font-semibold mb-6\">Liens Rapides</h4>\r\n              <ul className=\"space-y-3\">\r\n                {quickLinks.map((link) => (\r\n                  <li key={link.name}>\r\n                    <Link\r\n                      href={link.href}\r\n                      className=\"text-gray-400 hover:text-white transition-colors duration-300 flex items-center group\"\r\n                    >\r\n                      <ArrowRight className=\"w-3 h-3 mr-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300\" />\r\n                      {link.name}\r\n                    </Link>\r\n                  </li>\r\n                ))}\r\n              </ul>\r\n            </div>\r\n\r\n            {/* Emergency Contact */}\r\n            {/* <div className=\"bg-gray-800 rounded-lg p-6 space-y-4\">\r\n              <h5 className=\"font-semibold text-primary\">Urgences 24/7</h5>\r\n              <p className=\"text-gray-400 text-sm\">\r\n                Pour les urgences juridiques, contactez notre ligne dédiée.\r\n              </p>\r\n              <Button asChild variant=\"outline\" size=\"sm\" className=\"w-full border-primary text-primary hover:bg-primary hover:text-white\">\r\n                <Link href=\"tel:+237682658037\">\r\n                  Ligne d'urgence\r\n                </Link>\r\n              </Button>\r\n            </div> */}\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Bottom Footer */}\r\n      <div className=\"border-t border-gray-800\">\r\n        <div className=\"container mx-auto px-4 py-8\">\r\n          <div className=\"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0\">\r\n            <div className=\"text-gray-400 text-sm\">\r\n              © {currentYear} Charlie Oscar Consulting. Tous droits réservés.\r\n            </div>\r\n            \r\n            <div className=\"flex flex-wrap justify-center md:justify-end gap-6\">\r\n              {legalLinks.map((link) => (\r\n                <Link\r\n                  key={link.name}\r\n                  href={link.href}\r\n                  className=\"text-gray-400 hover:text-white transition-colors duration-300 text-sm\"\r\n                >\r\n                  {link.name}\r\n                </Link>\r\n              ))}\r\n            </div>\r\n          </div>\r\n          \r\n          <div className=\"mt-8 pt-8 border-t border-gray-800 text-center\">\r\n            <p className=\"text-gray-500 text-xs\">\r\n              Développé avec ❤️ pour accompagner vos projets au Cameroun\r\n            </p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </footer>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAeA;AACA;AACA;;;AAtBA;;;;;;;;AAwBO,SAAS;;IACd,MAAM,cAAc,IAAI,OAAO,WAAW;IAC1C,MAAM,UAAU,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD;IAC7B,MAAM,cAAc,QAAQ,WAAW,IAAI,CAAC;IAC5C,MAAM,WAAW,CAAA,GAAA,oQAAA,CAAA,cAAW,AAAD;IAE3B,sDAAsD;IACtD,MAAM,cAAc,MAAM,OAAO,CAAC,YAAY,OAAO,IACjD,YAAY,OAAO,CAAC,GAAG,CAAC,CAAA,IAAK,CAAC;YAC5B,MAAM,EAAE,QAAQ;YAChB,MAAM,EAAE,GAAG;YACX,MAAM,EAAE,IAAI;QACd,CAAC,KACD,EAAE;IAEN,MAAM,kBAAkB,CAAA,GAAA,qIAAA,CAAA,wBAAqB,AAAD;IAC5C,MAAM,wBAAwB,gBAAgB,KAAK,CAAC,CAAC,IAAI,OAAO;IAEhE,MAAM,WAAW;QACf;YACE,MAAM;YACN,MAAM;YACN,MAAM,uSAAA,CAAA,YAAS;YACf,OAAO;mBACF,sBAAsB,GAAG,CAAC,CAAA,UAAW,CAAC;wBACvC,OAAO,QAAQ,KAAK;wBACpB,MAAM,CAAC,kBAAkB,EAAE,QAAQ,IAAI,EAAE;oBAC3C,CAAC;aACF;QACH;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,0RAAA,CAAA,OAAI;YACV,OAAO;gBACP;oBACE,MAAM;oBACN,OAAO;oBACP,aAAa;gBACf;gBACA;oBACE,MAAM;oBACN,OAAO;oBACP,aAAa;gBACf;gBACA;oBACE,MAAM;oBACN,OAAO;oBACP,aAAa;gBACf;gBACA;oBACE,MAAM;oBACN,OAAO;oBACP,aAAa;gBACf;gBACA;oBACE,MAAM;oBACN,OAAO;oBACP,aAAa;gBACf;gBACA;oBACE,MAAM;oBACN,OAAO;oBACP,aAAa;gBACf;aACC;QACH;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,6RAAA,CAAA,SAAM;YACZ,OAAO;gBACV;oBACG,MAAM;oBACN,OAAO;oBACP,aAAa;gBACf;gBACA;oBACE,MAAM;oBACN,OAAO;oBACP,aAAa;gBACf;gBACA;oBACE,MAAM;oBACN,OAAO;oBACP,aAAa;gBACf;gBACA;oBACE,MAAM;oBACN,OAAO;oBACP,aAAa;gBACf;gBACA;oBACE,MAAM;oBACN,OAAO;oBACP,aAAa;gBACf;aACG;QACH;KACD;IAED,MAAM,aAAa;QACjB;YAAE,MAAM;YAAY,MAAM;QAAY;QACtC,6CAA6C;QAC7C;YAAE,MAAM;YAAQ,MAAM;QAAQ;KAC/B;IAED,MAAM,aAAa;QACjB;YAAE,MAAM;YAAoB,MAAM;QAAoB;QACtD;YAAE,MAAM;YAAgC,MAAM;QAAmB;QACjE;YAAE,MAAM;YAA4B,MAAM;QAAc;QACxD;YAAE,MAAM;YAAgB,MAAM;QAAY;KAC3C;IAED,OACE,SAAS,UAAU,CAAC,QAAQ,qBAC5B,4TAAC;QAAO,WAAU;;0BAEhB,4TAAC;gBAAI,WAAU;0BACb,cAAA,4TAAC;oBAAI,WAAU;8BACb,cAAA,4TAAC;wBAAI,WAAU;;0CACb,4TAAC;gCAAG,WAAU;0CAAqB;;;;;;0CAGnC,4TAAC;gCAAE,WAAU;0CAAkC;;;;;;0CAI/C,4TAAC;gCAAI,WAAU;;kDACb,4TAAC,6HAAA,CAAA,QAAK;wCACJ,MAAK;wCACL,aAAY;wCACZ,WAAU;;;;;;kDAEZ,4TAAC,8HAAA,CAAA,SAAM;wCAAC,WAAU;;4CAAQ;0DAExB,4TAAC,ySAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;;;;;;;0CAG1B,4TAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;;;;;0BAQ3C,4TAAC;gBAAI,WAAU;0BACb,cAAA,4TAAC;oBAAI,WAAU;;sCAEb,4TAAC;4BAAI,WAAU;;8CACb,4TAAC,8RAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;8CACvB,cAAA,4TAAC;wCACC,KAAI;wCACJ,KAAI;wCACJ,WAAU;;;;;;;;;;;8CAGd,4TAAC;oCAAE,WAAU;8CAAgC;;;;;;8CAM7C,4TAAC;oCAAI,WAAU;;sDACb,4TAAC;4CAAI,WAAU;;8DACb,4TAAC,iSAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,4TAAC;8DAAM,YAAY,SAAS,EAAE,CAAC,EAAE,IAAI;;;;;;;;;;;;sDAEvC,4TAAC;4CAAI,WAAU;;8DACb,4TAAC,2RAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,4TAAC;8DAAM,YAAY,KAAK,IAAI;;;;;;;;;;;;sDAE9B,4TAAC;4CAAI,WAAU;;8DACb,4TAAC,yRAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,4TAAC;8DAAM,YAAY,KAAK,IAAI;;;;;;;;;;;;sDAE9B,4TAAC;4CAAI,WAAU;;8DACb,4TAAC,+SAAA,CAAA,gBAAa;oDAAC,WAAU;;;;;;8DACzB,4TAAC;8DAAM,YAAY,QAAQ,IAAI;;;;;;;;;;;;;;;;;;8CAKnC,4TAAC;oCAAI,WAAU;8CACZ,YAAY,GAAG,CAAC,CAAC,QAAQ;wCACxB,wDAAwD;wCACxD,MAAM,aAAa;4CACjB,UAAU,iSAAA,CAAA,WAAQ;4CAClB,SAAS,+RAAA,CAAA,UAAO;4CAChB,UAAU,iSAAA,CAAA,WAAQ;4CAClB,WAAW,mSAAA,CAAA,YAAS;4CACpB,UAAU,+SAAA,CAAA,gBAAa;wCACzB,CAAC,CAAC,OAAO,IAAI,EAAE,cAAc;wCAC7B,qBACE,4TAAC;4CAEC,MAAM,OAAO,IAAI;4CACjB,WAAU;4CACV,cAAY,OAAO,IAAI;4CACvB,QAAO;4CACP,KAAI;sDAEH,2BACC,4TAAC;gDAAW,WAAU;;;;;qEAEtB,4TAAC;gDAAE,WAAW,CAAC,KAAK,EAAE,OAAO,IAAI,CAAC,6CAA6C,CAAC;;;;;;2CAV7E,OAAO,IAAI;;;;;oCActB;;;;;;;;;;;;sCAKJ,4TAAC;4BAAI,WAAU;;8CACb,4TAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,4TAAC;oCAAI,WAAU;8CACZ,SAAS,GAAG,CAAC,CAAC;wCACb,MAAM,OAAO,QAAQ,IAAI;wCACzB,qBACE,4TAAC;4CAAuB,WAAU;;8DAChC,4TAAC,8RAAA,CAAA,UAAI;oDACH,MAAM,QAAQ,IAAI;oDAClB,WAAU;;sEAEV,4TAAC;4DAAK,WAAU;;;;;;sEAChB,4TAAC;4DAAK,WAAU;sEAAe,QAAQ,IAAI;;;;;;;;;;;;8DAE7C,4TAAC;oDAAG,WAAU;8DACX,QAAQ,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,QACxB,OAAO,SAAS,yBACd,4TAAC;sEACC,cAAA,4TAAC,8RAAA,CAAA,UAAI;gEACH,MAAM,QAAQ,IAAI;gEAClB,WAAU;0EAET;;;;;;2DALI;;;;iFAST,4TAAC;sEACC,cAAA,4TAAC,8RAAA,CAAA,UAAI;gEACH,MAAM,KAAK,IAAI;gEACf,WAAU;0EAET,KAAK,KAAK;;;;;;2DALN,KAAK,IAAI;;;;;;;;;;;2CApBhB,QAAQ,IAAI;;;;;oCAiC1B;;;;;;;;;;;;sCAKJ,4TAAC;4BAAI,WAAU;sCACb,cAAA,4TAAC;;kDACC,4TAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,4TAAC;wCAAG,WAAU;kDACX,WAAW,GAAG,CAAC,CAAC,qBACf,4TAAC;0DACC,cAAA,4TAAC,8RAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAU;;sEAEV,4TAAC,ySAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;wDACrB,KAAK,IAAI;;;;;;;+CANL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BA8B9B,4TAAC;gBAAI,WAAU;0BACb,cAAA,4TAAC;oBAAI,WAAU;;sCACb,4TAAC;4BAAI,WAAU;;8CACb,4TAAC;oCAAI,WAAU;;wCAAwB;wCAClC;wCAAY;;;;;;;8CAGjB,4TAAC;oCAAI,WAAU;8CACZ,WAAW,GAAG,CAAC,CAAC,qBACf,4TAAC,8RAAA,CAAA,UAAI;4CAEH,MAAM,KAAK,IAAI;4CACf,WAAU;sDAET,KAAK,IAAI;2CAJL,KAAK,IAAI;;;;;;;;;;;;;;;;sCAUtB,4TAAC;4BAAI,WAAU;sCACb,cAAA,4TAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjD;GAnUgB;;QAIG,oQAAA,CAAA,cAAW;;;KAJd", "debugId": null}}]}