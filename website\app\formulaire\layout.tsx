import { Metadata } from "next";
import { generateMetadata as generateSEOMetadata } from "@/app/lib/seo-config";

// Generate metadata for formulaire page
export const metadata: Metadata = generateSEOMetadata({
  title: "Formulaire de Demande",
  description: "Remplissez notre formulaire sécurisé pour obtenir une consultation personnalisée pour votre procédure foncière.",
  keywords: [
    "formulaire foncier",
    "demande consultation",
    "consultation gratuite",
    "devis personnalisé"
  ],
  canonical: "/formulaire",
  ogImage: "/images/seo/og-formulaire.jpg"
});

export default function FormulaireLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <>
      {/* Structured Data for Form */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "WebPage",
            "name": "Formulaire de Demande - Charlie Oscar Consulting",
            "description": "Formulaire sécurisé pour demander une consultation en foncier, immobilier ou construction",
            "url": "https://charlieoscarconsulting.com/formulaire",
            "mainEntity": {
              "@type": "ContactPoint",
              "contactType": "customer service",
              "availableLanguage": ["French", "English"]
            }
          })
        }}
      />
      {children}
    </>
  );
}
