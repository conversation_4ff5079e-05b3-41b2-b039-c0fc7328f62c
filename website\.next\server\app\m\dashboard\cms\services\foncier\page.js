const CHUNK_PUBLIC_PATH = "server/app/m/dashboard/cms/services/foncier/page.js";
const runtime = require("../../../../../../chunks/ssr/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/ssr/32a9e_next_dist_1b98e904._.js");
runtime.loadChunk("server/chunks/ssr/[root-of-the-server]__4c58c055._.js");
runtime.loadChunk("server/chunks/ssr/app_1f3630ef._.js");
runtime.loadChunk("server/chunks/ssr/[root-of-the-server]__cb2999cc._.js");
runtime.loadChunk("server/chunks/ssr/node_modules__pnpm_75432760._.js");
runtime.loadChunk("server/chunks/ssr/_79ca3fe1._.js");
runtime.loadChunk("server/chunks/ssr/32a9e_next_dist_client_components_3923b3a5._.js");
runtime.loadChunk("server/chunks/ssr/32a9e_next_dist_client_components_unauthorized-error_666ec8d5.js");
runtime.loadChunk("server/chunks/ssr/[root-of-the-server]__473d383f._.js");
runtime.loadChunk("server/chunks/ssr/app_m_dashboard_layout_tsx_e4c2b8c8._.js");
runtime.loadChunk("server/chunks/ssr/app_m_dashboard_cms_services_foncier_loading_tsx_036a240f._.js");
runtime.loadChunk("server/chunks/ssr/32a9e_next_dist_f64d5677._.js");
runtime.loadChunk("server/chunks/ssr/_6d972a94._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/m/dashboard/cms/services/foncier/page/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/.pnpm/next@15.3.1_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/esm/build/templates/app-page.js?page=/m/dashboard/cms/services/foncier/page { METADATA_0 => \"[project]/app/favicon.ico.mjs { IMAGE => \\\"[project]/app/favicon.ico (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js server component)\", MODULE_1 => \"[project]/app/layout.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_2 => \"[project]/app/not-found.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_3 => \"[project]/node_modules/.pnpm/next@15.3.1_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/forbidden-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_4 => \"[project]/node_modules/.pnpm/next@15.3.1_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/unauthorized-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_5 => \"[project]/app/m/layout.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_6 => \"[project]/app/m/dashboard/layout.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_7 => \"[project]/app/m/dashboard/cms/services/foncier/loading.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_8 => \"[project]/app/m/dashboard/cms/services/foncier/page.tsx [app-rsc] (ecmascript, Next.js server component)\" } [app-rsc] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/.pnpm/next@15.3.1_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/esm/build/templates/app-page.js?page=/m/dashboard/cms/services/foncier/page { METADATA_0 => \"[project]/app/favicon.ico.mjs { IMAGE => \\\"[project]/app/favicon.ico (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js server component)\", MODULE_1 => \"[project]/app/layout.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_2 => \"[project]/app/not-found.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_3 => \"[project]/node_modules/.pnpm/next@15.3.1_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/forbidden-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_4 => \"[project]/node_modules/.pnpm/next@15.3.1_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/unauthorized-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_5 => \"[project]/app/m/layout.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_6 => \"[project]/app/m/dashboard/layout.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_7 => \"[project]/app/m/dashboard/cms/services/foncier/loading.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_8 => \"[project]/app/m/dashboard/cms/services/foncier/page.tsx [app-rsc] (ecmascript, Next.js server component)\" } [app-rsc] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
