import { <PERSON>ada<PERSON> } from "next";
import Link from "next/link";
import Image from "next/image";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ImageCarousel } from "@/components/ui/image-carousel";
import {
  ArrowRight,
  Home,
  Building2,
  MapPin,
  Key,
  Users,
  Award,
  CheckCircle,
  Target,
  Shield,
  Clock,
  Star,
  TrendingUp,
  ShieldCheck,
  UserCheck,
  Eye,
  Zap,
  Scale,
  Compass
} from "lucide-react";
import { getImmobilierContent } from "@/app/cms/utils/immobilier";

const immobilierData = getImmobilierContent();

export const metadata: Metadata = {
  title: immobilierData.metadata.title,
  description: immobilierData.metadata.description,
  keywords: immobilierData.metadata.keywords,
  openGraph: immobilierData.metadata.openGraph,
  alternates: {
    canonical: immobilierData.metadata.canonical
  }
};

export default function ImmobilierPage() {
  const heroData = immobilierData.hero;
  const aboutData = immobilierData.about;
  const servicesData = immobilierData.services;
  const advantagesData = immobilierData.advantages;
  const ctaData = immobilierData.cta;

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section with Carousel */}
      <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
        {/* Background Carousel */}
        <div className="absolute inset-0">
          <ImageCarousel
            images={heroData.carouselImages}
            autoPlay={true}
            autoPlayInterval={6000}
            showControls={true}
            showIndicators={true}
            className="w-full h-full"
          />
        </div>

        <div className="container mx-auto px-4 relative z-20">
          <div className="max-w-5xl mx-auto text-center text-white space-y-10">
            {/* Badge with enhanced styling */}
            <div className="inline-flex items-center px-8 py-4 bg-white/10 backdrop-blur-md rounded-full text-sm font-medium border border-white/20 shadow-lg">
              <Home className="w-5 h-5 mr-3 text-blue-300" />
              <span className="text-white">{heroData.badge.text}</span>
            </div>

            {/* Main Title with enhanced typography */}
            <div className="space-y-6">
              <h1 className="text-5xl md:text-7xl lg:text-8xl font-bold leading-tight tracking-tight">
                <span className="block bg-gradient-to-r from-white via-blue-100 to-white bg-clip-text text-transparent">
                  {heroData.title.main}
                </span>
                <span className="block text-4xl md:text-6xl lg:text-7xl bg-gradient-to-r from-blue-300 via-blue-400 to-blue-500 bg-clip-text text-transparent mt-2">
                  {heroData.title.highlight}
                </span>
              </h1>
            </div>

            {/* Subtitle with better spacing */}
            <p className="text-xl md:text-2xl lg:text-3xl text-gray-100 max-w-4xl mx-auto leading-relaxed font-light">
              {heroData.subtitle}
            </p>

            {/* Enhanced Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-6 justify-center pt-12">
              {heroData.buttons.map((button, index) => (
                <Button
                  key={index}
                  asChild
                  size="lg"
                  className={button.variant === 'primary'
                    ? "bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-10 py-6 text-lg font-semibold group shadow-xl hover:shadow-2xl transition-all duration-300 border-0"
                    : "border-2 border-white/30 text-white hover:bg-white/10 hover:border-white/50 px-10 py-6 text-lg font-semibold backdrop-blur-md transition-all duration-300"
                  }
                  variant={button.variant === 'primary' ? 'default' : 'outline'}
                >
                  <Link href={button.href} className="flex items-center">
                    {button.text}
                    {button.icon && <ArrowRight className="ml-3 h-6 w-6 transition-transform group-hover:translate-x-1" />}
                  </Link>
                </Button>
              ))}
            </div>

            {/* Enhanced Stats with better visual hierarchy */}
            <div className="grid grid-cols-3 gap-12 pt-20 max-w-3xl mx-auto">
              {heroData.stats.map((stat, index) => (
                <div key={index} className="text-center group">
                  <div className="bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20 hover:bg-white/15 transition-all duration-300">
                    <div className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-blue-300 to-blue-400 bg-clip-text text-transparent mb-3">
                      {stat.value}
                    </div>
                    <div className="text-sm md:text-base text-gray-200 font-medium">
                      {stat.label}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Enhanced Scroll indicator */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-20">
          <div className="flex flex-col items-center space-y-2">
            <div className="w-8 h-12 border-2 border-white/40 rounded-full flex justify-center bg-white/5 backdrop-blur-sm">
              <div className="w-1.5 h-4 bg-gradient-to-b from-white to-blue-300 rounded-full mt-2 animate-bounce"></div>
            </div>
            <span className="text-white/60 text-xs font-medium tracking-wider">DÉCOUVRIR</span>
          </div>
        </div>

        {/* Floating elements for visual enhancement */}
        <div className="absolute top-20 left-10 w-20 h-20 bg-blue-400/10 rounded-full blur-xl animate-pulse"></div>
        <div className="absolute bottom-32 right-16 w-32 h-32 bg-white/5 rounded-full blur-2xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/3 right-20 w-16 h-16 bg-blue-300/10 rounded-full blur-lg animate-pulse delay-500"></div>
      </section>

      {/* About Section - Enhanced Minimalist Design */}
      <section className="py-32 bg-gradient-to-br from-gray-50 via-white to-blue-50/30">
        <div className="container mx-auto px-4">
          <div className="max-w-7xl mx-auto">
            <div className="grid lg:grid-cols-2 gap-24 items-center">
              {/* Content */}
              <div className="space-y-12 lg:pr-8">
                <div className="space-y-10">
                  <div className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-blue-50 to-gray-50 text-blue-800 rounded-full text-sm font-semibold border border-blue-100 shadow-sm">
                    <Building2 className="w-5 h-5 mr-3 text-blue-600" />
                    {aboutData.badge}
                  </div>

                  <h2 className="text-4xl md:text-6xl font-bold text-gray-900 leading-tight tracking-tight">
                    <span className="bg-gradient-to-r from-gray-900 via-blue-900 to-gray-900 bg-clip-text text-transparent">
                      {aboutData.title}
                    </span>
                  </h2>

                  <p className="text-xl md:text-2xl text-gray-600 leading-relaxed font-light">
                    {aboutData.description}
                  </p>
                </div>

                {/* Enhanced Features Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                  {aboutData.features.map((feature, index) => (
                    <div key={index} className="group p-6 bg-white rounded-3xl shadow-sm hover:shadow-xl transition-all duration-500 border border-gray-100 hover:border-blue-200">
                      <div className="space-y-5">
                        <div className="w-16 h-16 bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-sm">
                          {feature.icon === 'target' && <Target className="w-8 h-8 text-blue-600" />}
                          {feature.icon === 'shield' && <Shield className="w-8 h-8 text-blue-600" />}
                          {feature.icon === 'users' && <Users className="w-8 h-8 text-blue-600" />}
                          {feature.icon === 'network' && <Building2 className="w-8 h-8 text-blue-600" />}
                        </div>
                        <div>
                          <h3 className="text-lg font-bold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors">
                            {feature.title}
                          </h3>
                          <p className="text-gray-600 leading-relaxed">
                            {feature.description}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Enhanced Images Grid */}
              <div className="relative lg:pl-8">
                <div className="grid grid-cols-2 gap-8">
                  <div className="space-y-8">
                    <div className="group h-72 rounded-3xl overflow-hidden shadow-xl hover:shadow-2xl transition-all duration-700">
                      <div className="relative h-full">
                        <Image
                          src={aboutData.images[0]}
                          alt="Immobilier moderne"
                          width={400}
                          height={300}
                          className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700"
                        />
                        <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                      </div>
                    </div>
                    <div className="group h-56 rounded-3xl overflow-hidden shadow-xl hover:shadow-2xl transition-all duration-700">
                      <div className="relative h-full">
                        <Image
                          src={aboutData.images[1]}
                          alt="Consultation immobilière"
                          width={400}
                          height={200}
                          className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700"
                        />
                        <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                      </div>
                    </div>
                  </div>
                  <div className="space-y-8 pt-16">
                    <div className="group h-56 rounded-3xl overflow-hidden shadow-xl hover:shadow-2xl transition-all duration-700">
                      <div className="relative h-full">
                        <Image
                          src={aboutData.images[2]}
                          alt="Architecture moderne"
                          width={400}
                          height={200}
                          className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700"
                        />
                        <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                      </div>
                    </div>
                    <div className="group h-72 rounded-3xl overflow-hidden shadow-xl hover:shadow-2xl transition-all duration-700">
                      <div className="relative h-full">
                        <Image
                          src={aboutData.images[3]}
                          alt="Intérieur élégant"
                          width={400}
                          height={300}
                          className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700"
                        />
                        <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Enhanced floating decorations */}
                <div className="absolute -top-8 -right-8 w-32 h-32 bg-gradient-to-br from-blue-100 to-blue-200 rounded-full opacity-60 blur-xl animate-pulse"></div>
                <div className="absolute -bottom-8 -left-8 w-40 h-40 bg-gradient-to-br from-gray-100 to-gray-200 rounded-full opacity-40 blur-2xl animate-pulse delay-1000"></div>
                <div className="absolute top-1/2 -right-4 w-20 h-20 bg-gradient-to-br from-blue-50 to-blue-100 rounded-full opacity-80 blur-lg animate-pulse delay-500"></div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Services Section - Clean & Visual */}
      <section className="py-32 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-7xl mx-auto">
            {/* Section Header */}
            <div className="text-center space-y-8 mb-20">
              <div className="inline-flex items-center px-6 py-3 bg-white text-gray-700 rounded-full text-sm font-medium shadow-sm">
                <Building2 className="w-4 h-4 mr-2" />
                Nos Services
              </div>

              <h2 className="text-4xl md:text-5xl font-bold text-gray-900 max-w-4xl mx-auto leading-tight">
                {servicesData.title}
              </h2>

              <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                {servicesData.subtitle}
              </p>
            </div>

            {/* Services Grid */}
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
              {servicesData.items.map((service) => (
                <Card key={service.id} className="group hover:shadow-2xl transition-all duration-500 border-0 shadow-lg bg-white overflow-hidden">
                  {/* Service Image */}
                  <div className="h-48 overflow-hidden">
                    <Image
                      src={service.image}
                      alt={service.title}
                      width={400}
                      height={200}
                      className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-500"
                    />
                  </div>

                  <CardHeader className="space-y-4 p-8">
                    <div className="flex items-center justify-between">
                      <Badge className={`
                        ${service.color === 'orange' ? 'bg-orange-100 text-orange-800 border-orange-200' : ''}
                        ${service.color === 'blue' ? 'bg-blue-100 text-blue-800 border-blue-200' : ''}
                        ${service.color === 'green' ? 'bg-green-100 text-green-800 border-green-200' : ''}
                        ${service.color === 'purple' ? 'bg-purple-100 text-purple-800 border-purple-200' : ''}
                        ${service.color === 'yellow' ? 'bg-yellow-100 text-yellow-800 border-yellow-200' : ''}
                        ${service.color === 'red' ? 'bg-red-100 text-red-800 border-red-200' : ''}
                      `}>
                        {service.icon === 'map' && <MapPin className="w-3 h-3 mr-1" />}
                        {service.icon === 'home' && <Home className="w-3 h-3 mr-1" />}
                        {service.icon === 'building' && <Building2 className="w-3 h-3 mr-1" />}
                        {service.icon === 'key' && <Key className="w-3 h-3 mr-1" />}
                        {service.icon === 'compass' && <Compass className="w-3 h-3 mr-1" />}
                        {service.icon === 'scale' && <Scale className="w-3 h-3 mr-1" />}
                        Service
                      </Badge>
                    </div>

                    <CardTitle className={`text-xl group-hover:text-${service.color}-600 transition-colors`}>
                      {service.title}
                    </CardTitle>

                    <CardDescription className="text-gray-600 leading-relaxed">
                      {service.description}
                    </CardDescription>
                  </CardHeader>

                  <CardContent className="px-8 pb-8">
                    <Button
                      asChild
                      variant="outline"
                      className={`w-full group/btn border-${service.color}-200 hover:bg-${service.color}-50`}
                    >
                      <Link href={`/immobilier/services#${service.id}`} className="flex items-center justify-center">
                        En savoir plus
                        <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover/btn:translate-x-1" />
                      </Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* All Services Link */}
            <div className="text-center">
              <Button asChild size="lg" className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4">
                <Link href="/immobilier/services" className="flex items-center">
                  Découvrir tous nos services immobiliers
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Advantages Section - Minimalist Grid */}
      <section className="py-32 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-7xl mx-auto">
            {/* Section Header */}
            <div className="text-center space-y-8 mb-20">
              <div className="inline-flex items-center px-6 py-3 bg-blue-50 text-blue-800 rounded-full text-sm font-medium">
                <Star className="w-4 h-4 mr-2" />
                Nos Avantages
              </div>

              <h2 className="text-4xl md:text-5xl font-bold text-gray-900 max-w-4xl mx-auto leading-tight">
                {advantagesData.title}
              </h2>

              <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                {advantagesData.subtitle}
              </p>
            </div>

            {/* Advantages Grid */}
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {advantagesData.items.map((advantage, index) => (
                <div key={index} className="group p-8 bg-white rounded-3xl shadow-lg hover:shadow-2xl transition-all duration-500 border border-gray-100">
                  <div className="space-y-6">
                    <div className="w-16 h-16 bg-blue-50 rounded-2xl flex items-center justify-center group-hover:bg-blue-100 transition-colors">
                      {advantage.icon === 'trending-up' && <TrendingUp className="w-8 h-8 text-blue-600" />}
                      {advantage.icon === 'shield-check' && <ShieldCheck className="w-8 h-8 text-blue-600" />}
                      {advantage.icon === 'user-check' && <UserCheck className="w-8 h-8 text-blue-600" />}
                      {advantage.icon === 'users' && <Users className="w-8 h-8 text-blue-600" />}
                      {advantage.icon === 'eye' && <Eye className="w-8 h-8 text-blue-600" />}
                      {advantage.icon === 'zap' && <Zap className="w-8 h-8 text-blue-600" />}
                    </div>

                    <div>
                      <h3 className="text-xl font-semibold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors">
                        {advantage.title}
                      </h3>
                      <p className="text-gray-600 leading-relaxed">
                        {advantage.description}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Stats Section */}
            <div className="mt-20 bg-gradient-to-br from-blue-50 via-white to-gray-50 rounded-3xl p-12 border border-gray-100">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
                <div className="text-center space-y-3">
                  <div className="text-4xl font-bold text-blue-600">10+</div>
                  <div className="text-sm text-gray-600 font-medium">Années d'expérience</div>
                </div>
                <div className="text-center space-y-3">
                  <div className="text-4xl font-bold text-blue-600">100%</div>
                  <div className="text-sm text-gray-600 font-medium">Transactions sécurisées</div>
                </div>
                <div className="text-center space-y-3">
                  <div className="text-4xl font-bold text-blue-600">24/7</div>
                  <div className="text-sm text-gray-600 font-medium">Accompagnement</div>
                </div>
                <div className="text-center space-y-3">
                  <div className="text-4xl font-bold text-blue-600">6</div>
                  <div className="text-sm text-gray-600 font-medium">Services spécialisés</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section - Clean & Modern */}
      <section className="py-32 bg-gradient-to-br from-blue-600 via-blue-700 to-blue-800 text-white relative overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-0 left-0 w-full h-full bg-[url('data:image/svg+xml,%3Csvg%20width%3D%2260%22%20height%3D%2260%22%20viewBox%3D%220%200%2060%2060%22%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%3E%3Cg%20fill%3D%22none%22%20fill-rule%3D%22evenodd%22%3E%3Cg%20fill%3D%22%23ffffff%22%20fill-opacity%3D%220.1%22%3E%3Ccircle%20cx%3D%2230%22%20cy%3D%2230%22%20r%3D%222%22/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')]"></div>
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-4xl mx-auto text-center space-y-12">
            <div className="space-y-6">
              <h2 className="text-4xl md:text-5xl font-bold leading-tight">
                {ctaData.title}
              </h2>

              <p className="text-xl text-blue-100 leading-relaxed max-w-3xl mx-auto">
                {ctaData.description}
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-6 justify-center">
              {ctaData.buttons.map((button, index) => (
                <Button
                  key={index}
                  asChild
                  size="lg"
                  className={button.variant === 'secondary'
                    ? "bg-white text-blue-600 hover:bg-gray-100 px-8 py-4 text-lg group"
                    : "border-white text-white hover:bg-white hover:text-blue-600 px-8 py-4 text-lg"
                  }
                  variant={button.variant === 'secondary' ? 'secondary' : 'outline'}
                >
                  <Link href={button.href} className="flex items-center">
                    {button.text}
                    <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
                  </Link>
                </Button>
              ))}
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
