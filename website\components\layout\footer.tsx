"use client";

import Link from "next/link";
import Image from "next/image";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { 
  Building2, 
  Home, 
  Hammer, 
  Phone, 
  Mail, 
  MapPin, 
  Clock,
  Facebook,
  Twitter,
  Linkedin,
  Instagram,
  ArrowRight,
  MessageCircle
} from "lucide-react";
import { getContactInfo } from "@/app/cms/utils/contact";
import { getAllFoncierServices } from "@/lib/foncier-services-data";
import { usePathname } from "next/navigation";

export function Footer() {
  const currentYear = new Date().getFullYear();
  const contact = getContactInfo();
  const contactInfo = contact.contactInfo || {};
  const pathname = usePathname();

  // Socials from CMS (array of { platform, url, icon })
  const socialLinks = Array.isArray(contactInfo.socials)
    ? contactInfo.socials.map(s => ({
        name: s.platform,
        href: s.url,
        icon: s.icon
      }))
    : [];

  const foncierServices = getAllFoncierServices();
  const last10FoncierServices = foncierServices.slice(-12).reverse();

  const services = [
    {
      name: "Foncier",
      href: "/foncier",
      icon: Building2,
      items: [
        ...last10FoncierServices.map(service => ({
          label: service.title,
          href: `/foncier/services/${service.slug}`
        }))
      ]
    },
    {
      name: "Immobilier",
      href: "/immobilier",
      icon: Home,
      items: [
      {
        href: "/immobilier/services/#achat-vente-terrains",
        label: "Achat & Vente de Terrains",
        description: "Sécurisez vos transactions foncières avec un accompagnement juridique complet et une validation notariée.",
      },
      {
        href: "/immobilier/services/#achat-vente-biens",
        label: "Achat & Vente de Biens",
        description: "Trouvez ou vendez votre maison ou appartement avec une expertise qui optimise votre investissement.",
      },
      {
        href: "/immobilier/services/#location-bureaux",
        label: "Location de Bureaux",
        description: "Des solutions clés en main pour vos espaces professionnels, de la sélection à la négociation de bail.",
      },
      {
        href: "/immobilier/services/#location-residentielle",
        label: "Location Résidentielle",
        description: "Mettez ou trouvez des maisons et appartements (meublés ou non) avec un processus simple et sécurisé.",
      },
      {
        href: "/immobilier/services/#location-terrains",
        label: "Location de Terrains",
        description: "Facilitez la location de terrains pour divers usages, avec une conformité légale assurée.",
      },
      {
        href: "/immobilier/services/#conseil-expertise-juridique",
        label: "Conseil & Expertise Juridique",
        description: "Bénéficiez de conseils de nos juristes expérimentés pour toutes vos questions juridiques immobilières.",
      }
      ]
    },
    {
      name: "Construction",
      href: "/construction",
      icon: Hammer,
      items: [
   {
      href: "/construction/services/#etudes-preliminaires",
      label: "Études Préliminaires",
      description: "Analyse complète de faisabilité et études techniques préalables",
    },
    {
      href: "/construction/services/#conception-architecturale",
      label: "Conception Architecturale",
      description: "Plans détaillés et design architectural sur mesure",
    },
    {
      href: "/construction/services/#ingenierie",
      label: "Ingénierie Structure",
      description: "Calculs de structure et solutions techniques avancées",
    },
    {
      href: "/construction/services/#gestion-projet",
      label: "Gestion de Projet",
      description: "Coordination complète et suivi de vos projets de construction",
    },
    {
      href: "/construction/services/#smart-building",
      label: "Smart Building",
      description: "Technologies intelligentes et solutions connectées",
    }
      ]
    }
  ];

  const quickLinks = [
    { name: "À propos", href: "/a-propos" },
    // { name: "Nos projets", href: "/projets" },
    { name: "Blog", href: "/blog" },
  ];

  const legalLinks = [
    { name: "Mentions légales", href: "/mentions-legales" },
    { name: "Politique de confidentialité", href: "/confidentialite" },
    { name: "Conditions d'utilisation", href: "/conditions" },
    { name: "Plan du site", href: "/site-map" }
  ];

  return (
    pathname.startsWith("/m") ? null : // Hide footer for /m routes
    <footer className="bg-gray-900 text-white">
      {/* Newsletter Section */}
      <div className="border-b border-gray-800">
        <div className="container mx-auto px-4 py-12">
          <div className="max-w-4xl mx-auto text-center space-y-6">
            <h3 className="text-2xl font-bold">
              Restez informé de nos actualités
            </h3>
            <p className="text-gray-400 max-w-2xl mx-auto">
              Recevez nos derniers articles, conseils d'experts et actualités 
              du secteur foncier et immobilier camerounais.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
              <Input
                type="email"
                placeholder="Votre adresse email"
                className="bg-gray-800 border-gray-700 text-white placeholder:text-gray-400 focus:border-primary"
              />
              <Button className="group">
                S'abonner
                <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
              </Button>
            </div>
            <p className="text-xs text-gray-500">
              Pas de spam, désabonnement possible à tout moment.
            </p>
          </div>
        </div>
      </div>

      {/* Main Footer Content */}
      <div className="container mx-auto px-4 py-16">
        <div className="grid lg:grid-cols-4 gap-12">
          {/* Company Info */}
          <div className="lg:col-span-1 space-y-6">
            <Link href="/" className="flex items-center space-x-3">
              <Image
                src="/logo_2.svg"
                alt="Charlie Oscar Consulting Secondary Logo"
                width={240}
                height={240}
                className="w-40 h-40 sm:w-54 sm:h-54 md:w-60 md:h-60 rounded-lg transition-transform duration-300 group-hover:scale-110 object-contain"
              />
            </Link>
            <p className="text-gray-400 leading-relaxed">
              Votre partenaire de confiance pour tous vos projets fonciers, 
              immobiliers et de construction au Cameroun.
            </p>

            {/* Contact Info */}
            <div className="space-y-3">
              <div className="flex items-center space-x-3 text-gray-400">
                <MapPin className="w-5 h-5 text-primary flex-shrink-0" />
                <span>{contactInfo.locations?.[0] || 'Adresse non renseignée'}</span>
              </div>
              <div className="flex items-center space-x-3 text-gray-400">
                <Phone className="w-5 h-5 text-primary flex-shrink-0" />
                <span>{contactInfo.phone || 'Téléphone non renseigné'}</span>
              </div>
              <div className="flex items-center space-x-3 text-gray-400">
                <Mail className="w-5 h-5 text-primary flex-shrink-0" />
                <span>{contactInfo.email || 'Email non renseigné'}</span>
              </div>
              <div className="flex items-center space-x-3 text-gray-400">
                <MessageCircle className="w-5 h-5 text-primary flex-shrink-0" />
                <span>{contactInfo.whatsapp || 'WhatsApp non renseigné'}</span>
              </div>
            </div>

            {/* Social Links */}
            <div className="flex space-x-4">
              {socialLinks.map((social, idx) => {
                // Use Lucide icons if available, fallback to <i> if not
                const LucideIcon = {
                  facebook: Facebook,
                  twitter: Twitter,
                  linkedin: Linkedin,
                  instagram: Instagram,
                  whatsapp: MessageCircle
                }[social.icon?.toLowerCase()];
                return (
                  <a
                    key={social.name}
                    href={social.href}
                    className="w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-primary transition-colors duration-300 group"
                    aria-label={social.name}
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    {LucideIcon ? (
                      <LucideIcon className="w-5 h-5 text-gray-400 group-hover:text-white" />
                    ) : (
                      <i className={`icon-${social.icon} w-5 h-5 text-gray-400 group-hover:text-white`}></i>
                    )}
                  </a>
                );
              })}
            </div>
          </div>

          {/* Services */}
          <div className="lg:col-span-2">
            <h4 className="text-lg font-semibold mb-6">Nos Services</h4>
            <div className="grid md:grid-cols-3 gap-8">
              {services.map((service) => {
                const Icon = service.icon;
                return (
                  <div key={service.name} className="space-y-4">
                    <Link 
                      href={service.href}
                      className="flex items-center space-x-3 text-white hover:text-primary transition-colors duration-300 group"
                    >
                      <Icon className="w-5 h-5 text-primary" />
                      <span className="font-medium">{service.name}</span>
                    </Link>
                    <ul className="space-y-2">
                      {service.items.map((item, index) => (
                        typeof item === 'string' ? (
                          <li key={index}>
                            <Link
                              href={service.href}
                              className="text-gray-400 hover:text-white transition-colors duration-300 text-sm"
                            >
                              {item}
                            </Link>
                          </li>
                        ) : (
                          <li key={item.href}>
                            <Link
                              href={item.href}
                              className="text-gray-400 hover:text-white transition-colors duration-300 text-sm"
                            >
                              {item.label}
                            </Link>
                          </li>
                        )
                      ))}
                    </ul>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Quick Links */}
          <div className="space-y-8">
            <div>
              <h4 className="text-lg font-semibold mb-6">Liens Rapides</h4>
              <ul className="space-y-3">
                {quickLinks.map((link) => (
                  <li key={link.name}>
                    <Link
                      href={link.href}
                      className="text-gray-400 hover:text-white transition-colors duration-300 flex items-center group"
                    >
                      <ArrowRight className="w-3 h-3 mr-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                      {link.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>

            {/* Emergency Contact */}
            {/* <div className="bg-gray-800 rounded-lg p-6 space-y-4">
              <h5 className="font-semibold text-primary">Urgences 24/7</h5>
              <p className="text-gray-400 text-sm">
                Pour les urgences juridiques, contactez notre ligne dédiée.
              </p>
              <Button asChild variant="outline" size="sm" className="w-full border-primary text-primary hover:bg-primary hover:text-white">
                <Link href="tel:+237682658037">
                  Ligne d'urgence
                </Link>
              </Button>
            </div> */}
          </div>
        </div>
      </div>

      {/* Bottom Footer */}
      <div className="border-t border-gray-800">
        <div className="container mx-auto px-4 py-8">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <div className="text-gray-400 text-sm">
              © {currentYear} Charlie Oscar Consulting. Tous droits réservés.
            </div>
            
            <div className="flex flex-wrap justify-center md:justify-end gap-6">
              {legalLinks.map((link) => (
                <Link
                  key={link.name}
                  href={link.href}
                  className="text-gray-400 hover:text-white transition-colors duration-300 text-sm"
                >
                  {link.name}
                </Link>
              ))}
            </div>
          </div>
          
          <div className="mt-8 pt-8 border-t border-gray-800 text-center">
            <p className="text-gray-500 text-xs">
              Développé avec ❤️ pour accompagner vos projets au Cameroun
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
}
