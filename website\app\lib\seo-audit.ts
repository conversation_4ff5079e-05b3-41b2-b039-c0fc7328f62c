// SEO Audit utilities for Charlie Oscar Consulting website

export interface SEOAuditResult {
  score: number;
  issues: SEOIssue[];
  recommendations: string[];
  passed: SEOCheck[];
}

export interface SEOIssue {
  type: 'error' | 'warning' | 'info';
  category: 'meta' | 'images' | 'structure' | 'performance' | 'content';
  message: string;
  element?: string;
  fix?: string;
}

export interface SEOCheck {
  category: string;
  description: string;
}

// Audit page SEO
export function auditPageSEO(document: Document): SEOAuditResult {
  const issues: SEOIssue[] = [];
  const passed: SEOCheck[] = [];
  let score = 100;

  // Check meta tags
  const metaAudit = auditMetaTags(document);
  issues.push(...metaAudit.issues);
  passed.push(...metaAudit.passed);
  score -= metaAudit.issues.length * 5;

  // Check images
  const imageAudit = auditImages(document);
  issues.push(...imageAudit.issues);
  passed.push(...imageAudit.passed);
  score -= imageAudit.issues.length * 3;

  // Check structure
  const structureAudit = auditStructure(document);
  issues.push(...structureAudit.issues);
  passed.push(...structureAudit.passed);
  score -= structureAudit.issues.length * 4;

  // Check content
  const contentAudit = auditContent(document);
  issues.push(...contentAudit.issues);
  passed.push(...contentAudit.passed);
  score -= contentAudit.issues.length * 2;

  const recommendations = generateRecommendations(issues);

  return {
    score: Math.max(0, score),
    issues,
    recommendations,
    passed
  };
}

// Audit meta tags
function auditMetaTags(document: Document) {
  const issues: SEOIssue[] = [];
  const passed: SEOCheck[] = [];

  // Check title
  const title = document.querySelector('title');
  if (!title || !title.textContent) {
    issues.push({
      type: 'error',
      category: 'meta',
      message: 'Missing page title',
      element: '<title>',
      fix: 'Add a descriptive title tag'
    });
  } else if (title.textContent.length > 60) {
    issues.push({
      type: 'warning',
      category: 'meta',
      message: 'Title too long (>60 characters)',
      element: '<title>',
      fix: 'Shorten title to under 60 characters'
    });
  } else {
    passed.push({
      category: 'meta',
      description: 'Title tag present and optimal length'
    });
  }

  // Check meta description
  const description = document.querySelector('meta[name="description"]');
  if (!description || !description.getAttribute('content')) {
    issues.push({
      type: 'error',
      category: 'meta',
      message: 'Missing meta description',
      element: '<meta name="description">',
      fix: 'Add a compelling meta description'
    });
  } else {
    const content = description.getAttribute('content') || '';
    if (content.length > 160) {
      issues.push({
        type: 'warning',
        category: 'meta',
        message: 'Meta description too long (>160 characters)',
        element: '<meta name="description">',
        fix: 'Shorten description to under 160 characters'
      });
    } else {
      passed.push({
        category: 'meta',
        description: 'Meta description present and optimal length'
      });
    }
  }

  // Check Open Graph tags
  const ogTitle = document.querySelector('meta[property="og:title"]');
  const ogDescription = document.querySelector('meta[property="og:description"]');
  const ogImage = document.querySelector('meta[property="og:image"]');

  if (!ogTitle) {
    issues.push({
      type: 'warning',
      category: 'meta',
      message: 'Missing Open Graph title',
      element: '<meta property="og:title">',
      fix: 'Add Open Graph title for social sharing'
    });
  } else {
    passed.push({
      category: 'meta',
      description: 'Open Graph title present'
    });
  }

  if (!ogDescription) {
    issues.push({
      type: 'warning',
      category: 'meta',
      message: 'Missing Open Graph description',
      element: '<meta property="og:description">',
      fix: 'Add Open Graph description for social sharing'
    });
  } else {
    passed.push({
      category: 'meta',
      description: 'Open Graph description present'
    });
  }

  if (!ogImage) {
    issues.push({
      type: 'warning',
      category: 'meta',
      message: 'Missing Open Graph image',
      element: '<meta property="og:image">',
      fix: 'Add Open Graph image for social sharing'
    });
  } else {
    passed.push({
      category: 'meta',
      description: 'Open Graph image present'
    });
  }

  return { issues, passed };
}

// Audit images
function auditImages(document: Document) {
  const issues: SEOIssue[] = [];
  const passed: SEOCheck[] = [];
  
  const images = document.querySelectorAll('img');
  let imagesWithAlt = 0;
  let imagesWithoutAlt = 0;

  images.forEach((img, index) => {
    const alt = img.getAttribute('alt');
    const src = img.getAttribute('src');

    if (!alt || alt.trim() === '') {
      imagesWithoutAlt++;
      issues.push({
        type: 'warning',
        category: 'images',
        message: `Image missing alt text: ${src || `image ${index + 1}`}`,
        element: '<img>',
        fix: 'Add descriptive alt text for accessibility and SEO'
      });
    } else {
      imagesWithAlt++;
    }

    // Check for lazy loading
    const loading = img.getAttribute('loading');
    if (!loading && index > 2) { // First 3 images can be eager loaded
      issues.push({
        type: 'info',
        category: 'performance',
        message: `Consider lazy loading for image: ${src || `image ${index + 1}`}`,
        element: '<img>',
        fix: 'Add loading="lazy" attribute'
      });
    }
  });

  if (imagesWithAlt > 0) {
    passed.push({
      category: 'images',
      description: `${imagesWithAlt} images have alt text`
    });
  }

  return { issues, passed };
}

// Audit page structure
function auditStructure(document: Document) {
  const issues: SEOIssue[] = [];
  const passed: SEOCheck[] = [];

  // Check heading structure
  const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
  const h1s = document.querySelectorAll('h1');

  if (h1s.length === 0) {
    issues.push({
      type: 'error',
      category: 'structure',
      message: 'Missing H1 tag',
      element: '<h1>',
      fix: 'Add a single H1 tag with main page topic'
    });
  } else if (h1s.length > 1) {
    issues.push({
      type: 'warning',
      category: 'structure',
      message: 'Multiple H1 tags found',
      element: '<h1>',
      fix: 'Use only one H1 tag per page'
    });
  } else {
    passed.push({
      category: 'structure',
      description: 'Single H1 tag present'
    });
  }

  // Check for structured data
  const structuredData = document.querySelectorAll('script[type="application/ld+json"]');
  if (structuredData.length === 0) {
    issues.push({
      type: 'warning',
      category: 'structure',
      message: 'No structured data found',
      element: '<script type="application/ld+json">',
      fix: 'Add JSON-LD structured data for better search understanding'
    });
  } else {
    passed.push({
      category: 'structure',
      description: 'Structured data present'
    });
  }

  return { issues, passed };
}

// Audit content
function auditContent(document: Document) {
  const issues: SEOIssue[] = [];
  const passed: SEOCheck[] = [];

  // Check content length
  const textContent = document.body.textContent || '';
  const wordCount = textContent.split(/\s+/).length;

  if (wordCount < 300) {
    issues.push({
      type: 'warning',
      category: 'content',
      message: 'Content may be too short for SEO',
      fix: 'Consider adding more valuable content (aim for 300+ words)'
    });
  } else {
    passed.push({
      category: 'content',
      description: `Adequate content length (${wordCount} words)`
    });
  }

  // Check for internal links
  const internalLinks = document.querySelectorAll('a[href^="/"], a[href^="#"]');
  if (internalLinks.length === 0) {
    issues.push({
      type: 'info',
      category: 'content',
      message: 'No internal links found',
      fix: 'Add internal links to improve site navigation and SEO'
    });
  } else {
    passed.push({
      category: 'content',
      description: `${internalLinks.length} internal links found`
    });
  }

  return { issues, passed };
}

// Generate recommendations based on issues
function generateRecommendations(issues: SEOIssue[]): string[] {
  const recommendations: string[] = [];
  const categories = [...new Set(issues.map(issue => issue.category))];

  categories.forEach(category => {
    const categoryIssues = issues.filter(issue => issue.category === category);
    const errorCount = categoryIssues.filter(issue => issue.type === 'error').length;
    const warningCount = categoryIssues.filter(issue => issue.type === 'warning').length;

    if (errorCount > 0) {
      recommendations.push(`Fix ${errorCount} critical ${category} issues immediately`);
    }
    if (warningCount > 0) {
      recommendations.push(`Address ${warningCount} ${category} warnings to improve SEO`);
    }
  });

  // General recommendations
  if (issues.length === 0) {
    recommendations.push('Great! No major SEO issues found. Continue monitoring performance.');
  } else if (issues.length > 10) {
    recommendations.push('Consider implementing a comprehensive SEO audit and fix plan');
  }

  return recommendations;
}
