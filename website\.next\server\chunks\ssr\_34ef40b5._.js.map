{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/charlie-oscar-consulting/website/lib/foncier-services-data.ts"], "sourcesContent": ["export interface ProcessStep {\r\n  id: number;\r\n  title: string;\r\n  description: string;\r\n  duration?: string;\r\n  documents?: string[];\r\n}\r\n\r\nexport interface Testimonial {\r\n  id: number;\r\n  name: string;\r\n  role: string;\r\n  location: string;\r\n  content: string;\r\n  rating: number;\r\n}\r\n\r\nexport interface FAQ {\r\n  id: number;\r\n  question: string;\r\n  answer: string;\r\n}\r\n\r\nexport interface FoncierService {\r\n  id: string;\r\n  slug: string;\r\n  title: string;\r\n  shortDescription: string;\r\n  detailedDescription: string;\r\n  documents?: string[];\r\n  process?: ProcessStep[];\r\n  testimonials?: Testimonial[];\r\n  faq?: FAQ[];\r\n  featured: boolean;\r\n  category: string;\r\n  estimatedDuration: string;\r\n  complexity: 'Simple' | 'Modéré' | 'Complexe';\r\n  prestations?: { title: string; description: string }[];\r\n  advantages?: { title: string; description: string }[];\r\n  whenToCall?: string[];\r\n}\r\n\r\n\r\nexport const foncierServices: FoncierService[] = [\r\n  {\r\n    id: \"1\",\r\n    slug: \"derogation-speciale\",\r\n    title: \"Dérogation Spéciale\",\r\n    shortDescription: \"Obtenez l'autorisation ministérielle pour initier votre immatriculation directe ou votre concession domaniale\",\r\n    detailedDescription: \"Une dérogation spéciale est une autorisation administrative émise par le ministre des Domaines, autorisant par dérogation l'immatriculation cadastrale ou l'octroi d'une concession domaniale dans le cadre du droit foncier. \",\r\n    category: \"Procédures spéciales\",\r\n    estimatedDuration: \"3-6 mois\",\r\n    complexity: \"Complexe\",\r\n    featured: true,\r\n    prestations: [\r\n      { title: \"Analyse approfondie de l'éligibilité\", description: \"Évaluation de la situation de votre terrain et de votre projet pour déterminer la faisabilité et les fondements juridiques d'une dérogation.\" },\r\n      { title: \"Constitution et dépôt du dossier ministériel\", description: \"Préparation exhaustive de tous les documents requis et dépôt officiel auprès du Ministère des Domaines, du Cadastre et des Affaires Foncières (MINDCAF).\" },\r\n      { title: \"Suivi personnalisé et relances administratives\", description: \"Coordination et suivi proactif de votre dossier auprès des différentes administrations impliquées, avec des relances régulières pour accélérer le processus.\" },\r\n      { title: \"Rédaction de requêtes et mémoires justificatifs\", description: \"Élaboration des arguments juridiques et des courriers officiels pour appuyer votre demande de dérogation.\" },\r\n      { title: \"Conseils stratégiques et juridiques\", description: \"Orientation sur les meilleures approches et solutions pour surmonter les obstacles et sécuriser l'obtention de votre dérogation.\" }\r\n    ],\r\n    advantages: [\r\n      { title: \"Légalisation de situations foncières complexes\", description: \"Permet de régulariser des projets ou des occupations de terrain qui sortent du cadre réglementaire habituel.\" },\r\n      { title: \"Sécurisation de vos futurs droits fonciers\", description: \"La dérogation est la première étape essentielle pour obtenir un titre foncier ou une concession sur un bien atypique.\" },\r\n      { title: \"Accélération des processus d'immatriculation/concession\", description: \"Notre expertise des rouages administratifs réduit les délais et les risques de blocage.\" },\r\n      { title: \"Conformité administrative garantie\", description: \"La procédure est menée dans le respect strict des textes de loi, évitant les litiges ultérieurs.\" }\r\n    ],\r\n    whenToCall: [\r\n      \"Votre projet foncier (construction, développement) ne respecte pas les normes urbanistiques ou foncières standard et nécessite une autorisation exceptionnelle.\",\r\n      \"Vous souhaitez immatriculer un terrain ou obtenir une concession sur un bien présentant des spécificités juridiques ou des contraintes particulières.\",\r\n      \"Vous avez besoin d'une décision ministérielle pour débloquer une situation foncière complexe ou initiée de manière non conventionnelle.\"\r\n    ]\r\n  },\r\n  {\r\n    id: \"2\",\r\n    slug: \"dossier-technique\",\r\n    title: \"Dossier Technique\",\r\n    shortDescription: \"Études et validation technique nécessaires pour les projets fonciers.\",\r\n    detailedDescription: \"Un dossier technique est un document essentiel lors de l'acquisition d'un terrain. Il contient des informations cruciales telles que la délimitation précise du terrain, les éventuelles servitudes et le certificat de propriété de la parcelle. Ce dossier est établi par un géomètre agréé et permet de garantir l'exactitude des dimensions et des limites du terrain. \",\r\n    category: \"Études techniques\",\r\n    estimatedDuration: \"2-4 mois\",\r\n    complexity: \"Modéré\",\r\n    featured: false,\r\n    prestations: [\r\n      { title: \"Bornage et délimitation officielle\", description: \"Mise en place ou vérification des bornes de votre terrain par un géomètre assermenté pour définir ses limites exactes.\" },\r\n      { title: \"Établissement du plan topographique\", description: \"Création d'un plan détaillé de votre parcelle incluant les caractéristiques du terrain et des environs.\" },\r\n      { title: \"Rédaction du procès-verbal de bornage\", description: \"Formalisation du document officiel attestant des limites du terrain, signé par toutes les parties concernées.\" },\r\n      { title: \"Obtention du certificat de propriété\", description: \"Vérification et obtention des documents prouvant la propriété et les éventuelles charges foncières.\" },\r\n      { title: \"Validation et enregistrement cadastral\", description: \"Dépôt et suivi du dossier auprès des services du Cadastre pour son approbation et son enregistrement.\" }\r\n    ],\r\n    advantages: [\r\n      { title: \"Sécurisation de vos acquisitions foncières\", description: \"Assure l'exactitude des dimensions et limites du terrain, évitant les surprises et les litiges post-achat.\" },\r\n      { title: \"Clarté des limites de propriété\", description: \"Définit sans équivoque les frontières de votre parcelle, prévenant les conflits avec les voisins.\" },\r\n      { title: \"Conformité légale et réglementaire\", description: \"Indispensable pour toute transaction ou projet foncier, garantissant la validité juridique de vos démarches.\" },\r\n      { title: \"Facilite les démarches ultérieures\", description: \"Un dossier technique à jour et validé simplifie les demandes de permis de construire, morcellement, ou vente.\" }\r\n    ],\r\n    whenToCall: [\r\n      \"Vous envisagez d'acheter ou de vendre un terrain et avez besoin de la certitude de ses limites et de sa superficie.\",\r\n      \"Vous souhaitez construire sur votre terrain et avez besoin d'un plan précis et conforme.\",\r\n      \"Vous avez des doutes ou des désaccords avec un voisin concernant les limites de votre propriété.\",\r\n      \"Vous préparez un dossier pour l'obtention d'un titre foncier ou pour un morcellement.\"\r\n    ]\r\n  },\r\n  {\r\n    id: \"3\",\r\n    slug: \"immatriculation-directe\",\r\n    title: \"Immatriculation Directe\",\r\n    shortDescription: \"Nous facilitons l'immatriculation directe de votre terrain occupé pour sécuriser votre bien.\",\r\n    detailedDescription: \"L’immatriculation directe est un processus administratif qui permet d’obtenir un titre foncier ( une reconnaissance officielle des droits sur le terrain.) pour un terrain appartenant au domaine national de première catégorie (occupé avant le 5 août 1974), ou dont l’occupation antérieure a été mise en valeur.\",\r\n    category: \"Immobilier\",\r\n    estimatedDuration: \"3-6 mois\",\r\n    complexity: \"Modéré\",\r\n    featured: true,\r\n    prestations: [\r\n      { title: \"Constitution complète du dossier de demande\", description: \"Collecte, vérification et organisation de tous les documents requis (demande timbrée, croquis, attestations d'occupation, etc.).\" },\r\n      { title: \"Dépôt et suivi diligent de la procédure\", description: \"Enregistrement de votre dossier auprès des services fonciers et suivi rigoureux à chaque étape administrative.\" },\r\n      { title: \"Accompagnement lors de la descente de la commission consultative\", description: \"Préparation et assistance durant la visite de la commission chargée de constater l'occupation et la mise en valeur de votre terrain.\" },\r\n      { title: \"Gestion des publications légales et délais d'opposition\", description: \"Veille et suivi des publications officielles, et gestion des éventuelles oppositions dans les délais légaux.\" },\r\n      { title: \"Formalités de bornage et paiement des redevances\", description: \"Prise en charge des démarches de bornage et gestion des paiements des taxes et redevances foncières.\" }\r\n    ],\r\n    advantages: [\r\n      { title: \"Obtention d'un Titre Foncier définitif\", description: \"Votre propriété est reconnue officiellement et devient inattaquable, vous conférant des droits absolus.\" },\r\n      { title: \"Sécurisation juridique de votre patrimoine\", description: \"Protège votre bien contre les spoliations, les litiges et les revendications de tiers, assurant une possession paisible.\" },\r\n      { title: \"Valorisation de votre bien immobilier\", description: \"Un terrain titré prend de la valeur et est plus facile à vendre, à léguer ou à utiliser comme garantie.\" },\r\n      { title: \"Accès facilité aux services bancaires\", description: \"Le titre foncier est une garantie acceptée par les banques pour l'obtention de crédits et de financements.\" }\r\n    ],\r\n    whenToCall: [\r\n      \"Vous occupez un terrain non titré depuis de nombreuses années (avant le 5 août 1974) et l'avez mis en valeur de manière significative.\",\r\n      \"Vous avez hérité d'un terrain dont le statut foncier n'a jamais été régularisé et vous souhaitez en devenir le propriétaire officiel.\",\r\n      \"Vous désirez vendre votre terrain, mais celui-ci n'est pas encore titré et vous voulez le sécuriser pour la transaction.\"\r\n    ]\r\n  },\r\n  {\r\n    id: \"4\",\r\n    slug: \"achat-terrain-non-titre\",\r\n    title: \"Achat de Terrain Non Titré\",\r\n    shortDescription: \" Acquérez un terrain non immatriculé en toute sécurité.\",\r\n    detailedDescription: \"L’achat d’un terrain non titré comporte des risques. Nous vérifions les antécédents fonciers, sécurisons la transaction et vous accompagnons dans les démarches administratives pour obtenir un titre foncier. \",\r\n    category: \"Transaction\",\r\n    estimatedDuration: \"2-5 mois\",\r\n    complexity: \"Modéré\",\r\n    featured: false,\r\n    prestations: [\r\n      { title: \"Audit foncier approfondi et vérification des antécédents\", description: \"Recherche minutieuse de l'historique du terrain, des propriétaires précédents et identification des risques de litiges ou de doubles ventes.\" },\r\n      { title: \"Assistance à la négociation et rédaction des actes de cession\", description: \"Accompagnement dans les discussions avec le vendeur et élaboration des documents juridiques pertinents (convention de vente, abandon de droits coutumiers).\" },\r\n      { title: \"Obtention des visas administratifs nécessaires\", description: \"Démarches pour l'obtention des autorisations requises, notamment pour les acquéreurs étrangers ou non-nationaux.\" },\r\n      { title: \"Légalisation et authentification des documents\", description: \"Validation des actes de vente auprès des autorités locales (chefferies, sous-préfectures) pour leur conférer une force probante.\" },\r\n      { title: \"Accompagnement vers l'immatriculation du terrain\", description: \"Prise en charge des démarches post-acquisition pour transformer le terrain non titré en un bien doté d'un titre foncier.\" }\r\n    ],\r\n    advantages: [\r\n      { title: \"Réduction drastique des risques d'arnaque\", description: \"Notre expertise vous protège des vendeurs frauduleux et des litiges coûteux après l'achat.\" },\r\n      { title: \"Sécurisation progressive de votre investissement\", description: \"Nous mettons en place des garanties juridiques pour protéger votre acquisition, même avant l'obtention du titre foncier.\" },\r\n      { title: \"Obtention d'un titre foncier à terme\", description: \"Notre accompagnement vise l'immatriculation de votre terrain, garantissant sa sécurité et sa valorisation à long terme.\" },\r\n      { title: \"Tranquillité d'esprit et confiance\", description: \"Vous achetez votre terrain en toute sérénité, sachant que toutes les précautions ont été prises pour protéger votre bien.\" }\r\n    ],\r\n    whenToCall: [\r\n      \"Vous avez trouvé un terrain à acquérir qui ne possède pas encore de titre foncier.\",\r\n      \"Vous souhaitez acheter un terrain et vous voulez être sûr d'éviter les problèmes juridiques et les arnaques foncières.\",\r\n      \"Vous êtes un investisseur ou un particulier cherchant à acquérir un terrain non titré et vous avez besoin d'un cadre légal sécurisé.\",\r\n      \"Vous désirez un accompagnement complet, de la vérification initiale à l'obtention du titre foncier.\"\r\n    ]\r\n  },\r\n  {\r\n    id: \"5\",\r\n    slug: \"concession-domaniale\",\r\n    title: \"Concession Domaniale\",\r\n    shortDescription: \"Nous vous guidons pour obtenir une concession temporaire ou définitive sur un terrain du domaine public ou national de 2nde catégorie.\",\r\n    detailedDescription: \"La concession domaniale au Cameroun est la procédure par laquelle l'État cède un terrain du domaine public ou national à une personne, pour un projet de développement, moyennant le respect d'un cahier des charges et le paiement de redevances foncières. Elle se déroule en deux phases : provisoire et définitive, et implique des démarches administratives auprès des services des domaines.\",\r\n    category: \"Domaine public\",\r\n    estimatedDuration: \"6-12 mois\",\r\n    complexity: \"Complexe\",\r\n    featured: true,\r\n    prestations: [\r\n      { title: \"Élaboration du dossier de concession provisoire\", description: \"Préparation minutieuse des documents requis, incluant le projet de mise en valeur détaillé et le plan de situation du terrain.\" },\r\n      { title: \"Suivi auprès des administrations (Préfecture, MINDCAF)\", description: \"Dépôt du dossier et suivi rigoureux auprès des services préfectoraux et du Ministère des Domaines pour l'obtention de l'arrêté de concession provisoire.\" },\r\n      { title: \"Accompagnement pour le constat de mise en valeur\", description: \"Préparation de votre dossier et de votre site pour la visite de la commission chargée de vérifier l'effectivité de votre projet.\" },\r\n      { title: \"Constitution du dossier de concession définitive\", description: \"Préparation des pièces et formalités pour la transformation de la concession provisoire en concession définitive, après validation de la mise en valeur.\" },\r\n      { title: \"Gestion des redevances et obligations contractuelles\", description: \"Calcul et gestion des paiements des redevances foncières et suivi du respect des clauses du cahier des charges.\" }\r\n    ],\r\n    advantages: [\r\n      { title: \"Accès à des terrains adaptés aux projets d'envergure\", description: \"Permet de développer des projets agricoles, industriels, ou d'infrastructures sur des superficies importantes du domaine national.\" },\r\n      { title: \"Sécurisation de l'usage du terrain sur le long terme\", description: \"La concession offre un cadre légal pour l'exploitation et la jouissance du terrain, avec la possibilité d'une immatriculation future.\" },\r\n      { title: \"Reconnaissance et soutien institutionnel\", description: \"L'obtention d'une concession prouve la validation et le soutien de l'État pour votre projet, facilitant d'autres démarches.\" },\r\n      { title: \"Possibilité de conversion en Titre Foncier\", description: \"Après la phase de mise en valeur et l'obtention de la concession définitive, le terrain peut être immatriculé à votre nom.\" }\r\n    ],\r\n    whenToCall: [\r\n      \"Vous avez un projet de développement (agricole, industriel, immobilier) qui nécessite une parcelle de grande taille, située sur le domaine public ou national de 2nde catégorie.\",\r\n      \"Vous souhaitez obtenir une autorisation légale d'occuper et d'exploiter un terrain appartenant à l'État.\",\r\n      \"Votre projet implique un engagement à mettre en valeur le terrain et à respecter un cahier des charges spécifique.\"\r\n    ]\r\n  },\r\n  {\r\n    id: \"6\",\r\n    slug: \"achat-gre-a-gre\",\r\n    title: \"Achat de Gré à Gré (Lotissement Domanial)\",\r\n    shortDescription: \"devenez propriétaire d'une parcelle issue du domaine privé de l'État\",\r\n    detailedDescription: \"L'achat d'un terrain par 'gré à gré' dans un lotissement domanial au Cameroun implique une procédure spécifique. Il s'agit d'une vente directe entre l'État (représenté par les services du domaine) et l'acquéreur, sans passer par une adjudication publique\",\r\n    category: \"Transaction\",\r\n    estimatedDuration: \"3-6 mois\",\r\n    complexity: \"Modéré\",\r\n    featured: false,\r\n    prestations: [\r\n      { title: \"Dépôt de la demande d'acquisition\", description: \"Préparation et soumission de votre demande d'attribution de terrain par vente de gré à gré au Ministre des Domaines, sous couvert du Préfet.\" },\r\n      { title: \"Obtention des autorisations préfectorales et ministérielles\", description: \"Suivi actif du dossier pour obtenir les arrêtés préfectoraux et les décisions ministérielles autorisant la vente directe.\" },\r\n      { title: \"Gestion des frais et redevances foncières\", description: \"Prise en charge du calcul et du paiement des frais d'ouverture de dossier et des redevances dues à l'État.\" },\r\n      { title: \"Établissement et approbation de l'acte de vente\", description: \"Rédaction de l'acte de vente officiel entre l'État et vous, et assurance de son approbation par les autorités compétentes.\" },\r\n      { title: \"Finalisation de l'obtention du titre foncier\", description: \"Accompagnement jusqu'à l'enregistrement de l'acte de vente et la délivrance du titre foncier définitif à votre nom.\" }\r\n    ],\r\n    advantages: [\r\n      { title: \"Acquisition sécurisée d'une propriété de l'État\", description: \"Vous devenez propriétaire d'un terrain dont l'origine est officielle, incontestable et garantie par l'État.\" },\r\n      { title: \"Processus d'acquisition simplifié et direct\", description: \"Évitez les complexités et les incertitudes des adjudications publiques, en optant pour une vente directe.\" },\r\n      { title: \"Accès à des parcelles dans des lotissements officiels\", description: \"Bénéficiez de terrains déjà viabilisés ou intégrés dans des plans d'urbanisme, facilitant vos projets de construction.\" },\r\n      { title: \"Titre Foncier définitif et garanti\", description: \"La procédure aboutit à l'obtention d'un titre foncier en bonne et due forme, sécurisant pleinement votre investissement.\" }\r\n    ],\r\n    whenToCall: [\r\n      \"Vous souhaitez acquérir une parcelle située dans un lotissement domanial (terrain appartenant au domaine privé de l'État).\",\r\n      \"Vous préférez une transaction directe et sécurisée avec l'État plutôt que de passer par des enchères publiques.\",\r\n      \"Vous recherchez un terrain dont la légitimité et l'historique sont garantis par l'administration foncière.\",\r\n      \"Vous avez un projet de construction ou d'investissement nécessitant un terrain officiel et facilement titrable.\"\r\n    ]\r\n  },\r\n  {\r\n    id: \"7\",\r\n    slug: \"rehabilitation-titres-fonciers\",\r\n    title: \"Réhabilitation des Titres Fonciers\",\r\n    shortDescription: \"Restauration de titres fonciers perdus, abîmés ou irréguliers\",\r\n    detailedDescription: \"La réhabilitation des titres fonciers est un processus visant à corriger les irrégularités ou erreurs dans les titres fonciers existants, afin d'assurer la légitimité des droits de propriété.\",\r\n    category: \"Procédures correctives\",\r\n    estimatedDuration: \"2-6 mois\",\r\n    complexity: \"Modéré\",\r\n    featured: false,\r\n    prestations: [\r\n      { title: \"Analyse approfondie des irrégularités\", description: \"Identification précise des erreurs, omissions, chevauchements ou cas de duplicata de votre titre foncier.\" },\r\n      { title: \"Constitution du dossier de recours/rectification\", description: \"Préparation des documents justificatifs, preuves et arguments juridiques pour corriger les anomalies de votre titre.\" },\r\n      { title: \"Dépôt et suivi auprès du MINDCAF et des tribunaux\", description: \"Enregistrement de la demande auprès des services des Domaines, Cadastre et Affaires Foncières, et suivi actif de la procédure, y compris les démarches judiciaires si nécessaire.\" },\r\n      { title: \"Rédaction des requêtes et plaidoyers\", description: \"Élaboration des correspondances officielles, mémoires et argumentaires pour défendre vos droits devant les autorités compétentes.\" },\r\n      { title: \"Obtention du titre foncier réhabilité/corrigé\", description: \"Accompagnement jusqu'à la délivrance d'un nouveau titre foncier, débarrassé de toute irrégularité et pleinement valide.\" }\r\n    ],\r\n    advantages: [\r\n      { title: \"Sécurité et validité juridique retrouvées\", description: \"Votre titre foncier redevient un document légalement inattaquable, garantissant vos droits de propriété.\" },\r\n      { title: \"Prévention des litiges et fraudes\", description: \"La correction des irrégularités élimine les risques de contestation, de double vente ou de spoliation de votre bien.\" },\r\n      { title: \"Valorisation de votre patrimoine immobilier\", description: \"Un titre foncier sain facilite grandement la vente, l'hypothèque ou la transmission de votre propriété.\" },\r\n      { title: \"Tranquillité d'esprit et confiance\", description: \"Vous retrouvez la sérénité en sachant que votre propriété est légalement protégée et sans défauts.\" }\r\n    ],\r\n    whenToCall: [\r\n      \"Votre titre foncier comporte des erreurs matérielles (faute de frappe, mauvaise description, superficie inexacte).\",\r\n      \"Vous constatez qu'un même terrain fait l'objet de plusieurs titres fonciers (duplicata).\",\r\n      \"Votre titre foncier a été perdu, volé, ou est illisible/endommagé.\",\r\n      \"Une décision administrative a retiré, annulé ou modifié votre titre foncier et vous souhaitez la contester.\"\r\n    ]\r\n  },\r\n  {\r\n    id: \"12\",\r\n    slug: \"mutation-par-deces\",\r\n    title: \"Mutation par Décès\",\r\n    shortDescription: \"Transférez à votre nom votre terrain hérité grâce à un accompagnement complet du dossier\",\r\n    detailedDescription: \"La mutation par décès désigne le transfert des droits de propriété d'un bien immobilier (immeuble ou terrain) d'une personne décédée à ses héritiers. Ce processus est essentiel pour régulariser la situation foncière après un décès et permettre aux héritiers d'exercer leurs droits sur les biens. \",\r\n    category: \"Succession\",\r\n    estimatedDuration: \"2-6 mois\",\r\n    complexity: \"Modéré\",\r\n    featured: true,\r\n    prestations: [\r\n      { title: \"Analyse complète de la succession\", description: \"Évaluation de la situation familiale, identification de tous les héritiers légaux et vérification des documents de succession (testament, état civil).\" },\r\n      { title: \"Obtention des actes d'hérédité et certificats\", description: \"Assistance pour l'obtention du jugement d'hérédité, du certificat de décès, du certificat de non-opposition et autres documents notariaux requis.\" },\r\n      { title: \"Constitution et dépôt du dossier de mutation\", description: \"Préparation exhaustive de tous les documents nécessaires et dépôt officiel auprès du Conservateur Foncier compétent.\" },\r\n      { title: \"Suivi des publications légales et délais\", description: \"Vérification des publications obligatoires au bulletin foncier et gestion des délais légaux pour les éventuelles oppositions.\" },\r\n      { title: \"Retrait du nouveau titre foncier\", description: \"Accompagnement jusqu'à la délivrance du titre foncier mis à jour, établi au nom des héritiers ou de l'ayant droit.\" }\r\n    ],\r\n    advantages: [\r\n      { title: \"Officialisation de la propriété pour les héritiers\", description: \"Les héritiers deviennent légalement propriétaires du bien, avec un titre foncier incontestable à leur nom.\" },\r\n      { title: \"Sécurisation juridique de l'héritage\", description: \"Protège le bien immobilier des litiges futurs, des spoliations et des revendications infondées, assurant une transmission paisible.\" },\r\n      { title: \"Facilitation de la gestion et de la vente du bien\", description: \"Un titre foncier à jour simplifie toute opération future, qu'il s'agisse de vendre, de louer, de morceler ou d'hypothéquer le terrain.\" },\r\n      { title: \"Prévention des conflits familiaux\", description: \"Une procédure transparente et légale aide à distribuer les biens conformément à la loi, minimisant les désaccords entre cohéritiers.\" }\r\n    ],\r\n    whenToCall: [\r\n      \"Le propriétaire d'un terrain ou d'un immeuble est décédé et le titre foncier est toujours à son nom.\",\r\n      \"Vous êtes un héritier et souhaitez formaliser légalement votre part de propriété sur le terrain hérité.\",\r\n      \"Vous envisagez de vendre, de partager ou de réaliser des opérations sur un bien immobilier issu d'une succession.\"\r\n    ]\r\n  },\r\n  {\r\n    id: \"8\",\r\n    slug: \"morcellement-mutation-achat\",\r\n    title: \"Morcellement & Mutation par Achat\",\r\n    shortDescription: \"Divisez votre terrain en parcelles distinctes et sécurisez la vente ou l'acquisition de chacune d'elles.\",\r\n    detailedDescription: \"Le morcellement implique la division d'un terrain en plusieurs lots, souvent pour la vente, tandis que la mutation par achat concerne le transfert de la propriété d'un terrain existant à un nouvel acquéreur. \",\r\n    category: \"Transaction\",\r\n    estimatedDuration: \"3-7 mois\",\r\n    complexity: \"Modéré\",\r\n    featured: false,\r\n    prestations: [\r\n      { title: \"Étude de faisabilité et élaboration du plan de morcellement\", description: \"Analyse des contraintes urbanistiques et techniques, et conception d'un plan de division qui maximise la valeur de votre terrain.\" },\r\n      { title: \"Bornage et délimitation des nouvelles parcelles\", description: \"Réalisation des opérations de bornage par un géomètre assermenté pour délimiter précisément chaque nouveau lot créé.\" },\r\n      { title: \"Constitution et validation du dossier technique de morcellement\", description: \"Préparation et dépôt de tous les documents techniques et administratifs requis auprès des services du Cadastre.\" },\r\n      { title: \"Rédaction et signature de l'acte de vente notarié\", description: \"Assistance pour la rédaction de l'acte authentique de vente de chaque parcelle et suivi de son enregistrement.\" },\r\n      { title: \"Suivi de la mutation et obtention des nouveaux titres fonciers\", description: \"Accompagnement jusqu'à la délivrance des titres fonciers individuels pour chaque parcelle morcelée et vendue.\" }\r\n    ],\r\n    advantages: [\r\n      { title: \"Optimisation de la valeur foncière de votre bien\", description: \"Diviser un grand terrain en lots plus petits augmente souvent sa valeur marchande et facilite la vente.\" },\r\n      { title: \"Sécurisation totale des transactions de vente\", description: \"Chaque parcelle vendue dispose de son propre titre foncier, offrant une sécurité maximale à l'acheteur et au vendeur.\" },\r\n      { title: \"Gestion simplifiée des propriétés\", description: \"Les nouvelles parcelles sont clairement définies et enregistrées, facilitant leur gestion individuelle.\" },\r\n      { title: \"Conformité légale et urbanistique\", description: \"Toutes les étapes du morcellement et de la mutation sont réalisées dans le strict respect de la réglementation en vigueur.\" }\r\n    ],\r\n    whenToCall: [\r\n      \"Vous êtes propriétaire d'un grand terrain et souhaitez le diviser pour vendre des parcelles séparées.\",\r\n      \"Vous avez acquis une portion d'un terrain plus grand et vous avez besoin d'un titre foncier distinct pour votre part.\",\r\n      \"Vous voulez sécuriser la transaction d'une parcelle nouvellement créée issue d'une division foncière.\",\r\n      \"Vous êtes un promoteur immobilier et souhaitez créer des lots constructibles à partir d'une grande parcelle.\"\r\n    ]\r\n  },\r\n  {\r\n    id: \"9\",\r\n    slug: \"retrocession\",\r\n    title: \"Rétrocession\",\r\n    shortDescription: \"Récupérez un terrain précédemment exproprié ou intégré dans le domaine public suite à une carence administrative ou un projet abandonné.\",\r\n    detailedDescription: \"la rétrocession se réfère au droit pour les anciens propriétaires ou leurs ayants droit de demander la restitution d'un terrain exproprié pour cause d'utilité publique, si ce terrain n'est pas utilisé conformément à la destination prévue dans le délai imparti\",\r\n    category: \"Procédures correctives\",\r\n    estimatedDuration: \"6-18 mois\",\r\n    complexity: \"Complexe\",\r\n    featured: false,\r\n    prestations: [\r\n      { title: \"Analyse de la recevabilité de la demande\", description: \"Vérification des motifs d'expropriation, du non-respect de l'affectation du terrain ou de l'abandon du projet public.\" },\r\n      { title: \"Constitution du dossier de rétrocession\", description: \"Préparation des documents justificatifs prouvant l'expropriation et la non-utilisation du terrain conformément à l'objectif initial.\" },\r\n      { title: \"Dépôt et suivi des recours administratifs\", description: \"Enregistrement de la demande de rétrocession auprès des autorités compétentes et suivi rigoureux du processus.\" },\r\n      { title: \"Négociation et représentation\", description: \"Défense de vos intérêts et dialogue avec l'administration pour parvenir à la restitution de votre terrain.\" },\r\n      { title: \"Accompagnement en cas de contentieux\", description: \"Soutien juridique et assistance si la procédure nécessite une action devant les tribunaux pour faire valoir vos droits.\" }\r\n    ],\r\n    advantages: [\r\n      { title: \"Récupération de votre propriété légitime\", description: \"Vous retrouvez la pleine jouissance d'un bien foncier qui vous a été retiré ou dont l'usage n'a pas été respecté.\" },\r\n      { title: \"Réparation d'un préjudice foncier\", description: \"Obtenez justice et compensation pour la perte ou l'usage abusif de votre terrain par l'administration.\" },\r\n      { title: \"Sécurisation de vos droits fonciers\", description: \"Le terrain est officiellement réintégré dans votre patrimoine, avec toutes les garanties juridiques.\" },\r\n      { title: \"Rétablissement de la justice administrative\", description: \"Faire valoir vos droits face aux manquements ou abus de l'administration, renforçant la sécurité foncière.\" }\r\n    ],\r\n    whenToCall: [\r\n      \"Votre terrain a été exproprié pour un projet public qui n'a pas été réalisé, a été abandonné, ou n'est pas utilisé conformément à l'objectif initial.\",\r\n      \"Vous estimez avoir subi un préjudice foncier suite à une décision administrative contestable ou non respectée.\",\r\n      \"Vous souhaitez faire valoir votre droit à la restitution d'un bien foncier intégré au domaine public sans juste cause ou compensation.\"\r\n    ]\r\n  },\r\n  {\r\n    id: \"10\",\r\n    slug: \"bornage-reconstitution\",\r\n    title: \"Bornage et Reconstitution des Bornes\",\r\n    shortDescription: \"Identifiez et délimitez précisément les limites de votre terrain avec l'aide d'un géomètre assermenté.\",\r\n    detailedDescription: \"le bornage et la reconstitution des bornes sont des procédures cadastrales importantes pour délimiter les propriétés foncières et éviter les conflits de voisinage. Le bornage consiste à fixer les limites d'un terrain à l'aide de bornes, tandis que la reconstitution concerne la remise en place de bornes manquantes ou endommagées.\",\r\n    category: \"Délimitation\",\r\n    estimatedDuration: \"1-2 mois\",\r\n    complexity: \"Modéré\",\r\n    featured: false,\r\n    prestations: [\r\n      { title: \"Analyse documentaire et cadastrale\", description: \"Étude des titres de propriété, plans cadastraux et autres documents pour établir les points de repère et les limites existantes.\" },\r\n      { title: \"Intervention d'un géomètre assermenté sur site\", description: \"Descente sur le terrain avec un expert pour identifier, mesurer et matérialiser les limites précises par la pose de bornes officielles.\" },\r\n      { title: \"Rédaction et homologation du procès-verbal de bornage\", description: \"Établissement du document officiel décrivant les opérations de bornage et les limites fixées, signé par toutes les parties et enregistré.\" },\r\n      { title: \"Gestion des notifications aux propriétaires voisins\", description: \"Information des propriétaires des parcelles contiguës et gestion des éventuelles observations ou contestations.\" },\r\n      { title: \"Conseils pour la prévention et la résolution des litiges\", description: \"Accompagnement pour prévenir ou résoudre à l'amiable les conflits de voisinage liés aux limites foncières.\" }\r\n    ],\r\n    advantages: [\r\n      { title: \"Délimitation claire et incontestable de votre propriété\", description: \"Élimine les incertitudes sur les frontières de votre terrain, évitant les empiètements et les malentendus.\" },\r\n      { title: \"Sécurité juridique de vos limites foncières\", description: \"Le procès-verbal de bornage est un document officiel qui a force probante, protégeant vos droits de propriété.\" },\r\n      { title: \"Prévention et résolution des conflits de voisinage\", description: \"Des limites clairement définies favorisent de bonnes relations et évitent les litiges coûteux avec les propriétés adjacentes.\" },\r\n      { title: \"Valorisation de votre bien immobilier\", description: \"Un terrain clairement borné est plus attractif et sécurisant pour les futurs acquéreurs ou pour tout projet de développement.\" }\r\n    ],\r\n    whenToCall: [\r\n      \"Vous avez des doutes sur les limites exactes de votre propriété ou de celles de vos voisins.\",\r\n      \"Votre terrain n'a jamais été officiellement borné ou les bornes existantes sont manquantes, déplacées ou endommagées.\",\r\n      \"Vous envisagez de construire une clôture, un mur ou de réaliser des aménagements importants en limite de propriété.\",\r\n      \"Vous préparez la vente ou l'acquisition d'un terrain et vous souhaitez garantir la précision de sa superficie et de ses limites.\"\r\n    ]\r\n  },\r\n  {\r\n    id: \"11\",\r\n    slug: \"indemnisation\",\r\n    title: \"Indemnisation\",\r\n    shortDescription: \"Obtenez une compensation équitable en cas d'expropriation ou de préjudice foncier.\",\r\n    detailedDescription: \"L'indemnisation vise à compenser les pertes subies par les individus du fait de la perte de leurs terres ou constructions. \",\r\n    category: \"Procédures correctives\",\r\n    estimatedDuration: \"6-12 mois\",\r\n    complexity: \"Complexe\",\r\n    featured: false,\r\n    prestations: [\r\n      { title: \"Évaluation du préjudice foncier et des droits\", description: \"Analyse approfondie de la situation pour déterminer la nature et l'étendue de votre préjudice et estimer la juste compensation à réclamer.\" },\r\n      { title: \"Constitution et dépôt du dossier d'indemnisation\", description: \"Préparation exhaustive des documents justificatifs (titre foncier, plans, preuves de mise en valeur, etc.) pour appuyer votre demande.\" },\r\n      { title: \"Négociation avec l'administration ou les entités expropriatrices\", description: \"Défense active de vos intérêts et recherche d'un accord amiable sur le montant de l'indemnité.\" },\r\n      { title: \"Suivi devant les commissions d'évaluation et les tribunaux\", description: \"Accompagnement et représentation lors des audiences des commissions d'évaluation et, si nécessaire, devant les juridictions compétentes.\" },\r\n      { title: \"Assistance au paiement et à la réception de l'indemnité\", description: \"Veille au bon déroulement du processus de paiement et à la réception effective de la compensation due.\" }\r\n    ],\r\n    advantages: [\r\n      { title: \"Obtention d'une indemnisation juste et équitable\", description: \"Nous veillons à ce que la compensation versée reflète la valeur réelle de votre bien et les préjudices subis (pertes de revenus, de jouissance, etc.).\" },\r\n      { title: \"Défense experte de vos droits de propriétaire\", description: \"Vous êtes accompagné par des professionnels aguerris face aux procédures d'expropriation souvent complexes et intimidantes.\" },\r\n      { title: \"Accélération du processus d'indemnisation\", description: \"Notre connaissance des procédures et des acteurs réduit les délais d'attente pour l'obtention de votre compensation.\" },\r\n      { title: \"Sécurisation juridique de vos recours\", description: \"Nous garantissons la conformité de vos démarches et la protection de vos droits face à des montants jugés insuffisants ou injustes.\" }\r\n    ],\r\n    whenToCall: [\r\n      \"Votre terrain, votre maison ou vos biens sont menacés d'expropriation pour cause d'utilité publique.\",\r\n      \"Vous avez subi un préjudice lié à un projet public (construction d'une route, d'une infrastructure) qui affecte votre propriété.\",\r\n      \"Le montant d'indemnisation proposé par l'administration est insuffisant, contestable ou ne correspond pas à la valeur de votre bien.\"\r\n    ]\r\n  },\r\n  {\r\n    id: \"13\",\r\n    slug: \"lotissement\",\r\n    title: \"Lotissement\",\r\n    shortDescription: \"Création et aménagement de lotissements en toute conformité\",\r\n    detailedDescription: \"Le lotissement consiste en la division d’un terrain pour créer des parcelles destinées à la vente ou à l’aménagement. Il nécessite une étude de faisabilité, un plan d’implantation, des autorisations urbanistiques, et un suivi des travaux. Charlie Oscar Consulting vous accompagne de la planification à l’obtention de toutes les autorisations nécessaires.\",\r\n    category: \"Aménagement\",\r\n    estimatedDuration: \"6-12 mois\",\r\n    complexity: \"Modéré\",\r\n    featured: false,\r\n    prestations: [\r\n      { title: \"Étude de faisabilité technique et réglementaire\", description: \"Analyse approfondie du terrain, des règles d'urbanisme et des potentialités de division pour votre projet de lotissement.\" },\r\n      { title: \"Conception du plan de lotissement et d'aménagement\", description: \"Élaboration de plans détaillés pour la division des parcelles, la création de voies d'accès, d'espaces verts et d'infrastructures.\" },\r\n      { title: \"Obtention des autorisations urbanistiques (permis de lotir)\", description: \"Dépôt et suivi des demandes de permis et autorisations nécessaires auprès des services d'urbanisme.\" },\r\n      { title: \"Coordination des travaux d'aménagement\", description: \"Accompagnement dans la supervision des travaux de viabilisation (voies, eau, électricité) conformément au plan approuvé.\" },\r\n      { title: \"Suivi jusqu'à la réception définitive du lotissement\", description: \"Assistance pour les démarches finales, y compris la réception des travaux et l'intégration au domaine public des équipements communs.\" }\r\n    ],\r\n    advantages: [\r\n      { title: \"Création de parcelles valorisables et attractives\", description: \"Divisez votre terrain en lots optimisés pour la vente, augmentant significativement leur valeur marchande.\" },\r\n      { title: \"Conformité totale avec les normes urbanistiques\", description: \"Votre lotissement est réalisé dans le respect strict des réglementations, évitant tout risque de blocage ou de sanction.\" },\r\n      { title: \"Sécurité juridique pour les futurs acquéreurs\", description: \"Les lots sont créés avec toutes les garanties légales, facilitant leur vente et l'obtention de titres fonciers individuels.\" },\r\n      { title: \"Gestion simplifiée de projets complexes\", description: \"Un accompagnement expert qui prend en charge toutes les étapes, de la conception à la commercialisation des lots.\" }\r\n    ],\r\n    whenToCall: [\r\n      \"Vous possédez un grand terrain et souhaitez le diviser en plusieurs parcelles constructibles pour les vendre.\",\r\n      \"Vous êtes un promoteur immobilier et avez un projet de développement résidentiel ou commercial nécessitant la création d'un lotissement.\",\r\n      \"Vous avez besoin d'une assistance complète pour les études, les plans, les autorisations et le suivi des travaux d'aménagement d'un terrain.\"\r\n    ]\r\n  },\r\n  {\r\n    id: \"15\",\r\n    slug: \"recours-gracieux\",\r\n    title: \"Recours Gracieux\",\r\n    shortDescription: \"Demande de réexamen administratif pour vos contentieux fonciers\",\r\n    detailedDescription: \"Un recours gracieux est une procédure administrative par laquelle une personne demande à l'administration de reconsidérer une décision qu'elle a prise. C'est une étape préalable obligatoire avant de saisir le juge administratif dans de nombreux cas. Le recours gracieux est un outil de droit public qui permet de résoudre amiablement des litiges administratifs. \",\r\n    category: \"Procédures correctives\",\r\n    estimatedDuration: \"3-6 mois\",\r\n    complexity: \"Modéré\",\r\n    featured: false,\r\n    prestations: [\r\n      { title: \"Analyse approfondie de la décision contestée\", description: \"Évaluation de la légalité et du bien-fondé de la décision administrative que vous souhaitez contester.\" },\r\n      { title: \"Rédaction et formalisation de la requête gracieuse\", description: \"Élaboration d'un recours détaillé et argumenté, incluant toutes les pièces justificatives et les arguments juridiques pertinents.\" },\r\n      { title: \"Dépôt et suivi proactif auprès du Ministère compétent\", description: \"Enregistrement de votre recours au MINDCAF et suivi régulier auprès de la cellule contentieux pour accélérer son traitement.\" },\r\n      { title: \"Négociation et échange avec l'administration\", description: \"Dialogue avec les services concernés pour trouver une solution amiable et obtenir la révision de la décision.\" },\r\n      { title: \"Préparation à un éventuel recours contentieux\", description: \"Si le recours gracieux n'aboutit pas, nous préparons les bases pour une action devant le juge administratif.\" }\r\n    ],\r\n    advantages: [\r\n      { title: \"Solution amiable et rapide\", description: \"Permet de résoudre un litige avec l'administration sans passer par une procédure judiciaire longue et coûteuse.\" },\r\n      { title: \"Préservation de vos droits fonciers\", description: \"Conteste une décision administrative qui affecte votre titre foncier ou vos droits sur un bien immobilier.\" },\r\n      { title: \"Expertise des procédures administratives\", description: \"Nous maîtrisons les exigences formelles et les circuits administratifs pour optimiser vos chances de succès.\" },\r\n      { title: \"Préparation à des actions plus poussées\", description: \"Même en cas d'échec, le recours gracieux est une étape obligatoire qui renforce votre position pour un recours contentieux.\" }\r\n    ],\r\n    whenToCall: [\r\n      \"Un titre foncier vous a été retiré, annulé ou modifié par l'administration sans motif valable.\",\r\n      \"Votre demande d'immatriculation, de concession ou toute autre démarche foncière a été refusée de manière contestable.\",\r\n      \"Vous souhaitez contester une décision administrative relative à un bien foncier sans engager immédiatement une procédure judiciaire.\",\r\n      \"Vous avez subi un préjudice suite à un acte administratif illégal et vous cherchez une réparation.\"\r\n    ]\r\n  }\r\n];\r\n\r\nexport function getFoncierServiceBySlug(slug: string): FoncierService | undefined {\r\n  return foncierServices.find(service => service.slug === slug);\r\n}\r\n\r\nexport function getAllFoncierServices(): FoncierService[] {\r\n  return foncierServices;\r\n}\r\n\r\nexport function getFeaturedFoncierServices(): FoncierService[] {\r\n  return foncierServices.filter(service => service.featured);\r\n}\r\n\r\nexport function getFoncierServicesByCategory(category: string): FoncierService[] {\r\n  return foncierServices.filter(service => service.category === category);\r\n}\r\n"], "names": [], "mappings": ";;;;;;;AA2CO,MAAM,kBAAoC;IAC/C;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,kBAAkB;QAClB,qBAAqB;QACrB,UAAU;QACV,mBAAmB;QACnB,YAAY;QACZ,UAAU;QACV,aAAa;YACX;gBAAE,OAAO;gBAAwC,aAAa;YAA+I;YAC7M;gBAAE,OAAO;gBAAgD,aAAa;YAA2J;YACjO;gBAAE,OAAO;gBAAkD,aAAa;YAA+J;YACvO;gBAAE,OAAO;gBAAmD,aAAa;YAA4G;YACrL;gBAAE,OAAO;gBAAuC,aAAa;YAAmI;SACjM;QACD,YAAY;YACV;gBAAE,OAAO;gBAAkD,aAAa;YAA+G;YACvL;gBAAE,OAAO;gBAA8C,aAAa;YAAwH;YAC5L;gBAAE,OAAO;gBAA2D,aAAa;YAA0F;YAC3K;gBAAE,OAAO;gBAAsC,aAAa;YAAmG;SAChK;QACD,YAAY;YACV;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,kBAAkB;QAClB,qBAAqB;QACrB,UAAU;QACV,mBAAmB;QACnB,YAAY;QACZ,UAAU;QACV,aAAa;YACX;gBAAE,OAAO;gBAAsC,aAAa;YAAyH;YACrL;gBAAE,OAAO;gBAAuC,aAAa;YAA0G;YACvK;gBAAE,OAAO;gBAAyC,aAAa;YAAgH;YAC/K;gBAAE,OAAO;gBAAwC,aAAa;YAAsG;YACpK;gBAAE,OAAO;gBAA0C,aAAa;YAAwG;SACzK;QACD,YAAY;YACV;gBAAE,OAAO;gBAA8C,aAAa;YAA6G;YACjL;gBAAE,OAAO;gBAAmC,aAAa;YAAoG;YAC7J;gBAAE,OAAO;gBAAsC,aAAa;YAA+G;YAC3K;gBAAE,OAAO;gBAAsC,aAAa;YAAgH;SAC7K;QACD,YAAY;YACV;YACA;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,kBAAkB;QAClB,qBAAqB;QACrB,UAAU;QACV,mBAAmB;QACnB,YAAY;QACZ,UAAU;QACV,aAAa;YACX;gBAAE,OAAO;gBAA+C,aAAa;YAAmI;YACxM;gBAAE,OAAO;gBAA2C,aAAa;YAAiH;YAClL;gBAAE,OAAO;gBAAoE,aAAa;YAAuI;YACjO;gBAAE,OAAO;gBAA2D,aAAa;YAA+G;YAChM;gBAAE,OAAO;gBAAoD,aAAa;YAAuG;SAClL;QACD,YAAY;YACV;gBAAE,OAAO;gBAA0C,aAAa;YAA0G;YAC1K;gBAAE,OAAO;gBAA8C,aAAa;YAA2H;YAC/L;gBAAE,OAAO;gBAAyC,aAAa;YAA0G;YACzK;gBAAE,OAAO;gBAAyC,aAAa;YAA6G;SAC7K;QACD,YAAY;YACV;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,kBAAkB;QAClB,qBAAqB;QACrB,UAAU;QACV,mBAAmB;QACnB,YAAY;QACZ,UAAU;QACV,aAAa;YACX;gBAAE,OAAO;gBAA4D,aAAa;YAA+I;YACjO;gBAAE,OAAO;gBAAiE,aAAa;YAA8J;YACrP;gBAAE,OAAO;gBAAkD,aAAa;YAAmH;YAC3L;gBAAE,OAAO;gBAAkD,aAAa;YAAmI;YAC3M;gBAAE,OAAO;gBAAoD,aAAa;YAA2H;SACtM;QACD,YAAY;YACV;gBAAE,OAAO;gBAA6C,aAAa;YAA6F;YAChK;gBAAE,OAAO;gBAAoD,aAAa;YAA2H;YACrM;gBAAE,OAAO;gBAAwC,aAAa;YAA0H;YACxL;gBAAE,OAAO;gBAAsC,aAAa;YAA4H;SACzL;QACD,YAAY;YACV;YACA;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,kBAAkB;QAClB,qBAAqB;QACrB,UAAU;QACV,mBAAmB;QACnB,YAAY;QACZ,UAAU;QACV,aAAa;YACX;gBAAE,OAAO;gBAAmD,aAAa;YAAiI;YAC1M;gBAAE,OAAO;gBAA0D,aAAa;YAA2J;YAC3O;gBAAE,OAAO;gBAAoD,aAAa;YAAmI;YAC7M;gBAAE,OAAO;gBAAoD,aAAa;YAA2J;YACrO;gBAAE,OAAO;gBAAwD,aAAa;YAAkH;SACjM;QACD,YAAY;YACV;gBAAE,OAAO;gBAAwD,aAAa;YAAqI;YACnN;gBAAE,OAAO;gBAAwD,aAAa;YAAwI;YACtN;gBAAE,OAAO;gBAA4C,aAAa;YAA8H;YAChM;gBAAE,OAAO;gBAA8C,aAAa;YAA6H;SAClM;QACD,YAAY;YACV;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,kBAAkB;QAClB,qBAAqB;QACrB,UAAU;QACV,mBAAmB;QACnB,YAAY;QACZ,UAAU;QACV,aAAa;YACX;gBAAE,OAAO;gBAAqC,aAAa;YAA+I;YAC1M;gBAAE,OAAO;gBAA+D,aAAa;YAA4H;YACjN;gBAAE,OAAO;gBAA6C,aAAa;YAA6G;YAChL;gBAAE,OAAO;gBAAmD,aAAa;YAA6H;YACtM;gBAAE,OAAO;gBAAgD,aAAa;YAAsH;SAC7L;QACD,YAAY;YACV;gBAAE,OAAO;gBAAmD,aAAa;YAA8G;YACvL;gBAAE,OAAO;gBAA+C,aAAa;YAA4G;YACjL;gBAAE,OAAO;gBAAyD,aAAa;YAAyH;YACxM;gBAAE,OAAO;gBAAsC,aAAa;YAA2H;SACxL;QACD,YAAY;YACV;YACA;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,kBAAkB;QAClB,qBAAqB;QACrB,UAAU;QACV,mBAAmB;QACnB,YAAY;QACZ,UAAU;QACV,aAAa;YACX;gBAAE,OAAO;gBAAyC,aAAa;YAA4G;YAC3K;gBAAE,OAAO;gBAAoD,aAAa;YAAuH;YACjM;gBAAE,OAAO;gBAAqD,aAAa;YAAoL;YAC/P;gBAAE,OAAO;gBAAwC,aAAa;YAAoI;YAClM;gBAAE,OAAO;gBAAiD,aAAa;YAA0H;SAClM;QACD,YAAY;YACV;gBAAE,OAAO;gBAA6C,aAAa;YAA2G;YAC9K;gBAAE,OAAO;gBAAqC,aAAa;YAAuH;YAClL;gBAAE,OAAO;gBAA+C,aAAa;YAA0G;YAC/K;gBAAE,OAAO;gBAAsC,aAAa;YAAqG;SAClK;QACD,YAAY;YACV;YACA;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,kBAAkB;QAClB,qBAAqB;QACrB,UAAU;QACV,mBAAmB;QACnB,YAAY;QACZ,UAAU;QACV,aAAa;YACX;gBAAE,OAAO;gBAAqC,aAAa;YAAyJ;YACpN;gBAAE,OAAO;gBAAiD,aAAa;YAAoJ;YAC3N;gBAAE,OAAO;gBAAgD,aAAa;YAAuH;YAC7L;gBAAE,OAAO;gBAA4C,aAAa;YAAgI;YAClM;gBAAE,OAAO;gBAAoC,aAAa;YAAqH;SAChL;QACD,YAAY;YACV;gBAAE,OAAO;gBAAsD,aAAa;YAA6G;YACzL;gBAAE,OAAO;gBAAwC,aAAa;YAAsI;YACpM;gBAAE,OAAO;gBAAqD,aAAa;YAAyI;YACpN;gBAAE,OAAO;gBAAqC,aAAa;YAAuI;SACnM;QACD,YAAY;YACV;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,kBAAkB;QAClB,qBAAqB;QACrB,UAAU;QACV,mBAAmB;QACnB,YAAY;QACZ,UAAU;QACV,aAAa;YACX;gBAAE,OAAO;gBAA+D,aAAa;YAAoI;YACzN;gBAAE,OAAO;gBAAmD,aAAa;YAAuH;YAChM;gBAAE,OAAO;gBAAmE,aAAa;YAAkH;YAC3M;gBAAE,OAAO;gBAAqD,aAAa;YAAiH;YAC5L;gBAAE,OAAO;gBAAkE,aAAa;YAAgH;SACzM;QACD,YAAY;YACV;gBAAE,OAAO;gBAAoD,aAAa;YAA0G;YACpL;gBAAE,OAAO;gBAAiD,aAAa;YAAwH;YAC/L;gBAAE,OAAO;gBAAqC,aAAa;YAA0G;YACrK;gBAAE,OAAO;gBAAqC,aAAa;YAA6H;SACzL;QACD,YAAY;YACV;YACA;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,kBAAkB;QAClB,qBAAqB;QACrB,UAAU;QACV,mBAAmB;QACnB,YAAY;QACZ,UAAU;QACV,aAAa;YACX;gBAAE,OAAO;gBAA4C,aAAa;YAAwH;YAC1L;gBAAE,OAAO;gBAA2C,aAAa;YAAuI;YACxM;gBAAE,OAAO;gBAA6C,aAAa;YAAiH;YACpL;gBAAE,OAAO;gBAAiC,aAAa;YAA6G;YACpK;gBAAE,OAAO;gBAAwC,aAAa;YAA0H;SACzL;QACD,YAAY;YACV;gBAAE,OAAO;gBAA4C,aAAa;YAAoH;YACtL;gBAAE,OAAO;gBAAqC,aAAa;YAAyG;YACpK;gBAAE,OAAO;gBAAuC,aAAa;YAAuG;YACpK;gBAAE,OAAO;gBAA+C,aAAa;YAA6G;SACnL;QACD,YAAY;YACV;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,kBAAkB;QAClB,qBAAqB;QACrB,UAAU;QACV,mBAAmB;QACnB,YAAY;QACZ,UAAU;QACV,aAAa;YACX;gBAAE,OAAO;gBAAsC,aAAa;YAAmI;YAC/L;gBAAE,OAAO;gBAAkD,aAAa;YAA0I;YAClN;gBAAE,OAAO;gBAAyD,aAAa;YAA4I;YAC3N;gBAAE,OAAO;gBAAuD,aAAa;YAAkH;YAC/L;gBAAE,OAAO;gBAA4D,aAAa;YAA6G;SAChM;QACD,YAAY;YACV;gBAAE,OAAO;gBAA2D,aAAa;YAA6G;YAC9L;gBAAE,OAAO;gBAA+C,aAAa;YAAiH;YACtL;gBAAE,OAAO;gBAAsD,aAAa;YAAgI;YAC5M;gBAAE,OAAO;gBAAyC,aAAa;YAAgI;SAChM;QACD,YAAY;YACV;YACA;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,kBAAkB;QAClB,qBAAqB;QACrB,UAAU;QACV,mBAAmB;QACnB,YAAY;QACZ,UAAU;QACV,aAAa;YACX;gBAAE,OAAO;gBAAiD,aAAa;YAA6I;YACpN;gBAAE,OAAO;gBAAoD,aAAa;YAAyI;YACnN;gBAAE,OAAO;gBAAoE,aAAa;YAAiG;YAC3L;gBAAE,OAAO;gBAA8D,aAAa;YAA2I;YAC/N;gBAAE,OAAO;gBAA2D,aAAa;YAAyG;SAC3L;QACD,YAAY;YACV;gBAAE,OAAO;gBAAoD,aAAa;YAAyJ;YACnO;gBAAE,OAAO;gBAAiD,aAAa;YAA8H;YACrM;gBAAE,OAAO;gBAA6C,aAAa;YAAuH;YAC1L;gBAAE,OAAO;gBAAyC,aAAa;YAAsI;SACtM;QACD,YAAY;YACV;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,kBAAkB;QAClB,qBAAqB;QACrB,UAAU;QACV,mBAAmB;QACnB,YAAY;QACZ,UAAU;QACV,aAAa;YACX;gBAAE,OAAO;gBAAmD,aAAa;YAA4H;YACrM;gBAAE,OAAO;gBAAsD,aAAa;YAAqI;YACjN;gBAAE,OAAO;gBAA+D,aAAa;YAAsG;YAC3L;gBAAE,OAAO;gBAA0C,aAAa;YAA2H;YAC3L;gBAAE,OAAO;gBAAwD,aAAa;YAAwI;SACvN;QACD,YAAY;YACV;gBAAE,OAAO;gBAAqD,aAAa;YAA6G;YACxL;gBAAE,OAAO;gBAAmD,aAAa;YAA2H;YACpM;gBAAE,OAAO;gBAAiD,aAAa;YAA8H;YACrM;gBAAE,OAAO;gBAA2C,aAAa;YAAoH;SACtL;QACD,YAAY;YACV;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,kBAAkB;QAClB,qBAAqB;QACrB,UAAU;QACV,mBAAmB;QACnB,YAAY;QACZ,UAAU;QACV,aAAa;YACX;gBAAE,OAAO;gBAAgD,aAAa;YAAyG;YAC/K;gBAAE,OAAO;gBAAsD,aAAa;YAAoI;YAChN;gBAAE,OAAO;gBAAyD,aAAa;YAA+H;YAC9M;gBAAE,OAAO;gBAAgD,aAAa;YAAgH;YACtL;gBAAE,OAAO;gBAAiD,aAAa;YAA+G;SACvL;QACD,YAAY;YACV;gBAAE,OAAO;gBAA8B,aAAa;YAAkH;YACtK;gBAAE,OAAO;gBAAuC,aAAa;YAA6G;YAC1K;gBAAE,OAAO;gBAA4C,aAAa;YAA+G;YACjL;gBAAE,OAAO;gBAA2C,aAAa;YAA8H;SAChM;QACD,YAAY;YACV;YACA;YACA;YACA;SACD;IACH;CACD;AAEM,SAAS,wBAAwB,IAAY;IAClD,OAAO,gBAAgB,IAAI,CAAC,CAAA,UAAW,QAAQ,IAAI,KAAK;AAC1D;AAEO,SAAS;IACd,OAAO;AACT;AAEO,SAAS;IACd,OAAO,gBAAgB,MAAM,CAAC,CAAA,UAAW,QAAQ,QAAQ;AAC3D;AAEO,SAAS,6BAA6B,QAAgB;IAC3D,OAAO,gBAAgB,MAAM,CAAC,CAAA,UAAW,QAAQ,QAAQ,KAAK;AAChE", "debugId": null}}, {"offset": {"line": 848, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/charlie-oscar-consulting/website/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst badgeVariants = cva(\r\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\r\n        secondary:\r\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\r\n        destructive:\r\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\r\n        outline:\r\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nfunction Badge({\r\n  className,\r\n  variant,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"span\"> &\r\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\r\n  const Comp = asChild ? Slot : \"span\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"badge\"\r\n      className={cn(badgeVariants({ variant }), className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Badge, badgeVariants }\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,2OAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,oSAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6WAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 894, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/charlie-oscar-consulting/website/components/foncier/service-hero.tsx"], "sourcesContent": ["import Link from \"next/link\";\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport { FoncierService } from \"@/lib/foncier-services-data\";\r\nimport { ArrowRight, ChevronRight, Home, Star } from \"lucide-react\";\r\n\r\ninterface ServiceHeroProps {\r\n  service: FoncierService;\r\n}\r\n\r\nexport function ServiceHero({ service }: ServiceHeroProps) {\r\n  return (\r\n    <section className=\"relative bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-white py-20 overflow-hidden w-full min-h-[60vh] flex items-center\" style={{backgroundImage: `url('/your-background-image.jpg')`, backgroundSize: 'cover', backgroundPosition: 'center'}}>\r\n      <div className=\"absolute inset-0 opacity-60 bg-black\" />\r\n      <div className=\"container mx-auto px-4 relative z-10\">\r\n        <div className=\"max-w-6xl mx-auto\">\r\n          {/* Breadcrumb */}\r\n          <div className=\"mb-8\">\r\n            <nav className=\"flex items-center space-x-2 text-sm text-gray-300\">\r\n              <Link href=\"/\" className=\"hover:text-white transition-colors flex items-center\">\r\n                <Home className=\"w-4 h-4 mr-1\" />\r\n                Accueil\r\n              </Link>\r\n              <ChevronRight className=\"w-4 h-4\" />\r\n              <Link href=\"/foncier\" className=\"hover:text-white transition-colors\">\r\n                Foncier\r\n              </Link>\r\n              <ChevronRight className=\"w-4 h-4\" />\r\n              <Link href=\"/foncier/services\" className=\"hover:text-white transition-colors\">\r\n                Services\r\n              </Link>\r\n              <ChevronRight className=\"w-4 h-4\" />\r\n              <span className=\"text-primary\">{service.title}</span>\r\n            </nav>\r\n          </div>\r\n\r\n          <div className=\"space-y-8\">\r\n            {/* Service Category */}\r\n            <div className=\"flex items-center space-x-3\">\r\n              <Badge variant=\"secondary\" className=\"bg-primary/20 text-primary-foreground border-primary/30\">\r\n                {service.category}\r\n              </Badge>\r\n              {service.featured && (\r\n                <Badge variant=\"secondary\" className=\"bg-yellow-500/20 text-yellow-300 border-yellow-500/30 flex items-center\">\r\n                  <Star className=\"w-3 h-3 mr-1 fill-current\" />\r\n                  Service phare\r\n                </Badge>\r\n              )}\r\n            </div>\r\n\r\n            {/* Title */}\r\n            <div className=\"space-y-4\">\r\n              <h1 className=\"text-4xl md:text-5xl lg:text-6xl font-bold leading-tight\">\r\n                {service.title}\r\n              </h1>\r\n              <p className=\"text-xl md:text-2xl text-gray-300 leading-relaxed\">\r\n                {service.shortDescription}\r\n              </p>\r\n            </div>\r\n\r\n            {/* CTA Buttons */}\r\n            <div className=\"flex flex-col sm:flex-row gap-4\">\r\n              <Button asChild size=\"lg\" className=\"bg-primary hover:bg-primary/90 text-white group\">\r\n                <Link href={`/formulaire?service=${service.slug}`} className=\"flex items-center\">\r\n                  Demander un devis\r\n                  <ArrowRight className=\"ml-2 h-5 w-5\" />\r\n                </Link>\r\n              </Button>\r\n              <Button asChild size=\"lg\" variant=\"outline\" className=\"border-white text-primary hover:bg-white hover:text-gray-900 px-8 py-4\">\r\n                <Link href=\"/contact\">\r\n                  Contactez-nous\r\n                </Link>\r\n              </Button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;;;;;;AAMO,SAAS,YAAY,EAAE,OAAO,EAAoB;IACvD,qBACE,6WAAC;QAAQ,WAAU;QAA2I,OAAO;YAAC,iBAAiB,CAAC,iCAAiC,CAAC;YAAE,gBAAgB;YAAS,oBAAoB;QAAQ;;0BAC/Q,6WAAC;gBAAI,WAAU;;;;;;0BACf,6WAAC;gBAAI,WAAU;0BACb,cAAA,6WAAC;oBAAI,WAAU;;sCAEb,6WAAC;4BAAI,WAAU;sCACb,cAAA,6WAAC;gCAAI,WAAU;;kDACb,6WAAC,2RAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;;0DACvB,6WAAC,uRAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGnC,6WAAC,0SAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;kDACxB,6WAAC,2RAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAAqC;;;;;;kDAGrE,6WAAC,0SAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;kDACxB,6WAAC,2RAAA,CAAA,UAAI;wCAAC,MAAK;wCAAoB,WAAU;kDAAqC;;;;;;kDAG9E,6WAAC,0SAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;kDACxB,6WAAC;wCAAK,WAAU;kDAAgB,QAAQ,KAAK;;;;;;;;;;;;;;;;;sCAIjD,6WAAC;4BAAI,WAAU;;8CAEb,6WAAC;oCAAI,WAAU;;sDACb,6WAAC,0HAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAY,WAAU;sDAClC,QAAQ,QAAQ;;;;;;wCAElB,QAAQ,QAAQ,kBACf,6WAAC,0HAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAY,WAAU;;8DACnC,6WAAC,sRAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAA8B;;;;;;;;;;;;;8CAOpD,6WAAC;oCAAI,WAAU;;sDACb,6WAAC;4CAAG,WAAU;sDACX,QAAQ,KAAK;;;;;;sDAEhB,6WAAC;4CAAE,WAAU;sDACV,QAAQ,gBAAgB;;;;;;;;;;;;8CAK7B,6WAAC;oCAAI,WAAU;;sDACb,6WAAC,2HAAA,CAAA,SAAM;4CAAC,OAAO;4CAAC,MAAK;4CAAK,WAAU;sDAClC,cAAA,6WAAC,2RAAA,CAAA,UAAI;gDAAC,MAAM,CAAC,oBAAoB,EAAE,QAAQ,IAAI,EAAE;gDAAE,WAAU;;oDAAoB;kEAE/E,6WAAC,sSAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;;;;;;;sDAG1B,6WAAC,2HAAA,CAAA,SAAM;4CAAC,OAAO;4CAAC,MAAK;4CAAK,SAAQ;4CAAU,WAAU;sDACpD,cAAA,6WAAC,2RAAA,CAAA,UAAI;gDAAC,MAAK;0DAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUtC", "debugId": null}}, {"offset": {"line": 1160, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/charlie-oscar-consulting/website/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card\"\r\n      className={cn(\r\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-header\"\r\n      className={cn(\r\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-title\"\r\n      className={cn(\"leading-none font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-action\"\r\n      className={cn(\r\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-content\"\r\n      className={cn(\"px-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-footer\"\r\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Card,\r\n  CardHeader,\r\n  CardFooter,\r\n  CardTitle,\r\n  CardAction,\r\n  CardDescription,\r\n  CardContent,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6WAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6WAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6WAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6WAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6WAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6WAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6WAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1257, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/charlie-oscar-consulting/website/components/foncier/service-description.tsx"], "sourcesContent": ["import { Card, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from \"@/components/ui/card\";\r\nimport { FoncierService } from \"@/lib/foncier-services-data\";\r\nimport { FileText, CheckCircle, AlertCircle, Info, ListChecks } from \"lucide-react\";\r\n\r\ninterface ServiceDescriptionProps {\r\n  service: FoncierService;\r\n}\r\n\r\n// Example prestations incluses (replace with dynamic data if available)\r\nconst defaultPrestations = [\r\n  { \"title\": \"Analyse personnalisée de votre dossier\", \"description\": \"Nous étudions en détail votre situation pour adapter la démarche à vos besoins.\" },\r\n  { \"title\": \"Accompagnement administratif complet\", \"description\": \"Nous gérons toutes les formalités et relations avec les administrations.\" },\r\n  { \"title\": \"Rédaction et vérification des documents\", \"description\": \"Nous rédigeons et contrôlons tous les documents nécessaires à votre dossier.\" },\r\n  { \"title\": \"Suivi des démarches jusqu'à l'obtention du résultat\", \"description\": \"Nous assurons un suivi régulier et vous informons à chaque étape.\" },\r\n  { \"title\": \"Conseils juridiques et techniques adaptés\", \"description\": \"Vous bénéficiez de recommandations personnalisées pour sécuriser votre projet.\" }\r\n];\r\n\r\nexport function ServiceDescription({ service }: ServiceDescriptionProps) {\r\n  return (\r\n    <section className=\"py-20 bg-white\">\r\n      <div className=\"container mx-auto px-4\">\r\n        <div className=\"max-w-6xl mx-auto\">\r\n          <div className=\"grid lg:grid-cols-7 gap-12\">\r\n            {/* Main Content: spans 5/7 columns on large screens */}\r\n            <div className=\"lg:col-span-5 space-y-10\">\r\n              {/* Section Header */}\r\n              <div className=\"space-y-4\">\r\n                <div className=\"inline-flex items-center px-4 py-2 bg-primary/10 text-primary rounded-full text-sm font-medium\">\r\n                  <FileText className=\"w-4 h-4 mr-2\" />\r\n                  Description détaillée\r\n                </div>\r\n                <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900\">\r\n                  En quoi consiste ce service ?\r\n                </h2>\r\n              </div>\r\n              {/* Detailed Description */}\r\n              <div className=\"prose prose-lg max-w-none mb-8\">\r\n                <div className=\"text-gray-700 leading-relaxed space-y-6\">\r\n                  {service.detailedDescription.split('\\n').map((paragraph, index) => (\r\n                    <p key={index} className=\"text-lg leading-relaxed\">\r\n                      {paragraph}\r\n                    </p>\r\n                  ))}\r\n                </div>\r\n              </div>\r\n\r\n              {/* Avantages clés de ce service */}\r\n              <Card className=\"border-l-4 border-l-primary bg-primary/5 shadow-sm mb-8\">\r\n                <CardHeader>\r\n                  <CardTitle className=\"flex items-center text-xl text-gray-900\">\r\n                    <CheckCircle className=\"w-6 h-6 text-primary mr-3\" />\r\n                    Avantages clés de ce service\r\n                  </CardTitle>\r\n                </CardHeader>\r\n                <CardContent>\r\n                  <div className=\"flex flex-col gap-6\">\r\n                    <div className=\"space-y-4\">\r\n                      {\r\n                        service.advantages?.map((advantage, index) => (\r\n                          <div key={index} className=\"flex items-start space-x-3\">\r\n                            <CheckCircle className=\"w-5 h-5 text-green-600 flex-shrink-0 mt-0.5\" />\r\n                            <div>\r\n                              <h4 className=\"font-semibold text-gray-900\">{advantage.title}</h4>\r\n                              <p className=\"text-sm text-gray-600\">{advantage.description}</p>\r\n                            </div>\r\n                          </div>\r\n                        )) \r\n                      }\r\n                    </div>\r\n                  </div>\r\n                </CardContent>\r\n              </Card>\r\n\r\n              {/* Prestations incluses */}\r\n              <div className=\"mb-8\">\r\n                <div className=\"flex items-center mb-6\">\r\n                  <ListChecks className=\"w-6 h-6 text-accent mr-3\" />\r\n                  <span className=\"text-xl font-bold text-gray-900\">Prestations incluses</span>\r\n                </div>\r\n                <div className=\"grid md:grid-cols-2 gap-8\">\r\n                  {service.prestations?.map((item, idx) => {\r\n                    const isObject = typeof item === 'object' && item !== null;\r\n                    return (\r\n                      <div key={idx} className=\"relative flex bg-white rounded-xl shadow-sm border border-gray-100 p-8 min-h-[120px] items-center\">\r\n                        {/* Number on the left, rotated */}\r\n                        <div className=\"absolute left-0 top-6 flex flex-col items-center\" style={{width: '40px'}}>\r\n                          <span className=\"text-2xl font-bold text-accent opacity-80 transform -rotate-90 select-none\">{String(idx + 1).padStart(2, '0')}</span>\r\n                          <span className=\"block w-px h-12 bg-accent mt-2\" />\r\n                        </div>\r\n                        {/* Content to the right of the number */}\r\n                        <div className=\"pl-16\">\r\n                          <div className=\"font-semibold text-gray-900 text-lg mb-1\">\r\n                            {isObject ? item.title : item}\r\n                          </div>\r\n                          {isObject && item.description && (\r\n                            <div className=\"text-gray-600 text-base leading-relaxed mt-1\">{item.description}</div>\r\n                          )}\r\n                        </div>\r\n                      </div>\r\n                    );\r\n                  })}\r\n                </div>\r\n              </div>\r\n\r\n              {/* Quand faire appel à ce service ? */}\r\n              <Card className=\"border-l-4 border-l-accent bg-accent/5 shadow-sm mb-8\">\r\n                <CardHeader>\r\n                  <CardTitle className=\"flex items-center text-xl text-gray-900\">\r\n                    <Info className=\"w-6 h-6 text-accent mr-3\" />\r\n                    Quand faire appel à ce service ?\r\n                  </CardTitle>\r\n                </CardHeader>\r\n                <CardContent>\r\n                  <div className=\"space-y-4\">\r\n                    {service.whenToCall?.map((useCase, index) => (\r\n                      <div key={index} className=\"flex items-start space-x-3\">\r\n                        <div className=\"w-6 h-6 bg-accent/20 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5\">\r\n                          <span className=\"text-accent font-semibold text-sm\">{index + 1}</span>\r\n                        </div>\r\n                        <p className=\"text-gray-700\">{useCase}</p>\r\n                      </div>\r\n                    ))}\r\n                  </div>\r\n                </CardContent>\r\n              </Card>\r\n            </div>\r\n            {/* Sidebar: spans 2/7 columns on large screens */}\r\n            <div className=\"lg:col-span-2 space-y-6\">\r\n              {/* Service Summary */}\r\n              <Card className=\"sticky top-8 bg-gray-50 border border-gray-200\">\r\n                <CardHeader>\r\n                  <CardTitle className=\"text-lg text-gray-900\">\r\n                    Résumé du service\r\n                  </CardTitle>\r\n                </CardHeader>\r\n                <CardContent className=\"space-y-4\">\r\n                  <div className=\"space-y-3\">\r\n                    <div className=\"flex justify-between items-center py-2 border-b border-gray-200\">\r\n                      <span className=\"text-gray-600 text-sm\">Service</span>\r\n                      <span className=\"text-gray-900 font-medium text-sm\">{service.title}</span>\r\n                    </div>\r\n                    <div className=\"flex justify-between items-center py-2 border-b border-gray-200\">\r\n                      <span className=\"text-gray-600 text-sm\">Catégorie</span>\r\n                      <span className=\"text-gray-900 font-medium text-sm\">{service.category}</span>\r\n                    </div>\r\n                    {/* <div className=\"flex justify-between items-center py-2 border-b border-gray-200\">\r\n                      <span className=\"text-gray-600 text-sm\">Durée</span>\r\n                      <span className=\"text-gray-900 font-medium text-sm\">{service.estimatedDuration}</span>\r\n                    </div>\r\n                    <div className=\"flex justify-between items-center py-2\">\r\n                      <span className=\"text-gray-600 text-sm\">Complexité</span>\r\n                      <span className={`text-sm font-medium ${\r\n                        service.complexity === 'Simple' ? 'text-green-600' :\r\n                        service.complexity === 'Modéré' ? 'text-yellow-600' :\r\n                        'text-red-600'\r\n                      }`}>\r\n                        {service.complexity}\r\n                      </span>\r\n                    </div> */}\r\n                  </div>\r\n                </CardContent>\r\n              </Card>\r\n              {/* Important Notice */}\r\n              <Card className=\"border-l-4 border-l-orange-500 bg-orange-50 border border-orange-100\">\r\n                <CardHeader>\r\n                  <CardTitle className=\"flex items-center text-lg text-gray-900\">\r\n                    <AlertCircle className=\"w-5 h-5 text-orange-500 mr-2\" />\r\n                    Important\r\n                  </CardTitle>\r\n                </CardHeader>\r\n                <CardContent>\r\n                  <p className=\"text-sm text-gray-700 leading-relaxed\">\r\n                    Chaque dossier est unique. Les délais et procédures peuvent varier \r\n                    selon la complexité de votre situation. Nous vous recommandons une \r\n                    consultation préalable pour une évaluation personnalisée.\r\n                  </p>\r\n                </CardContent>\r\n              </Card>\r\n              {/* Related Services */}\r\n              <Card className=\"bg-gray-50 border border-gray-200\">\r\n                <CardHeader>\r\n                  <CardTitle className=\"text-lg text-gray-900\">\r\n                    Services complémentaires\r\n                  </CardTitle>\r\n                </CardHeader>\r\n                <CardContent>\r\n                  <div className=\"space-y-3\">\r\n                    {getRelatedServices(service.slug).map((relatedService, index) => (\r\n                      <div key={index} className=\"text-sm\">\r\n                        <a \r\n                          href={`/foncier/services/${relatedService.slug}`}\r\n                          className=\"text-primary hover:text-primary/80 font-medium transition-colors\"\r\n                        >\r\n                          {relatedService.title}\r\n                        </a>\r\n                        <p className=\"text-gray-600 text-xs mt-1\">{relatedService.shortDescription}</p>\r\n                      </div>\r\n                    ))}\r\n                  </div>\r\n                </CardContent>\r\n              </Card>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\n\r\n// Helper functions\r\n\r\nfunction getRelatedServices(currentSlug: string): Array<{slug: string, title: string, shortDescription: string}> {\r\n  const allServices = [\r\n  { \"slug\": \"immatriculation-directe\", \"title\": \"Immatriculation Directe\", \"shortDescription\": \"Sécurisation juridique d’un terrain occupé\" },\r\n  { \"slug\": \"achat-terrain-non-titre\", \"title\": \"Achat de Terrain Non Titré\", \"shortDescription\": \"Acquérez un terrain non immatriculé en toute sécurité.\" },\r\n  { \"slug\": \"concession-domanial\", \"title\": \"Concession Domaniale\", \"shortDescription\": \"Nous vous guidons pour obtenir une concession temporaire ou définitive.\" },\r\n  { \"slug\": \"mutation-par-deces\", \"title\": \"Mutation par Décès\", \"shortDescription\": \"Transférez à votre nom votre terrain hérité grâce à un accompagnement complet du dossier.\" }\r\n];\r\n  \r\n  return allServices.filter(service => service.slug !== currentSlug).slice(0, 3);\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;AAAA;AAAA;AAAA;AAAA;;;;AAMA,wEAAwE;AACxE,MAAM,qBAAqB;IACzB;QAAE,SAAS;QAA0C,eAAe;IAAkF;IACtJ;QAAE,SAAS;QAAwC,eAAe;IAA2E;IAC7I;QAAE,SAAS;QAA2C,eAAe;IAA+E;IACpJ;QAAE,SAAS;QAAuD,eAAe;IAAoE;IACrJ;QAAE,SAAS;QAA6C,eAAe;IAAiF;CACzJ;AAEM,SAAS,mBAAmB,EAAE,OAAO,EAA2B;IACrE,qBACE,6WAAC;QAAQ,WAAU;kBACjB,cAAA,6WAAC;YAAI,WAAU;sBACb,cAAA,6WAAC;gBAAI,WAAU;0BACb,cAAA,6WAAC;oBAAI,WAAU;;sCAEb,6WAAC;4BAAI,WAAU;;8CAEb,6WAAC;oCAAI,WAAU;;sDACb,6WAAC;4CAAI,WAAU;;8DACb,6WAAC,kSAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGvC,6WAAC;4CAAG,WAAU;sDAA+C;;;;;;;;;;;;8CAK/D,6WAAC;oCAAI,WAAU;8CACb,cAAA,6WAAC;wCAAI,WAAU;kDACZ,QAAQ,mBAAmB,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,WAAW,sBACvD,6WAAC;gDAAc,WAAU;0DACtB;+CADK;;;;;;;;;;;;;;;8CAQd,6WAAC,yHAAA,CAAA,OAAI;oCAAC,WAAU;;sDACd,6WAAC,yHAAA,CAAA,aAAU;sDACT,cAAA,6WAAC,yHAAA,CAAA,YAAS;gDAAC,WAAU;;kEACnB,6WAAC,+SAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;oDAA8B;;;;;;;;;;;;sDAIzD,6WAAC,yHAAA,CAAA,cAAW;sDACV,cAAA,6WAAC;gDAAI,WAAU;0DACb,cAAA,6WAAC;oDAAI,WAAU;8DAEX,QAAQ,UAAU,EAAE,IAAI,CAAC,WAAW,sBAClC,6WAAC;4DAAgB,WAAU;;8EACzB,6WAAC,+SAAA,CAAA,cAAW;oEAAC,WAAU;;;;;;8EACvB,6WAAC;;sFACC,6WAAC;4EAAG,WAAU;sFAA+B,UAAU,KAAK;;;;;;sFAC5D,6WAAC;4EAAE,WAAU;sFAAyB,UAAU,WAAW;;;;;;;;;;;;;2DAJrD;;;;;;;;;;;;;;;;;;;;;;;;;;8CAetB,6WAAC;oCAAI,WAAU;;sDACb,6WAAC;4CAAI,WAAU;;8DACb,6WAAC,sSAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;8DACtB,6WAAC;oDAAK,WAAU;8DAAkC;;;;;;;;;;;;sDAEpD,6WAAC;4CAAI,WAAU;sDACZ,QAAQ,WAAW,EAAE,IAAI,CAAC,MAAM;gDAC/B,MAAM,WAAW,OAAO,SAAS,YAAY,SAAS;gDACtD,qBACE,6WAAC;oDAAc,WAAU;;sEAEvB,6WAAC;4DAAI,WAAU;4DAAmD,OAAO;gEAAC,OAAO;4DAAM;;8EACrF,6WAAC;oEAAK,WAAU;8EAA8E,OAAO,MAAM,GAAG,QAAQ,CAAC,GAAG;;;;;;8EAC1H,6WAAC;oEAAK,WAAU;;;;;;;;;;;;sEAGlB,6WAAC;4DAAI,WAAU;;8EACb,6WAAC;oEAAI,WAAU;8EACZ,WAAW,KAAK,KAAK,GAAG;;;;;;gEAE1B,YAAY,KAAK,WAAW,kBAC3B,6WAAC;oEAAI,WAAU;8EAAgD,KAAK,WAAW;;;;;;;;;;;;;mDAZ3E;;;;;4CAiBd;;;;;;;;;;;;8CAKJ,6WAAC,yHAAA,CAAA,OAAI;oCAAC,WAAU;;sDACd,6WAAC,yHAAA,CAAA,aAAU;sDACT,cAAA,6WAAC,yHAAA,CAAA,YAAS;gDAAC,WAAU;;kEACnB,6WAAC,sRAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAA6B;;;;;;;;;;;;sDAIjD,6WAAC,yHAAA,CAAA,cAAW;sDACV,cAAA,6WAAC;gDAAI,WAAU;0DACZ,QAAQ,UAAU,EAAE,IAAI,CAAC,SAAS,sBACjC,6WAAC;wDAAgB,WAAU;;0EACzB,6WAAC;gEAAI,WAAU;0EACb,cAAA,6WAAC;oEAAK,WAAU;8EAAqC,QAAQ;;;;;;;;;;;0EAE/D,6WAAC;gEAAE,WAAU;0EAAiB;;;;;;;uDAJtB;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAYpB,6WAAC;4BAAI,WAAU;;8CAEb,6WAAC,yHAAA,CAAA,OAAI;oCAAC,WAAU;;sDACd,6WAAC,yHAAA,CAAA,aAAU;sDACT,cAAA,6WAAC,yHAAA,CAAA,YAAS;gDAAC,WAAU;0DAAwB;;;;;;;;;;;sDAI/C,6WAAC,yHAAA,CAAA,cAAW;4CAAC,WAAU;sDACrB,cAAA,6WAAC;gDAAI,WAAU;;kEACb,6WAAC;wDAAI,WAAU;;0EACb,6WAAC;gEAAK,WAAU;0EAAwB;;;;;;0EACxC,6WAAC;gEAAK,WAAU;0EAAqC,QAAQ,KAAK;;;;;;;;;;;;kEAEpE,6WAAC;wDAAI,WAAU;;0EACb,6WAAC;gEAAK,WAAU;0EAAwB;;;;;;0EACxC,6WAAC;gEAAK,WAAU;0EAAqC,QAAQ,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAoB7E,6WAAC,yHAAA,CAAA,OAAI;oCAAC,WAAU;;sDACd,6WAAC,yHAAA,CAAA,aAAU;sDACT,cAAA,6WAAC,yHAAA,CAAA,YAAS;gDAAC,WAAU;;kEACnB,6WAAC,wSAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;oDAAiC;;;;;;;;;;;;sDAI5D,6WAAC,yHAAA,CAAA,cAAW;sDACV,cAAA,6WAAC;gDAAE,WAAU;0DAAwC;;;;;;;;;;;;;;;;;8CAQzD,6WAAC,yHAAA,CAAA,OAAI;oCAAC,WAAU;;sDACd,6WAAC,yHAAA,CAAA,aAAU;sDACT,cAAA,6WAAC,yHAAA,CAAA,YAAS;gDAAC,WAAU;0DAAwB;;;;;;;;;;;sDAI/C,6WAAC,yHAAA,CAAA,cAAW;sDACV,cAAA,6WAAC;gDAAI,WAAU;0DACZ,mBAAmB,QAAQ,IAAI,EAAE,GAAG,CAAC,CAAC,gBAAgB,sBACrD,6WAAC;wDAAgB,WAAU;;0EACzB,6WAAC;gEACC,MAAM,CAAC,kBAAkB,EAAE,eAAe,IAAI,EAAE;gEAChD,WAAU;0EAET,eAAe,KAAK;;;;;;0EAEvB,6WAAC;gEAAE,WAAU;0EAA8B,eAAe,gBAAgB;;;;;;;uDAPlE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmBhC;AAEA,mBAAmB;AAEnB,SAAS,mBAAmB,WAAmB;IAC7C,MAAM,cAAc;QACpB;YAAE,QAAQ;YAA2B,SAAS;YAA2B,oBAAoB;QAA6C;QAC1I;YAAE,QAAQ;YAA2B,SAAS;YAA8B,oBAAoB;QAAyD;QACzJ;YAAE,QAAQ;YAAuB,SAAS;YAAwB,oBAAoB;QAA0E;QAChK;YAAE,QAAQ;YAAsB,SAAS;YAAsB,oBAAoB;QAA4F;KAChL;IAEC,OAAO,YAAY,MAAM,CAAC,CAAA,UAAW,QAAQ,IAAI,KAAK,aAAa,KAAK,CAAC,GAAG;AAC9E", "debugId": null}}, {"offset": {"line": 1901, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/charlie-oscar-consulting/website/components/foncier/process-section.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ProcessSection = registerClientReference(\n    function() { throw new Error(\"Attempted to call ProcessSection() from the server but ProcessSection is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/foncier/process-section.tsx <module evaluation>\",\n    \"ProcessSection\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,iBAAiB,CAAA,GAAA,oXAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,wEACA", "debugId": null}}, {"offset": {"line": 1915, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/charlie-oscar-consulting/website/components/foncier/process-section.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ProcessSection = registerClientReference(\n    function() { throw new Error(\"Attempted to call ProcessSection() from the server but ProcessSection is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/foncier/process-section.tsx\",\n    \"ProcessSection\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,iBAAiB,CAAA,GAAA,oXAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,oDACA", "debugId": null}}, {"offset": {"line": 1929, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1939, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/charlie-oscar-consulting/website/app/foncier/services/%5Bslug%5D/page.tsx"], "sourcesContent": ["import { Metadata } from \"next\";\r\nimport { notFound } from \"next/navigation\";\r\nimport { getFoncierServiceBySlug, getAllFoncierServices } from \"@/lib/foncier-services-data\";\r\nimport { ServiceHero } from \"@/components/foncier/service-hero\";\r\nimport { ServiceDescription } from \"@/components/foncier/service-description\";\r\nimport { ProcessSection } from \"@/components/foncier/process-section\";\r\nimport { FaqSection } from \"@/components/foncier/faq-section\";\r\nimport { FinalCtaSection } from \"@/components/foncier/final-cta-section\";\r\n\r\ninterface ServicePageProps {\r\n  params: Promise<{\r\n    slug: string;\r\n  }>;\r\n}\r\n\r\nexport async function generateStaticParams() {\r\n  const services = getAllFoncierServices();\r\n  \r\n  return services.map((service) => ({\r\n    slug: service.slug,\r\n  }));\r\n}\r\n\r\nexport async function generateMetadata({ params }: ServicePageProps): Promise<Metadata> {\r\n  const { slug } = await params;\r\n  const service = getFoncierServiceBySlug(slug);\r\n\r\n  if (!service) {\r\n    return {\r\n      title: \"Service non trouvé | Charlie Oscar Consulting\",\r\n      description: \"Le service demandé n'existe pas.\"\r\n    };\r\n  }\r\n\r\n  return {\r\n    title: `${service.title} - Service Foncier | Charlie Oscar Consulting`,\r\n    description: service.shortDescription,\r\n    keywords: [\r\n      service.title,\r\n      service.category,\r\n      \"foncier\",\r\n      \"Cameroun\",\r\n      \"Charlie Oscar\",\r\n      \"expertise foncière\",\r\n      \"conseil juridique\",\r\n      \"Yaoundé\",\r\n      \"Douala\"\r\n    ],\r\n    openGraph: {\r\n      title: `${service.title} - Service Foncier | Charlie Oscar Consulting`,\r\n      description: service.shortDescription,\r\n      type: \"website\",\r\n      locale: \"fr_FR\",\r\n      siteName: \"Charlie Oscar Consulting\"\r\n    },\r\n    twitter: {\r\n      card: \"summary_large_image\",\r\n      title: `${service.title} - Service Foncier | Charlie Oscar Consulting`,\r\n      description: service.shortDescription\r\n    },\r\n    alternates: {\r\n      canonical: `/foncier/services/${service.slug}`\r\n    }\r\n  };\r\n}\r\n\r\nexport default async function ServicePage({ params }: ServicePageProps) {\r\n  const { slug } = await params;\r\n  const service = getFoncierServiceBySlug(slug);\r\n\r\n  if (!service) {\r\n    notFound();\r\n  }\r\n\r\n  // Extract custom FAQs for this service, if any\r\n  const customFaqs = service.faq || [];\r\nconsole.log(\"Custom FAQs for service:\", customFaqs);\r\n  return (\r\n    <div className=\"min-h-screen\">\r\n      {/* Hero Section */}\r\n      <ServiceHero service={service} />\r\n      {/* Description Section */}\r\n      <ServiceDescription service={service} />\r\n      {/* Shared Foncier Process Section */}\r\n      <ProcessSection />\r\n      {/* Shared Foncier FAQ Section with custom FAQs */}\r\n      \r\n      {/* <FaqSection customFaqs={customFaqs} /> to be implemented*/} \r\n      \r\n      {/* Shared Foncier Final CTA Section */}\r\n      {/* <FinalCtaSection /> */}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;;AACA;AAAA;AACA;AACA;AACA;AACA;;;;;;;AAUO,eAAe;IACpB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,wBAAqB,AAAD;IAErC,OAAO,SAAS,GAAG,CAAC,CAAC,UAAY,CAAC;YAChC,MAAM,QAAQ,IAAI;QACpB,CAAC;AACH;AAEO,eAAe,iBAAiB,EAAE,MAAM,EAAoB;IACjE,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM;IACvB,MAAM,UAAU,CAAA,GAAA,kIAAA,CAAA,0BAAuB,AAAD,EAAE;IAExC,IAAI,CAAC,SAAS;QACZ,OAAO;YACL,OAAO;YACP,aAAa;QACf;IACF;IAEA,OAAO;QACL,OAAO,GAAG,QAAQ,KAAK,CAAC,6CAA6C,CAAC;QACtE,aAAa,QAAQ,gBAAgB;QACrC,UAAU;YACR,QAAQ,KAAK;YACb,QAAQ,QAAQ;YAChB;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,WAAW;YACT,OAAO,GAAG,QAAQ,KAAK,CAAC,6CAA6C,CAAC;YACtE,aAAa,QAAQ,gBAAgB;YACrC,MAAM;YACN,QAAQ;YACR,UAAU;QACZ;QACA,SAAS;YACP,MAAM;YACN,OAAO,GAAG,QAAQ,KAAK,CAAC,6CAA6C,CAAC;YACtE,aAAa,QAAQ,gBAAgB;QACvC;QACA,YAAY;YACV,WAAW,CAAC,kBAAkB,EAAE,QAAQ,IAAI,EAAE;QAChD;IACF;AACF;AAEe,eAAe,YAAY,EAAE,MAAM,EAAoB;IACpE,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM;IACvB,MAAM,UAAU,CAAA,GAAA,kIAAA,CAAA,0BAAuB,AAAD,EAAE;IAExC,IAAI,CAAC,SAAS;QACZ,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD;IACT;IAEA,+CAA+C;IAC/C,MAAM,aAAa,QAAQ,GAAG,IAAI,EAAE;IACtC,QAAQ,GAAG,CAAC,4BAA4B;IACtC,qBACE,6WAAC;QAAI,WAAU;;0BAEb,6WAAC,yIAAA,CAAA,cAAW;gBAAC,SAAS;;;;;;0BAEtB,6WAAC,gJAAA,CAAA,qBAAkB;gBAAC,SAAS;;;;;;0BAE7B,6WAAC,4IAAA,CAAA,iBAAc;;;;;;;;;;;AASrB", "debugId": null}}]}