"use client";

import { useEffect, useState } from "react";
import { getConstructionServicesContent } from "@/app/cms/utils/construction-services";
import HeroSection from "./components/hero-section";
import ServicesAccordion from "./components/services-accordion";
import WhyChooseSection from "./components/why-choose-section";
import FinalCtaSection from "./components/final-cta-section";

export default function ConstructionServicesClient() {
  const constructionServicesData = getConstructionServicesContent();
  
  // État pour contrôler quel accordéon est ouvert
  const [openAccordion, setOpenAccordion] = useState<string>("etudes-preliminaires");

  // IDs valides des accordéons
  const validAccordionIds = constructionServicesData.servicesSection.categories.map(cat => cat.id);

  // Fonction pour faire défiler vers un élément avec offset pour le header
  const scrollToElement = (elementId: string) => {
    const element = document.getElementById(elementId);
    if (element) {
      const headerOffset = 100; // Offset pour le header fixe
      const elementPosition = element.offsetTop - headerOffset;
      
      window.scrollTo({
        top: elementPosition,
        behavior: 'smooth'
      });
    }
  };

  // Gérer les ancres URL au chargement initial seulement
  useEffect(() => {
    const hash = window.location.hash.replace('#', '');

    if (hash && validAccordionIds.includes(hash)) {
      setOpenAccordion(hash);
      // Délai pour permettre à l'accordéon de s'ouvrir avant le scroll
      setTimeout(() => scrollToElement(hash), 100);
    }
    // Note: On ne remet plus le premier accordéon par défaut si pas d'ancre
    // pour permettre à l'utilisateur de fermer tous les accordéons
  }, []); // Seulement au montage du composant

  // Gérer les changements d'ancre URL (navigation)
  useEffect(() => {
    const handleHashChange = () => {
      const hash = window.location.hash.replace('#', '');

      if (hash && validAccordionIds.includes(hash)) {
        setOpenAccordion(hash);
        // Délai pour permettre à l'accordéon de s'ouvrir avant le scroll
        setTimeout(() => scrollToElement(hash), 100);
      }
      // Ne pas fermer l'accordéon si l'ancre est supprimée
    };

    // Écouter les changements d'ancre
    window.addEventListener('hashchange', handleHashChange);

    return () => {
      window.removeEventListener('hashchange', handleHashChange);
    };
  }, [validAccordionIds]);

  // Fonction pour gérer les changements d'accordéon
  const handleAccordionChange = (value: string) => {
    setOpenAccordion(value);

    // Mettre à jour l'URL si un accordéon est ouvert
    if (value) {
      // Mettre à jour l'URL sans déclencher un rechargement
      const newUrl = `${window.location.pathname}#${value}`;
      window.history.replaceState(null, '', newUrl);
    } else {
      // Supprimer l'ancre si aucun accordéon n'est ouvert
      const newUrl = window.location.pathname;
      window.history.replaceState(null, '', newUrl);
    }
  };

  return (
    <div className="min-h-screen">
      <HeroSection heroData={constructionServicesData.hero} />
      
      <ServicesAccordion
        servicesData={constructionServicesData.servicesSection}
        openAccordion={openAccordion}
        onValueChange={handleAccordionChange}
      />
      
      <WhyChooseSection whyChooseData={constructionServicesData.whyChoose} />
      
      <FinalCtaSection ctaData={constructionServicesData.finalCta} />
    </div>
  );
}
