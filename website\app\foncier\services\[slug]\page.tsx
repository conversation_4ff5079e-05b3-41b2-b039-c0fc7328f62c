import { Metadata } from "next";
import { notFound } from "next/navigation";
import { getFoncierServiceBySlug, getAllFoncierServices } from "@/lib/foncier-services-data";
import { ServiceHero } from "@/components/foncier/service-hero";
import { ServiceDescription } from "@/components/foncier/service-description";
import { ProcessSection } from "@/components/foncier/process-section";
import { FaqSection } from "@/components/foncier/faq-section";
import { FinalCtaSection } from "@/components/foncier/final-cta-section";

interface ServicePageProps {
  params: Promise<{
    slug: string;
  }>;
}

export async function generateStaticParams() {
  const services = getAllFoncierServices();
  
  return services.map((service) => ({
    slug: service.slug,
  }));
}

export async function generateMetadata({ params }: ServicePageProps): Promise<Metadata> {
  const { slug } = await params;
  const service = getFoncierServiceBySlug(slug);

  if (!service) {
    return {
      title: "Service non trouvé | Charlie Oscar Consulting",
      description: "Le service demandé n'existe pas."
    };
  }

  return {
    title: `${service.title} - Service Foncier | Charlie Oscar Consulting`,
    description: service.shortDescription,
    keywords: [
      service.title,
      service.category,
      "foncier",
      "Cameroun",
      "Charlie Oscar",
      "expertise foncière",
      "conseil juridique",
      "Yaoundé",
      "Douala"
    ],
    openGraph: {
      title: `${service.title} - Service Foncier | Charlie Oscar Consulting`,
      description: service.shortDescription,
      type: "website",
      locale: "fr_FR",
      siteName: "Charlie Oscar Consulting"
    },
    twitter: {
      card: "summary_large_image",
      title: `${service.title} - Service Foncier | Charlie Oscar Consulting`,
      description: service.shortDescription
    },
    alternates: {
      canonical: `/foncier/services/${service.slug}`
    }
  };
}

export default async function ServicePage({ params }: ServicePageProps) {
  const { slug } = await params;
  const service = getFoncierServiceBySlug(slug);

  if (!service) {
    notFound();
  }

  // Extract custom FAQs for this service, if any
  const customFaqs = service.faq || [];
console.log("Custom FAQs for service:", customFaqs);
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <ServiceHero service={service} />
      {/* Description Section */}
      <ServiceDescription service={service} />
      {/* Shared Foncier Process Section */}
      <ProcessSection />
      {/* Shared Foncier FAQ Section with custom FAQs */}
      
      {/* <FaqSection customFaqs={customFaqs} /> to be implemented*/} 
      
      {/* Shared Foncier Final CTA Section */}
      {/* <FinalCtaSection /> */}
    </div>
  );
}
