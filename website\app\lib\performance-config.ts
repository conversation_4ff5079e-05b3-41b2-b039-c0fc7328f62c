// Performance monitoring and optimization configuration

// Core Web Vitals thresholds
export const PERFORMANCE_THRESHOLDS = {
  // Largest Contentful Paint (LCP) - should be < 2.5s
  LCP: {
    good: 2500,
    needsImprovement: 4000
  },
  // First Input Delay (FID) - should be < 100ms
  FID: {
    good: 100,
    needsImprovement: 300
  },
  // Cumulative Layout Shift (CLS) - should be < 0.1
  CLS: {
    good: 0.1,
    needsImprovement: 0.25
  },
  // First Contentful Paint (FCP) - should be < 1.8s
  FCP: {
    good: 1800,
    needsImprovement: 3000
  }
};

// Performance monitoring function
export function initPerformanceMonitoring() {
  if (typeof window === 'undefined') return;

  // Monitor Core Web Vitals
  if ('web-vital' in window) {
    // This would be used with web-vitals library
    console.log('Web Vitals monitoring initialized');
  }

  // Monitor page load performance
  window.addEventListener('load', () => {
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
    
    if (navigation) {
      const metrics = {
        dns: navigation.domainLookupEnd - navigation.domainLookupStart,
        tcp: navigation.connectEnd - navigation.connectStart,
        request: navigation.responseStart - navigation.requestStart,
        response: navigation.responseEnd - navigation.responseStart,
        dom: navigation.domContentLoadedEventEnd - navigation.responseEnd,
        load: navigation.loadEventEnd - navigation.loadEventStart,
        total: navigation.loadEventEnd - navigation.navigationStart
      };

      // Log performance metrics (in production, send to analytics)
      if (process.env.NODE_ENV === 'development') {
        console.table(metrics);
      }
    }
  });
}

// Image optimization helpers
export const IMAGE_OPTIMIZATION = {
  // Recommended image sizes for different breakpoints
  breakpoints: {
    mobile: 640,
    tablet: 768,
    desktop: 1024,
    large: 1280
  },
  
  // Generate responsive image sizes
  generateSizes: (maxWidth: number = 1200) => {
    return `(max-width: 640px) 100vw, (max-width: 768px) 80vw, (max-width: 1024px) 60vw, ${maxWidth}px`;
  },
  
  // Lazy loading configuration
  lazyLoading: {
    rootMargin: '50px',
    threshold: 0.1
  }
};

// Bundle analysis helpers
export const BUNDLE_OPTIMIZATION = {
  // Critical CSS patterns
  criticalCSS: [
    'header',
    'hero',
    'navigation',
    'above-fold'
  ],
  
  // Code splitting recommendations
  codeSplitting: {
    // Routes that should be lazy loaded
    lazyRoutes: [
      '/blog',
      '/projets',
      '/prototype'
    ],
    
    // Components that should be dynamically imported
    lazyComponents: [
      'ContactForm',
      'ImageCarousel',
      'MultiStepForm'
    ]
  }
};

// SEO performance helpers
export const SEO_PERFORMANCE = {
  // Meta tag optimization
  metaTags: {
    maxTitleLength: 60,
    maxDescriptionLength: 160,
    maxKeywords: 10
  },
  
  // Structured data validation
  validateStructuredData: (data: any) => {
    // Basic validation for structured data
    return data['@context'] && data['@type'];
  },
  
  // Image SEO optimization
  imagesSEO: {
    maxFileSize: 100 * 1024, // 100KB
    recommendedFormats: ['webp', 'avif', 'jpg'],
    altTextMaxLength: 125
  }
};

// Error tracking configuration
export const ERROR_TRACKING = {
  // Error boundaries configuration
  errorBoundary: {
    fallbackComponent: 'ErrorFallback',
    onError: (error: Error, errorInfo: any) => {
      if (process.env.NODE_ENV === 'production') {
        // Send to error tracking service
        console.error('Error caught by boundary:', error, errorInfo);
      }
    }
  },
  
  // Network error handling
  networkErrors: {
    retryAttempts: 3,
    retryDelay: 1000,
    timeoutDuration: 10000
  }
};

// Recommended performance monitoring tools
export const MONITORING_TOOLS = {
  // Free tools
  free: [
    {
      name: 'Google PageSpeed Insights',
      url: 'https://pagespeed.web.dev/',
      description: 'Analyze page performance and get optimization suggestions'
    },
    {
      name: 'Google Search Console',
      url: 'https://search.google.com/search-console',
      description: 'Monitor search performance and Core Web Vitals'
    },
    {
      name: 'Lighthouse CI',
      url: 'https://github.com/GoogleChrome/lighthouse-ci',
      description: 'Automated Lighthouse testing in CI/CD'
    },
    {
      name: 'Web Vitals Extension',
      url: 'https://chrome.google.com/webstore/detail/web-vitals/ahfhijdlegdabablpippeagghigmibma',
      description: 'Chrome extension to measure Core Web Vitals'
    }
  ],
  
  // Paid/Premium tools
  premium: [
    {
      name: 'Vercel Analytics',
      url: 'https://vercel.com/analytics',
      description: 'Real user monitoring with Core Web Vitals tracking'
    },
    {
      name: 'Sentry',
      url: 'https://sentry.io/',
      description: 'Error tracking and performance monitoring'
    },
    {
      name: 'LogRocket',
      url: 'https://logrocket.com/',
      description: 'Session replay and performance monitoring'
    },
    {
      name: 'New Relic',
      url: 'https://newrelic.com/',
      description: 'Full-stack observability platform'
    }
  ]
};

// Performance optimization checklist
export const OPTIMIZATION_CHECKLIST = {
  images: [
    'Use Next.js Image component with optimization',
    'Implement lazy loading for below-fold images',
    'Use WebP/AVIF formats when possible',
    'Optimize image sizes for different breakpoints',
    'Add proper alt text for SEO'
  ],
  
  javascript: [
    'Implement code splitting for large components',
    'Use dynamic imports for non-critical components',
    'Minimize bundle size with tree shaking',
    'Remove unused dependencies',
    'Use React.memo for expensive components'
  ],
  
  css: [
    'Extract critical CSS for above-fold content',
    'Use CSS modules or styled-components for better optimization',
    'Minimize unused CSS with PurgeCSS',
    'Use CSS Grid/Flexbox instead of heavy frameworks',
    'Implement CSS-in-JS for component-specific styles'
  ],
  
  networking: [
    'Enable HTTP/2 server push for critical resources',
    'Use CDN for static assets',
    'Implement proper caching headers',
    'Minimize HTTP requests',
    'Use resource hints (preload, prefetch, preconnect)'
  ],
  
  seo: [
    'Implement proper meta tags on all pages',
    'Add structured data (JSON-LD)',
    'Create comprehensive sitemap.xml',
    'Optimize robots.txt',
    'Implement canonical URLs'
  ]
};
