{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/charlie-oscar-consulting/website/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card\"\r\n      className={cn(\r\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-header\"\r\n      className={cn(\r\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-title\"\r\n      className={cn(\"leading-none font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-action\"\r\n      className={cn(\r\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-content\"\r\n      className={cn(\"px-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-footer\"\r\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Card,\r\n  CardHeader,\r\n  CardFooter,\r\n  CardTitle,\r\n  CardAction,\r\n  CardDescription,\r\n  CardContent,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,4TAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,4TAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,4TAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,4TAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,4TAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,4TAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,4TAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 122, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/charlie-oscar-consulting/website/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst badgeVariants = cva(\r\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\r\n        secondary:\r\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\r\n        destructive:\r\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\r\n        outline:\r\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nfunction Badge({\r\n  className,\r\n  variant,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"span\"> &\r\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\r\n  const Comp = asChild ? Slot : \"span\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"badge\"\r\n      className={cn(badgeVariants({ variant }), className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Badge, badgeVariants }\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,8OAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,uSAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,4TAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 174, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/charlie-oscar-consulting/website/components/ui/table.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Table({ className, ...props }: React.ComponentProps<\"table\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"table-container\"\r\n      className=\"relative w-full overflow-x-auto\"\r\n    >\r\n      <table\r\n        data-slot=\"table\"\r\n        className={cn(\"w-full caption-bottom text-sm\", className)}\r\n        {...props}\r\n      />\r\n    </div>\r\n  )\r\n}\r\n\r\nfunction TableHeader({ className, ...props }: React.ComponentProps<\"thead\">) {\r\n  return (\r\n    <thead\r\n      data-slot=\"table-header\"\r\n      className={cn(\"[&_tr]:border-b\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableBody({ className, ...props }: React.ComponentProps<\"tbody\">) {\r\n  return (\r\n    <tbody\r\n      data-slot=\"table-body\"\r\n      className={cn(\"[&_tr:last-child]:border-0\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableFooter({ className, ...props }: React.ComponentProps<\"tfoot\">) {\r\n  return (\r\n    <tfoot\r\n      data-slot=\"table-footer\"\r\n      className={cn(\r\n        \"bg-muted/50 border-t font-medium [&>tr]:last:border-b-0\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableRow({ className, ...props }: React.ComponentProps<\"tr\">) {\r\n  return (\r\n    <tr\r\n      data-slot=\"table-row\"\r\n      className={cn(\r\n        \"hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableHead({ className, ...props }: React.ComponentProps<\"th\">) {\r\n  return (\r\n    <th\r\n      data-slot=\"table-head\"\r\n      className={cn(\r\n        \"text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableCell({ className, ...props }: React.ComponentProps<\"td\">) {\r\n  return (\r\n    <td\r\n      data-slot=\"table-cell\"\r\n      className={cn(\r\n        \"p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableCaption({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"caption\">) {\r\n  return (\r\n    <caption\r\n      data-slot=\"table-caption\"\r\n      className={cn(\"text-muted-foreground mt-4 text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Table,\r\n  TableHeader,\r\n  TableBody,\r\n  TableFooter,\r\n  TableHead,\r\n  TableRow,\r\n  TableCell,\r\n  TableCaption,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAIA;AAJA;;;AAMA,SAAS,MAAM,EAAE,SAAS,EAAE,GAAG,OAAsC;IACnE,qBACE,4TAAC;QACC,aAAU;QACV,WAAU;kBAEV,cAAA,4TAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;YAC9C,GAAG,KAAK;;;;;;;;;;;AAIjB;KAbS;AAeT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,4TAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAChC,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAsC;IACvE,qBACE,4TAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,4TAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAmC;IACnE,qBACE,4TAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,4TAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,sJACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,4TAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OAC6B;IAChC,qBACE,4TAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 312, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/charlie-oscar-consulting/website/app/m/lib/mock-data.ts"], "sourcesContent": ["export interface BlogPost {\r\n  id: string\r\n  title: string\r\n  slug: string\r\n  content: string // Changed from any[] to string\r\n  excerpt: string\r\n  status: \"draft\" | \"published\" | \"archived\"\r\n  author: string\r\n  createdAt: string\r\n  updatedAt: string\r\n  tags: string[]\r\n  featuredImage?: string\r\n}\r\n\r\nexport interface FoncierService {\r\n  id: string\r\n  title: string\r\n  slug: string\r\n  content: string // Changed from any[] to string\r\n  description: string\r\n  status: \"active\" | \"inactive\"\r\n  price?: string\r\n  duration?: string\r\n  createdAt: string\r\n  updatedAt: string\r\n  category: string\r\n  featuredImage?: string\r\n}\r\n\r\nexport interface MediaFile {\r\n  id: string\r\n  name: string\r\n  url: string\r\n  type: \"image\" | \"video\" | \"document\"\r\n  size: number\r\n  uploadedAt: string\r\n  uploadedBy: string\r\n  alt?: string\r\n  caption?: string\r\n}\r\n\r\nexport interface User {\r\n  id: string\r\n  name: string\r\n  email: string\r\n  role: \"admin\" | \"editor\" | \"publisher\"\r\n  status: \"active\" | \"inactive\"\r\n  createdAt: string\r\n  lastLogin?: string\r\n}\r\n\r\n// Mock data\r\nexport const mockBlogPosts: BlogPost[] = [\r\n  {\r\n    id: \"1\",\r\n    title: \"Understanding Real Estate Market Trends\",\r\n    slug: \"understanding-real-estate-market-trends\",\r\n    content:\r\n      \"Real estate market analysis involves examining various factors that influence property values and market dynamics. This comprehensive guide explores current trends, economic indicators, and their implications for investors and homeowners alike.\\n\\nKey factors to consider include interest rates, employment levels, population growth, and local development projects. Understanding these elements helps in making informed decisions about buying, selling, or investing in real estate properties.\",\r\n    excerpt: \"A comprehensive guide to understanding current real estate market trends and their implications.\",\r\n    status: \"published\",\r\n    author: \"Admin User\",\r\n    createdAt: \"2024-01-15T10:00:00Z\",\r\n    updatedAt: \"2024-01-15T10:00:00Z\",\r\n    tags: [\"real-estate\", \"market-analysis\", \"trends\"],\r\n    featuredImage: \"/placeholder.svg?height=200&width=400\",\r\n  },\r\n  {\r\n    id: \"2\",\r\n    title: \"Investment Strategies for 2024\",\r\n    slug: \"investment-strategies-2024\",\r\n    content:\r\n      \"Investment strategies overview for the upcoming year focuses on diversification, risk management, and emerging opportunities in various sectors. This article explores different approaches to building a robust investment portfolio.\\n\\nWe'll cover traditional investment vehicles, alternative investments, and the importance of aligning your strategy with your financial goals and risk tolerance.\",\r\n    excerpt: \"Explore the best investment strategies for the upcoming year.\",\r\n    status: \"draft\",\r\n    author: \"Editor User\",\r\n    createdAt: \"2024-01-10T14:30:00Z\",\r\n    updatedAt: \"2024-01-12T09:15:00Z\",\r\n    tags: [\"investment\", \"strategy\", \"2024\"],\r\n  },\r\n]\r\n\r\nexport const mockFoncierServices: FoncierService[] = [\r\n  {\r\n    id: \"1\",\r\n    title: \"Property Valuation Services\",\r\n    slug: \"property-valuation-services\",\r\n    content:\r\n      \"Professional property valuation services for residential and commercial properties. Our certified appraisers use industry-standard methodologies to provide accurate and reliable property valuations.\\n\\nOur comprehensive valuation process includes:\\n- Market analysis and comparable sales research\\n- Physical inspection of the property\\n- Assessment of property condition and features\\n- Analysis of local market conditions\\n- Detailed valuation report with supporting documentation\\n\\nWhether you need a valuation for mortgage purposes, insurance, tax assessment, or investment decisions, our experienced team delivers precise and timely results.\",\r\n    description: \"Comprehensive property valuation services for residential and commercial properties.\",\r\n    status: \"active\",\r\n    price: \"€500 - €2000\",\r\n    duration: \"3-5 business days\",\r\n    createdAt: \"2024-01-01T00:00:00Z\",\r\n    updatedAt: \"2024-01-01T00:00:00Z\",\r\n    category: \"Valuation\",\r\n    featuredImage: \"/placeholder.svg?height=200&width=400\",\r\n  },\r\n  {\r\n    id: \"2\",\r\n    title: \"Land Survey and Analysis\",\r\n    slug: \"land-survey-analysis\",\r\n    content:\r\n      \"Detailed land surveying and analysis services for development projects, property boundaries, and construction planning. Our licensed surveyors use advanced equipment and techniques to provide accurate measurements and comprehensive reports.\\n\\nServices include:\\n- Boundary surveys and property line determination\\n- Topographic surveys for construction planning\\n- ALTA/NSPS surveys for commercial transactions\\n- Construction staking and layout\\n- Subdivision planning and platting\\n\\nWe work closely with architects, engineers, developers, and property owners to ensure projects meet all regulatory requirements and proceed smoothly from planning to completion.\",\r\n    description: \"Professional land surveying and analysis services for development projects.\",\r\n    status: \"active\",\r\n    price: \"€800 - €3000\",\r\n    duration: \"1-2 weeks\",\r\n    createdAt: \"2024-01-05T00:00:00Z\",\r\n    updatedAt: \"2024-01-05T00:00:00Z\",\r\n    category: \"Survey\",\r\n  },\r\n]\r\n\r\nexport const mockMediaFiles: MediaFile[] = [\r\n  {\r\n    id: \"1\",\r\n    name: \"office-building.jpg\",\r\n    url: \"/placeholder.svg?height=300&width=400\",\r\n    type: \"image\",\r\n    size: 245760,\r\n    uploadedAt: \"2024-01-15T10:00:00Z\",\r\n    uploadedBy: \"Admin User\",\r\n    alt: \"Modern office building\",\r\n    caption: \"Commercial property in downtown area\",\r\n  },\r\n  {\r\n    id: \"2\",\r\n    name: \"property-tour.mp4\",\r\n    url: \"/placeholder.svg?height=300&width=400\",\r\n    type: \"video\",\r\n    size: 15728640,\r\n    uploadedAt: \"2024-01-14T15:30:00Z\",\r\n    uploadedBy: \"Editor User\",\r\n    caption: \"Virtual property tour video\",\r\n  },\r\n]\r\n\r\nexport const mockUsers: User[] = [\r\n  {\r\n    id: \"1\",\r\n    name: \"Admin User\",\r\n    email: \"<EMAIL>\",\r\n    role: \"admin\",\r\n    status: \"active\",\r\n    createdAt: \"2023-12-01T00:00:00Z\",\r\n    lastLogin: \"2024-01-15T09:00:00Z\",\r\n  },\r\n  {\r\n    id: \"2\",\r\n    name: \"Editor User\",\r\n    email: \"<EMAIL>\",\r\n    role: \"editor\",\r\n    status: \"active\",\r\n    createdAt: \"2023-12-15T00:00:00Z\",\r\n    lastLogin: \"2024-01-14T16:30:00Z\",\r\n  },\r\n  {\r\n    id: \"3\",\r\n    name: \"Publisher User\",\r\n    email: \"<EMAIL>\",\r\n    role: \"publisher\",\r\n    status: \"active\",\r\n    createdAt: \"2024-01-01T00:00:00Z\",\r\n    lastLogin: \"2024-01-13T11:15:00Z\",\r\n  },\r\n]\r\n"], "names": [], "mappings": ";;;;;;AAoDO,MAAM,gBAA4B;IACvC;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,SACE;QACF,SAAS;QACT,QAAQ;QACR,QAAQ;QACR,WAAW;QACX,WAAW;QACX,MAAM;YAAC;YAAe;YAAmB;SAAS;QAClD,eAAe;IACjB;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,SACE;QACF,SAAS;QACT,QAAQ;QACR,QAAQ;QACR,WAAW;QACX,WAAW;QACX,MAAM;YAAC;YAAc;YAAY;SAAO;IAC1C;CACD;AAEM,MAAM,sBAAwC;IACnD;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,SACE;QACF,aAAa;QACb,QAAQ;QACR,OAAO;QACP,UAAU;QACV,WAAW;QACX,WAAW;QACX,UAAU;QACV,eAAe;IACjB;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,SACE;QACF,aAAa;QACb,QAAQ;QACR,OAAO;QACP,UAAU;QACV,WAAW;QACX,WAAW;QACX,UAAU;IACZ;CACD;AAEM,MAAM,iBAA8B;IACzC;QACE,IAAI;QACJ,MAAM;QACN,KAAK;QACL,MAAM;QACN,MAAM;QACN,YAAY;QACZ,YAAY;QACZ,KAAK;QACL,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,KAAK;QACL,MAAM;QACN,MAAM;QACN,YAAY;QACZ,YAAY;QACZ,SAAS;IACX;CACD;AAEM,MAAM,YAAoB;IAC/B;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,MAAM;QACN,QAAQ;QACR,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,MAAM;QACN,QAAQ;QACR,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,MAAM;QACN,QAAQ;QACR,WAAW;QACX,WAAW;IACb;CACD", "debugId": null}}, {"offset": {"line": 443, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/charlie-oscar-consulting/website/app/m/dashboard/cms/blog/page.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport { useState } from \"react\"\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\r\nimport { Input } from \"@/components/ui/input\"\r\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\r\nimport { Badge } from \"@/components/ui/badge\"\r\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from \"@/components/ui/table\"\r\nimport { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from \"@/components/ui/dropdown-menu\"\r\nimport { Plus, Search, MoreHorizontal, Edit, Trash2, Eye } from \"lucide-react\"\r\nimport Link from \"next/link\"\r\nimport { mockBlogPosts, type BlogPost } from \"@/app/m/lib/mock-data\"\r\n\r\nexport default function BlogListPage() {\r\n  const [searchTerm, setSearchTerm] = useState(\"\")\r\n  const [posts] = useState<BlogPost[]>(mockBlogPosts)\r\n\r\n  const filteredPosts = posts.filter(\r\n    (post) =>\r\n      post.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n      post.tags.some((tag) => tag.toLowerCase().includes(searchTerm.toLowerCase())),\r\n  )\r\n\r\n  const getStatusColor = (status: BlogPost[\"status\"]) => {\r\n    switch (status) {\r\n      case \"published\":\r\n        return \"bg-green-100 text-green-800\"\r\n      case \"draft\":\r\n        return \"bg-yellow-100 text-yellow-800\"\r\n      case \"archived\":\r\n        return \"bg-gray-100 text-gray-800\"\r\n      default:\r\n        return \"bg-gray-100 text-gray-800\"\r\n    }\r\n  }\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      <div className=\"flex items-center justify-between\">\r\n        <div>\r\n          <h1 className=\"text-3xl font-bold tracking-tight\">Blog Posts</h1>\r\n          <p className=\"text-muted-foreground\">Manage your blog content and articles</p>\r\n        </div>\r\n        <Button asChild>\r\n          <Link href=\"/m/dashboard/cms/blog/add\">\r\n            <Plus className=\"mr-2 h-4 w-4\" />\r\n            New Post\r\n          </Link>\r\n        </Button>\r\n      </div>\r\n\r\n      <Card>\r\n        <CardHeader>\r\n          <CardTitle>All Posts</CardTitle>\r\n          <CardDescription>A list of all blog posts in your system</CardDescription>\r\n        </CardHeader>\r\n        <CardContent>\r\n          <div className=\"flex items-center space-x-2 mb-4\">\r\n            <div className=\"relative flex-1 max-w-sm\">\r\n              <Search className=\"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground\" />\r\n              <Input\r\n                placeholder=\"Search posts...\"\r\n                value={searchTerm}\r\n                onChange={(e) => setSearchTerm(e.target.value)}\r\n                className=\"pl-8\"\r\n              />\r\n            </div>\r\n          </div>\r\n\r\n          <Table>\r\n            <TableHeader>\r\n              <TableRow>\r\n                <TableHead>Title</TableHead>\r\n                <TableHead>Status</TableHead>\r\n                <TableHead>Author</TableHead>\r\n                <TableHead>Created</TableHead>\r\n                <TableHead>Tags</TableHead>\r\n                <TableHead className=\"w-[70px]\">Actions</TableHead>\r\n              </TableRow>\r\n            </TableHeader>\r\n            <TableBody>\r\n              {filteredPosts.map((post) => (\r\n                <TableRow key={post.id}>\r\n                  <TableCell>\r\n                    <div>\r\n                      <div className=\"font-medium\">{post.title}</div>\r\n                      <div className=\"text-sm text-muted-foreground\">{post.excerpt}</div>\r\n                    </div>\r\n                  </TableCell>\r\n                  <TableCell>\r\n                    <Badge className={getStatusColor(post.status)}>{post.status}</Badge>\r\n                  </TableCell>\r\n                  <TableCell>{post.author}</TableCell>\r\n                  <TableCell>{new Date(post.createdAt).toLocaleDateString()}</TableCell>\r\n                  <TableCell>\r\n                    <div className=\"flex flex-wrap gap-1\">\r\n                      {post.tags.slice(0, 2).map((tag) => (\r\n                        <Badge key={tag} variant=\"outline\" className=\"text-xs\">\r\n                          {tag}\r\n                        </Badge>\r\n                      ))}\r\n                      {post.tags.length > 2 && (\r\n                        <Badge variant=\"outline\" className=\"text-xs\">\r\n                          +{post.tags.length - 2}\r\n                        </Badge>\r\n                      )}\r\n                    </div>\r\n                  </TableCell>\r\n                  <TableCell>\r\n                    <DropdownMenu>\r\n                      <DropdownMenuTrigger asChild>\r\n                        <Button variant=\"ghost\" className=\"h-8 w-8 p-0\">\r\n                          <MoreHorizontal className=\"h-4 w-4\" />\r\n                        </Button>\r\n                      </DropdownMenuTrigger>\r\n                      <DropdownMenuContent align=\"end\">\r\n                        <DropdownMenuItem asChild>\r\n                          <Link href={`m/dashboard/cms/blog/${post.id}`}>\r\n                            <Eye className=\"mr-2 h-4 w-4\" />\r\n                            View\r\n                          </Link>\r\n                        </DropdownMenuItem>\r\n                        <DropdownMenuItem asChild>\r\n                          <Link href={`m/dashboard/cms/blog/${post.id}/edit`}>\r\n                            <Edit className=\"mr-2 h-4 w-4\" />\r\n                            Edit\r\n                          </Link>\r\n                        </DropdownMenuItem>\r\n                        <DropdownMenuItem className=\"text-red-600\">\r\n                          <Trash2 className=\"mr-2 h-4 w-4\" />\r\n                          Delete\r\n                        </DropdownMenuItem>\r\n                      </DropdownMenuContent>\r\n                    </DropdownMenu>\r\n                  </TableCell>\r\n                </TableRow>\r\n              ))}\r\n            </TableBody>\r\n          </Table>\r\n        </CardContent>\r\n      </Card>\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;;;AAXA;;;;;;;;;;;AAae,SAAS;;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,MAAM,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAc,kIAAA,CAAA,gBAAa;IAElD,MAAM,gBAAgB,MAAM,MAAM,CAChC,CAAC,OACC,KAAK,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACxD,KAAK,IAAI,CAAC,IAAI,CAAC,CAAC,MAAQ,IAAI,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;IAG7E,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,4TAAC;QAAI,WAAU;;0BACb,4TAAC;gBAAI,WAAU;;kCACb,4TAAC;;0CACC,4TAAC;gCAAG,WAAU;0CAAoC;;;;;;0CAClD,4TAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAEvC,4TAAC,8HAAA,CAAA,SAAM;wBAAC,OAAO;kCACb,cAAA,4TAAC,8RAAA,CAAA,UAAI;4BAAC,MAAK;;8CACT,4TAAC,yRAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;0BAMvC,4TAAC,4HAAA,CAAA,OAAI;;kCACH,4TAAC,4HAAA,CAAA,aAAU;;0CACT,4TAAC,4HAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,4TAAC,4HAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAEnB,4TAAC,4HAAA,CAAA,cAAW;;0CACV,4TAAC;gCAAI,WAAU;0CACb,cAAA,4TAAC;oCAAI,WAAU;;sDACb,4TAAC,6RAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,4TAAC,6HAAA,CAAA,QAAK;4CACJ,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4CAC7C,WAAU;;;;;;;;;;;;;;;;;0CAKhB,4TAAC,6HAAA,CAAA,QAAK;;kDACJ,4TAAC,6HAAA,CAAA,cAAW;kDACV,cAAA,4TAAC,6HAAA,CAAA,WAAQ;;8DACP,4TAAC,6HAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,4TAAC,6HAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,4TAAC,6HAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,4TAAC,6HAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,4TAAC,6HAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,4TAAC,6HAAA,CAAA,YAAS;oDAAC,WAAU;8DAAW;;;;;;;;;;;;;;;;;kDAGpC,4TAAC,6HAAA,CAAA,YAAS;kDACP,cAAc,GAAG,CAAC,CAAC,qBAClB,4TAAC,6HAAA,CAAA,WAAQ;;kEACP,4TAAC,6HAAA,CAAA,YAAS;kEACR,cAAA,4TAAC;;8EACC,4TAAC;oEAAI,WAAU;8EAAe,KAAK,KAAK;;;;;;8EACxC,4TAAC;oEAAI,WAAU;8EAAiC,KAAK,OAAO;;;;;;;;;;;;;;;;;kEAGhE,4TAAC,6HAAA,CAAA,YAAS;kEACR,cAAA,4TAAC,6HAAA,CAAA,QAAK;4DAAC,WAAW,eAAe,KAAK,MAAM;sEAAI,KAAK,MAAM;;;;;;;;;;;kEAE7D,4TAAC,6HAAA,CAAA,YAAS;kEAAE,KAAK,MAAM;;;;;;kEACvB,4TAAC,6HAAA,CAAA,YAAS;kEAAE,IAAI,KAAK,KAAK,SAAS,EAAE,kBAAkB;;;;;;kEACvD,4TAAC,6HAAA,CAAA,YAAS;kEACR,cAAA,4TAAC;4DAAI,WAAU;;gEACZ,KAAK,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,oBAC1B,4TAAC,6HAAA,CAAA,QAAK;wEAAW,SAAQ;wEAAU,WAAU;kFAC1C;uEADS;;;;;gEAIb,KAAK,IAAI,CAAC,MAAM,GAAG,mBAClB,4TAAC,6HAAA,CAAA,QAAK;oEAAC,SAAQ;oEAAU,WAAU;;wEAAU;wEACzC,KAAK,IAAI,CAAC,MAAM,GAAG;;;;;;;;;;;;;;;;;;kEAK7B,4TAAC,6HAAA,CAAA,YAAS;kEACR,cAAA,4TAAC,wIAAA,CAAA,eAAY;;8EACX,4TAAC,wIAAA,CAAA,sBAAmB;oEAAC,OAAO;8EAC1B,cAAA,4TAAC,8HAAA,CAAA,SAAM;wEAAC,SAAQ;wEAAQ,WAAU;kFAChC,cAAA,4TAAC,uSAAA,CAAA,iBAAc;4EAAC,WAAU;;;;;;;;;;;;;;;;8EAG9B,4TAAC,wIAAA,CAAA,sBAAmB;oEAAC,OAAM;;sFACzB,4TAAC,wIAAA,CAAA,mBAAgB;4EAAC,OAAO;sFACvB,cAAA,4TAAC,8RAAA,CAAA,UAAI;gFAAC,MAAM,CAAC,qBAAqB,EAAE,KAAK,EAAE,EAAE;;kGAC3C,4TAAC,uRAAA,CAAA,MAAG;wFAAC,WAAU;;;;;;oFAAiB;;;;;;;;;;;;sFAIpC,4TAAC,wIAAA,CAAA,mBAAgB;4EAAC,OAAO;sFACvB,cAAA,4TAAC,8RAAA,CAAA,UAAI;gFAAC,MAAM,CAAC,qBAAqB,EAAE,KAAK,EAAE,CAAC,KAAK,CAAC;;kGAChD,4TAAC,kSAAA,CAAA,OAAI;wFAAC,WAAU;;;;;;oFAAiB;;;;;;;;;;;;sFAIrC,4TAAC,wIAAA,CAAA,mBAAgB;4EAAC,WAAU;;8FAC1B,4TAAC,iSAAA,CAAA,SAAM;oFAAC,WAAU;;;;;;gFAAiB;;;;;;;;;;;;;;;;;;;;;;;;;+CA/C9B,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6DtC;GAlIwB;KAAA", "debugId": null}}, {"offset": {"line": 926, "column": 0}, "map": {"version": 3, "file": "plus.js", "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/charlie-oscar-consulting/website/node_modules/.pnpm/lucide-react%400.503.0_react%4019.1.0/node_modules/lucide-react/src/icons/plus.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M5 12h14', key: '1ays0h' }],\n  ['path', { d: 'M12 5v14', key: 's699le' }],\n];\n\n/**\n * @component @name Plus\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNSAxMmgxNCIgLz4KICA8cGF0aCBkPSJNMTIgNXYxNCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/plus\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Plus = createLucideIcon('plus', __iconNode);\n\nexport default Plus;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA,CAAA;IACzC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAC3C,CAAA;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAO,CAAA,+PAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 972, "column": 0}, "map": {"version": 3, "file": "search.js", "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/charlie-oscar-consulting/website/node_modules/.pnpm/lucide-react%400.503.0_react%4019.1.0/node_modules/lucide-react/src/icons/search.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '11', cy: '11', r: '8', key: '4ej97u' }],\n  ['path', { d: 'm21 21-4.3-4.3', key: '1qie3q' }],\n];\n\n/**\n * @component @name Search\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMSIgY3k9IjExIiByPSI4IiAvPgogIDxwYXRoIGQ9Im0yMSAyMS00LjMtNC4zIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/search\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Search = createLucideIcon('search', __iconNode);\n\nexport default Search;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA,CAAA;IAClC;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA;YAAK,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA,CAAA;IACxD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAkB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CACjD,CAAA;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAS,CAAA,+PAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAU,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1020, "column": 0}, "map": {"version": 3, "file": "ellipsis.js", "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/charlie-oscar-consulting/website/node_modules/.pnpm/lucide-react%400.503.0_react%4019.1.0/node_modules/lucide-react/src/icons/ellipsis.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '12', cy: '12', r: '1', key: '41hilf' }],\n  ['circle', { cx: '19', cy: '12', r: '1', key: '1wjl8i' }],\n  ['circle', { cx: '5', cy: '12', r: '1', key: '1pcz8c' }],\n];\n\n/**\n * @component @name Ellipsis\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxIiAvPgogIDxjaXJjbGUgY3g9IjE5IiBjeT0iMTIiIHI9IjEiIC8+CiAgPGNpcmNsZSBjeD0iNSIgY3k9IjEyIiByPSIxIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/ellipsis\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Ellipsis = createLucideIcon('ellipsis', __iconNode);\n\nexport default Ellipsis;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA,CAAA;IAClC;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA;YAAK,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA,CAAA;IACxD;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA;YAAK,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA,CAAA;IACxD;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA;YAAK,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;CACzD,CAAA;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAW,CAAA,+PAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAY,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1079, "column": 0}, "map": {"version": 3, "file": "square-pen.js", "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/charlie-oscar-consulting/website/node_modules/.pnpm/lucide-react%400.503.0_react%4019.1.0/node_modules/lucide-react/src/icons/square-pen.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7', key: '1m0v6g' }],\n  [\n    'path',\n    {\n      d: 'M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z',\n      key: 'ohrbg2',\n    },\n  ],\n];\n\n/**\n * @component @name SquarePen\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgM0g1YTIgMiAwIDAgMC0yIDJ2MTRhMiAyIDAgMCAwIDIgMmgxNGEyIDIgMCAwIDAgMi0ydi03IiAvPgogIDxwYXRoIGQ9Ik0xOC4zNzUgMi42MjVhMSAxIDAgMCAxIDMgM2wtOS4wMTMgOS4wMTRhMiAyIDAgMCAxLS44NTMuNTA1bC0yLjg3My44NGEuNS41IDAgMCAxLS42Mi0uNjJsLjg0LTIuODczYTIgMiAwIDAgMSAuNTA2LS44NTJ6IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/square-pen\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst SquarePen = createLucideIcon('square-pen', __iconNode);\n\nexport default SquarePen;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA8D,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA,CAAA;IAC3F,CAAA;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA,CAAA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,CAAA,CAAA,CAAK,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACP,CAAA;KACF;CACF,CAAA;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,SAAA,CAAY,CAAA,+PAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,AAAjB,CAAA,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAc,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1125, "column": 0}, "map": {"version": 3, "file": "trash-2.js", "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/charlie-oscar-consulting/website/node_modules/.pnpm/lucide-react%400.503.0_react%4019.1.0/node_modules/lucide-react/src/icons/trash-2.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M3 6h18', key: 'd0wm0j' }],\n  ['path', { d: 'M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6', key: '4alrt4' }],\n  ['path', { d: 'M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2', key: 'v07s0e' }],\n  ['line', { x1: '10', x2: '10', y1: '11', y2: '17', key: '1uufr5' }],\n  ['line', { x1: '14', x2: '14', y1: '11', y2: '17', key: 'xtxkd' }],\n];\n\n/**\n * @component @name Trash2\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMyA2aDE4IiAvPgogIDxwYXRoIGQ9Ik0xOSA2djE0YzAgMS0xIDItMiAySDdjLTEgMC0yLTEtMi0yVjYiIC8+CiAgPHBhdGggZD0iTTggNlY0YzAtMSAxLTIgMi0yaDRjMSAwIDIgMSAyIDJ2MiIgLz4KICA8bGluZSB4MT0iMTAiIHgyPSIxMCIgeTE9IjExIiB5Mj0iMTciIC8+CiAgPGxpbmUgeDE9IjE0IiB4Mj0iMTQiIHkxPSIxMSIgeTI9IjE3IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/trash-2\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Trash2 = createLucideIcon('trash-2', __iconNode);\n\nexport default Trash2;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA,CAAA;IACxC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAyC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA,CAAA;IACtE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAsC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA,CAAA;IACnE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA,CAAA;IAClE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAS;KAAA;CACnE,CAAA;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAS,CAAA,+PAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAW,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1198, "column": 0}, "map": {"version": 3, "file": "eye.js", "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/charlie-oscar-consulting/website/node_modules/.pnpm/lucide-react%400.503.0_react%4019.1.0/node_modules/lucide-react/src/icons/eye.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0',\n      key: '1nclc0',\n    },\n  ],\n  ['circle', { cx: '12', cy: '12', r: '3', key: '1v7zrd' }],\n];\n\n/**\n * @component @name Eye\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMi4wNjIgMTIuMzQ4YTEgMSAwIDAgMSAwLS42OTYgMTAuNzUgMTAuNzUgMCAwIDEgMTkuODc2IDAgMSAxIDAgMCAxIDAgLjY5NiAxMC43NSAxMC43NSAwIDAgMS0xOS44NzYgMCIgLz4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIzIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/eye\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Eye = createLucideIcon('eye', __iconNode);\n\nexport default Eye;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA,CAAA;IAClC,CAAA;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA,CAAA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,CAAA,CAAA,CAAK,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACP,CAAA;KACF,CAAA;IACA;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA;YAAK,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;CAC1D,CAAA;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAM,CAAA,+PAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,EAAO,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}]}