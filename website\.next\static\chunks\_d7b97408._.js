(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/app/translations/fr.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// French translations
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
const fr = {
    fill: "Merci de remplir soigneusement ce formulaire. Un expert de Charlie Oscar Consulting vous contactera dans les plus brefs délais pour vous accompagner dans votre projet.",
    common: {
        next: "Continuer",
        previous: "Précédent",
        submit: "Soumettre ma demande",
        processing: "Traitement en cours...",
        required: "*",
        optional: "Facultatif",
        fileSize: "La taille maximale est de 2 Mo",
        filePreview: "Aperçu:",
        fileSelected: "Fichier sélectionné:",
        fileFormats: ".pdf, .jpg, .png, max 2 Mo par fichier",
        notProvided: "Non renseigné",
        notSpecified: "Non précisé",
        uploading: "Téléchargement...",
        uploaded: "Téléchargé",
        uploadFailed: "Échec du téléchargement",
        retry: "Réessayer",
        // success message for succesfull form upload
        success: "Votre demande a été soumise avec succès. Un conseiller de Charlie Oscar Consulting vous contactera dans les plus brefs délais.",
        online: "En ligne",
        offline: "Hors ligne",
        connectionLost: "Connexion Internet perdue. Certaines fonctionnalités peuvent être indisponibles.",
        connectionRestored: "Connexion Internet rétablie.",
        fileUploadOffline: "Vous ne pouvez pas uploader des fichier quand vous êtes hors ligne.",
        waitForUploads: "Veuillez attendre la fin des téléchargements...",
        form: "Formulaire",
        apiDocs: "Documentation API",
        testTools: "Outils de test",
        dropFilesHere: "Déposez vos fichiers ici ou cliquez pour parcourir",
        maxFileSize: "Taille maximale: 2 Mo",
        fileTooLarge: "Le fichier est trop volumineux. La taille maximale est de 2 Mo",
        invalidFileType: "Type de fichier non pris en charge",
        fileNameTooShort: "Le nom du fichier doit comporter au moins 3 caractères",
        cancel: "Annuler"
    },
    validation: {
        required: "Ce champ est obligatoire",
        minLength: "Doit contenir au moins {min} caractères",
        email: "Veuillez entrer une adresse email valide",
        phone: "Veuillez entrer un numéro de téléphone valide",
        consent: "Vous devez donner votre consentement pour continuer",
        date: "Veuillez entrer une date valide",
        selection: "Veuillez faire une sélection",
        fileRequired: "Veuillez téléverser un fichier",
        fileSize: "La taille du fichier doit être inférieure à {size}",
        fileType: "Type de fichier non pris en charge"
    },
    sidebar: {
        title: "Charlie Oscar",
        subtitle: "Consulting",
        step: "Étape",
        of: "/",
        currentStep: "Étape actuelle"
    },
    steps: {
        personal: {
            name: "Informations Personnelles",
            description: "Entrez vos informations personnelles pour nous permettre de mieux vous connaître."
        },
        emergency: {
            name: "Contacts d'urgence et procédure",
            description: "Indiquez vos contacts d'urgence et la procédure souhaitée."
        },
        documents: {
            name: "Documents et localisation du terrain",
            description: "Fournissez les documents et les informations sur la localisation du terrain."
        },
        summary: {
            name: "Récapitulatif et confirmation",
            description: "Vérifiez toutes les informations avant de soumettre votre demande."
        }
    },
    stepPersonal: {
        title: "Vos Informations Personnelles",
        intro: "Veuillez remplir tous les champs obligatoires marqués d'un astérisque (*) avec précision.",
        dataProtection: {
            title: "Protection des données",
            description: "Les informations collectées sont strictement confidentielles et utilisées uniquement dans le cadre du traitement de votre demande foncière par Charlie Oscar Consulting SARL.",
            consent: "J'accepte que mes informations soient traitées par Charlie Oscar Consulting SARL"
        },
        personalInfo: {
            title: "Informations personnelles",
            fullName: {
                label: "Nom & prénoms",
                placeholder: "Ex: MBARGA Paul Serge"
            },
            gender: {
                label: "Sexe",
                male: "Masculin",
                female: "Féminin"
            },
            birthDate: {
                label: "Date de naissance"
            },
            birthPlace: {
                label: "Lieu de naissance",
                placeholder: "Ex: Yaoundé, Cameroun"
            },
            nationality: {
                label: "Nationalité",
                placeholder: "Sélectionnez votre nationalité",
                options: {
                    cameroon: "Camerounaise",
                    france: "Française",
                    senegal: "Sénégalaise",
                    ivoryCoast: "Ivoirienne",
                    gabon: "Gabonaise",
                    congo: "Congolaise",
                    other: "Autre"
                }
            },
            profession: {
                label: "Profession",
                placeholder: "Ex: Enseignant, Commerçant"
            }
        },
        contactInfo: {
            title: "Informations de contact",
            primaryPhone: {
                label: "Téléphone principal",
                placeholder: "Ex: +237 6XXXXXXXX"
            },
            secondaryPhone: {
                label: "Téléphone secondaire",
                placeholder: "Ex: +237 6XXXXXXXX"
            },
            email: {
                label: "Email",
                placeholder: "<EMAIL>"
            },
            address: {
                label: "Adresse actuelle",
                placeholder: "Ex: Quartier Bastos, Rue 1.890, Yaoundé"
            },
            idDocument: {
                label: "Document d'identité",
                description: "Téléversez une copie claire et lisible de votre CNI ou passeport",
                notUploaded: "Aucun document téléversé",
                nameRequired: "Veuillez d'abord saisir votre nom complet"
            }
        }
    },
    stepDocumentsLocation: {
        title: "Documents et localisation du terrain",
        documents: {
            title: "Documents",
            intro: "Veuillez indiquer et téléverser les documents que vous possédez déjà concernant cette procédure. Le plan de localisation est fortement recommandé s'il est disponible.",
            availableDocs: {
                label: "Documents disponibles",
                help: "Cochez tous les documents que vous pouvez fournir",
                options: {
                    customaryProperty: "Attestation de propriété coutumière",
                    saleAct: "Acte de vente / donation",
                    occupationPermit: "Autorisation d'occupation",
                    locationPlan: "Plan de localisation / croquis du terrain",
                    landTitle: "Titre foncier",
                    otherDocs: "Autres documents"
                }
            },
            upload: {
                label: "Téléversement de documents",
                documentLabel: "Document",
                description: "Téléversez vos documents les plus pertinents pour votre demande une a la fois. Vous pourrez fournir des documents supplémentaires ultérieurement."
            },
            details: {
                label: "Précisions sur les documents",
                placeholder: "Ex: Le titre foncier date de 2015, l'acte de vente notarié est joint...",
                help: "Facultatif : précisez la nature ou l'état des documents fournis"
            }
        },
        location: {
            title: "Localisation",
            intro: "Veuillez fournir les informations précises concernant la localisation du terrain concerné par la procédure.",
            zoneType: {
                label: "Type de zone",
                urban: "Urbaine",
                rural: "Rurale",
                help: "Sélectionnez le type d'environnement où se situe le terrain"
            },
            region: {
                label: "Région",
                placeholder: "Sélectionnez une région",
                options: [
                    "Centre",
                    "Littoral",
                    "Sud",
                    "Est",
                    "Ouest",
                    "Nord-Ouest",
                    "Sud-Ouest",
                    "Nord",
                    "Extrême-Nord",
                    "Adamaoua"
                ]
            },
            department: {
                label: "Département",
                placeholder: "Sélectionnez ou preciser un département",
                options: {
                    centre: [
                        "Mfoundi",
                        "Nyong-et-Kellé",
                        "Nyong-et-So'o",
                        "Lekié",
                        "Mbam-et-Inoubou"
                    ],
                    littoral: [
                        "Wouri",
                        "Sanaga-Maritime",
                        "Nkam",
                        "Moungo"
                    ],
                    south: [
                        "Océan",
                        "Vallée-du-Ntem",
                        "Mvila",
                        "Dja-et-Lobo"
                    ],
                    east: [
                        "Haut-Nyong",
                        "Kadey",
                        "Lom-et-Djérem",
                        "Boumba-et-Ngoko"
                    ],
                    west: [
                        "Mifi",
                        "Menoua",
                        "Bamboutos",
                        "Haut-Nkam",
                        "Ndé",
                        "Koung-Khi"
                    ]
                }
            },
            subdivision: {
                label: "Arrondissement",
                placeholder: "Ex: Yaoundé II, Douala III, Bafoussam I",
                help: "Précisez l'arrondissement où se situe le terrain"
            },
            neighborhood: {
                label: "Quartier ou village",
                placeholder: "Ex: Mokolo, Bonapriso, Banengo..."
            },
            locationDetails: {
                label: "Lieu-dit",
                placeholder: "Ex: À proximité de l'église Saint-Jean, derrière le marché central...",
                help: "Repère géographique spécifique facilitant la localisation du terrain"
            },
            area: {
                label: "Superficie estimée",
                placeholder: "Ex: 500",
                unit: "m²",
                help: "Surface approximative du terrain en mètres carrés"
            }
        }
    },
    stepEmergencyProcedure: {
        title: "Procédure et urgence",
        emergencyContacts: {
            title: "Contacts d'urgence",
            intro: "Veuillez indiquer au moins une personne à contacter en cas d'indisponibilité.",
            contact1: {
                title: "Personne 1 (obligatoire)",
                name: {
                    label: "Nom & prénoms",
                    placeholder: "Ex: ATANGANA Marie Claire"
                },
                phone: {
                    label: "Téléphone",
                    placeholder: "Ex: +237 6XXXXXXXX"
                },
                relation: {
                    label: "Lien avec le demandeur",
                    placeholder: "Ex: Frère, Conjoint(e), Collègue",
                    help: "Précisez votre relation avec cette personne"
                }
            },
            contact2: {
                title: "Personne 2 (facultatif)",
                name: {
                    label: "Nom & prénoms",
                    placeholder: "Ex: ESSOMBA Jean"
                },
                phone: {
                    label: "Téléphone",
                    placeholder: "Ex: +237 6XXXXXXXX"
                },
                relation: {
                    label: "Lien avec le demandeur",
                    placeholder: "Ex: Sœur, Parent, Ami(e)"
                }
            }
        },
        landStatus: {
            title: "Statut foncier",
            intro: "Veuillez préciser votre statut par rapport au terrain concerné par la procédure.",
            label: "Votre statut",
            options: {
                owner: "Propriétaire",
                heir: "Héritier",
                buyer: "Acheteur",
                applicant: "Demandeur d'attribution",
                other: "Autre"
            },
            otherLabel: "Précision du statut",
            otherPlaceholder: "Veuillez préciser votre statut"
        },
        procedureType: {
            title: "Type de procédure",
            intro: "Veuillez indiquer le type de procédure foncière que vous souhaitez entreprendre.",
            label: "Type de procédure souhaitée",
            help: "Cette sélection déterminera les documents et procédures nécessaires pour votre dossier",
            options: [
                "Dérogation ministérielle spéciale",
                "Immatriculation directe",
                "Immatriculation par voie de concession",
                "Décision de gré à gré",
                "Protocole d'accord de bonne volonté",
                "Morcellement",
                "Mutation totale",
                "Mutation par décès",
                "Protocole d'achat d'un terrain non titré",
                "Dossier technique",
                "Indemnisation",
                "Rétrocession",
                "Recours gracieux",
                "Réhabilitation de titre foncier",
                "Autre"
            ],
            otherLabel: "Précisions sur la procédure",
            otherPlaceholder: "Veuillez préciser la nature exacte de votre demande",
            otherHelp: "Donnez le maximum de détails pour nous permettre de préparer votre dossier"
        },
        additionalInfo: {
            label: "Informations complémentaires",
            placeholder: "Saisissez ici toute information complémentaire concernant votre demande",
            help: "Facultatif : tout contexte ou détail pouvant nous aider à mieux comprendre votre situation"
        }
    },
    stepSummary: {
        title: "Récapitulatif et soumission",
        intro: "Veuillez vérifier les informations saisies avant de soumettre votre demande.",
        sections: {
            personal: {
                title: "Informations personnelles"
            },
            emergency: {
                title: "Contacts d'urgence"
            },
            procedure: {
                title: "Procédure"
            },
            documents: {
                title: "Documents fournis"
            },
            location: {
                title: "Localisation du terrain"
            },
            editButton: "Modifier cette section"
        },
        additionalComments: {
            label: "Commentaires additionnels",
            placeholder: "Ajoutez ici toute information complémentaire concernant votre demande",
            help: "Facultatif : précisez toute information qui pourrait nous aider à mieux traiter votre demande"
        },
        finalConsent: {
            dataProtection: "En soumettant ce formulaire, vous acceptez que les informations fournies soient traitées par Charlie Oscar Consulting SARL dans le strict cadre de votre demande foncière, conformément à la législation camerounaise sur la protection des données personnelles.",
            label: "Je confirme l'exactitude des informations fournies et accepte de soumettre ma demande"
        },
        submit: {
            button: "Soumettre ma demande",
            processing: "Traitement en cours..."
        },
        confirmation: {
            title: "Demande soumise avec succès",
            message: "Votre demande a été enregistrée. Un conseiller de Charlie Oscar Consulting vous contactera dans les plus brefs délais.",
            referenceLabel: "Référence de votre demande",
            referenceHelp: "Veuillez conserver cette référence pour toute communication future concernant votre dossier.",
            downloadButton: "Télécharger le récapitulatif",
            newFormButton: "Commencer un nouveau formulaire"
        },
        personalInfo: {
            title: "Informations personnelles",
            name: "Nom & prénoms",
            gender: "Sexe",
            birthDate: "Date de naissance",
            birthPlace: "Lieu de naissance",
            nationality: "Nationalité",
            profession: "Profession"
        },
        contactInfo: {
            title: "Informations de contact",
            phone: "Téléphone",
            email: "Email",
            address: "Adresse"
        },
        locationInfo: {
            title: "Localisation du terrain",
            region: "Région",
            department: "Département",
            subdivision: "Arrondissement",
            neighborhood: "Quartier/Village",
            area: "Superficie"
        },
        procedureInfo: {
            title: "Procédure",
            status: "Statut",
            type: "Type de procédure"
        },
        documents: {
            title: "Documents fournis",
            none: "Aucun document fourni"
        }
    },
    uploadPage: {
        title: "Téléverser des documents",
        description: "Téléversez votre formulaire complété et les documents justificatifs pour votre demande foncière.",
        mainFile: "Document principal",
        mainFileDescription: "Téléversez votre formulaire complété ou document principal (PDF uniquement).",
        mainFileRequired: "Veuillez téléverser votre document principal avant de soumettre.",
        relatedFiles: "Documents justificatifs",
        relatedFilesDescription: "Téléversez tous les documents justificatifs liés à votre demande.",
        addFiles: "Ajouter des fichiers",
        dragOrClick: "Glissez-déposez les fichiers ici ou cliquez pour parcourir",
        uploadedFiles: "Fichiers téléversés",
        uploadSuccess: "Vos documents ont été téléversés avec succès. Un conseiller de Charlie Oscar Consulting vous contacteras tres prochainement.",
        uploadSuccessWithRef: "Vos documents ont été téléversés avec succès avec le numéro de référence : {ref}. Un conseiller de Charlie Oscar Consulting vous contacteras tres prochainement.",
        dropMainFile: "Déposez votre document principal ici ou cliquez pour parcourir",
        selectFile: "Sélectionnez un fichier à téléverser",
        fileName: "Nom/Description du fichier",
        fileNamePlaceholder: "Entrez un nom ou une description pour ce fichier",
        pdfPreview: "Aperçu PDF",
        confirmWithErrors: "Certains fichiers présentent des erreurs de validation. Voulez-vous continuer sans eux ?",
        validationErrors: "Certains fichiers présentent des erreurs de validation et seront exclus de la soumission.",
        mainFileValidationError: "Le document principal présente des erreurs de validation. Veuillez les corriger avant de soumettre."
    }
};
const __TURBOPACK__default__export__ = fr;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/translations/en.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// English translations
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
const en = {
    fill: "Thank you for carefully filling out this form. An expert from Charlie Oscar Consulting will contact you as soon as possible to assist you with your project.",
    common: {
        next: "Continue",
        previous: "Previous",
        submit: "Submit my request",
        processing: "Processing...",
        required: "*",
        optional: "Optional",
        fileSize: "Maximum size is 2 MB",
        filePreview: "Preview:",
        fileSelected: "File selected:",
        fileFormats: ".pdf, .jpg, .png, max 2 MB per file",
        notProvided: "Not provided",
        notSpecified: "Not specified",
        uploading: "Uploading...",
        uploaded: "Uploaded",
        uploadFailed: "Upload failed",
        retry: "Retry",
        success: "Your request has been submitted successfully. A Charlie Oscar Consulting advisor will contact you as soon as possible.",
        online: "Online",
        offline: "Offline",
        connectionLost: "Internet connection lost. Some features may be unavailable.",
        connectionRestored: "Internet connection restored.",
        fileUploadOffline: "You cannot upload files when you are offline.",
        waitForUploads: "Please wait for uploads to complete...",
        form: "Form",
        apiDocs: "API Docs",
        testTools: "Test Tools",
        dropFilesHere: "Drop your files here or click to browse",
        maxFileSize: "Max file size: 2 MB",
        fileTooLarge: "File is too large. Maximum size is 2 MB",
        invalidFileType: "File type not supported",
        fileNameTooShort: "File name must be at least 3 characters",
        cancel: "Cancel"
    },
    validation: {
        required: "This field is required",
        minLength: "Must be at least {min} characters",
        email: "Please enter a valid email address",
        phone: "Please enter a valid phone number",
        consent: "You must consent to proceed",
        date: "Please enter a valid date",
        selection: "Please make a selection",
        fileRequired: "Please upload a file",
        fileSize: "File size must be less than {size}",
        fileType: "File type not supported"
    },
    sidebar: {
        title: "Charlie Oscar",
        subtitle: "Consulting",
        step: "Step",
        of: "/",
        currentStep: "Current step"
    },
    steps: {
        personal: {
            name: "Personal Information",
            description: "Enter your personal information to help us get to know you better."
        },
        emergency: {
            name: "Emergency Contacts and Procedure",
            description: "Provide your emergency contacts and desired procedure."
        },
        documents: {
            name: "Documents and Land Location",
            description: "Provide documents and information about the land location."
        },
        summary: {
            name: "Summary and Confirmation",
            description: "Verify all information before submitting your request."
        }
    },
    stepPersonal: {
        title: "Your Personal Information",
        intro: "Please fill in all required fields marked with an asterisk (*) accurately.",
        dataProtection: {
            title: "Data Protection",
            description: "The information collected is strictly confidential and used only for processing your land request by Charlie Oscar Consulting SARL.",
            consent: "I agree that my information will be processed by Charlie Oscar Consulting SARL"
        },
        personalInfo: {
            title: "Personal Information",
            fullName: {
                label: "Full Name",
                placeholder: "Ex: MBARGA Paul Serge"
            },
            gender: {
                label: "Gender",
                male: "Male",
                female: "Female"
            },
            birthDate: {
                label: "Date of Birth"
            },
            birthPlace: {
                label: "Place of Birth",
                placeholder: "Ex: Yaoundé, Cameroon"
            },
            nationality: {
                label: "Nationality",
                placeholder: "Select your nationality",
                options: {
                    cameroon: "Cameroonian",
                    france: "French",
                    senegal: "Senegalese",
                    ivoryCoast: "Ivorian",
                    gabon: "Gabonese",
                    congo: "Congolese",
                    other: "Other"
                }
            },
            profession: {
                label: "Profession",
                placeholder: "Ex: Teacher, Merchant"
            }
        },
        contactInfo: {
            title: "Contact Information",
            primaryPhone: {
                label: "Primary Phone",
                placeholder: "Ex: +237 6XXXXXXXX"
            },
            secondaryPhone: {
                label: "Secondary Phone",
                placeholder: "Ex: +237 6XXXXXXXX"
            },
            email: {
                label: "Email",
                placeholder: "<EMAIL>"
            },
            address: {
                label: "Current Address",
                placeholder: "Ex: Bastos District, Street 1.890, Yaoundé"
            },
            idDocument: {
                label: "ID Document",
                description: "Upload a clear and legible copy of your ID card or passport",
                notUploaded: "No document uploaded",
                nameRequired: "Please enter your full name first"
            }
        }
    },
    stepDocumentsLocation: {
        title: "Documents and Land Location",
        documents: {
            title: "Documents",
            intro: "Please indicate and upload the documents you already have regarding this procedure. The location plan is highly recommended if available.",
            availableDocs: {
                label: "Available Documents",
                help: "Check all documents you can provide",
                options: {
                    customaryProperty: "Customary Property Certificate",
                    saleAct: "Sale/Donation Deed",
                    occupationPermit: "Occupation Permit",
                    locationPlan: "Location Plan / Land Sketch",
                    landTitle: "Land Title",
                    otherDocs: "Other Documents"
                }
            },
            upload: {
                label: "Document Upload",
                documentLabel: "Document",
                description: "Upload your most relevant documents for your request once at the time. You can provide additional documents later."
            },
            details: {
                label: "Document Details",
                placeholder: "Ex: The land title dates from 2015, the notarized deed of sale is attached...",
                help: "Optional: specify the nature or status of the documents provided"
            }
        },
        location: {
            title: "Location",
            intro: "Please provide precise information regarding the location of the land concerned by the procedure.",
            zoneType: {
                label: "Zone Type",
                urban: "Urban",
                rural: "Rural",
                help: "Select the type of environment where the land is located"
            },
            region: {
                label: "Region",
                placeholder: "Select a region",
                options: [
                    "Centre",
                    "Littoral",
                    "South",
                    "East",
                    "West",
                    "North-West",
                    "South-West",
                    "North",
                    "Far North",
                    "Adamawa"
                ]
            },
            department: {
                label: "Department",
                placeholder: "Select or specify a department",
                options: {
                    centre: [
                        "Mfoundi",
                        "Nyong-et-Kellé",
                        "Nyong-et-So'o",
                        "Lekié",
                        "Mbam-et-Inoubou"
                    ],
                    littoral: [
                        "Wouri",
                        "Sanaga-Maritime",
                        "Nkam",
                        "Moungo"
                    ],
                    south: [
                        "Océan",
                        "Vallée-du-Ntem",
                        "Mvila",
                        "Dja-et-Lobo"
                    ],
                    east: [
                        "Haut-Nyong",
                        "Kadey",
                        "Lom-et-Djérem",
                        "Boumba-et-Ngoko"
                    ],
                    west: [
                        "Mifi",
                        "Menoua",
                        "Bamboutos",
                        "Haut-Nkam",
                        "Ndé",
                        "Koung-Khi"
                    ]
                }
            },
            subdivision: {
                label: "Subdivision",
                placeholder: "Ex: Yaoundé II, Douala III, Bafoussam I",
                help: "Specify the subdivision where the land is located"
            },
            neighborhood: {
                label: "Neighborhood or Village",
                placeholder: "Ex: Mokolo, Bonapriso, Banengo..."
            },
            locationDetails: {
                label: "Locality",
                placeholder: "Ex: Near Saint-Jean Church, behind the central market...",
                help: "Specific geographical landmark facilitating the location of the land"
            },
            area: {
                label: "Estimated Area",
                placeholder: "Ex: 500",
                unit: "m²",
                help: "Approximate land area in square meters"
            }
        }
    },
    stepEmergencyProcedure: {
        title: "Procedure and Urgency",
        emergencyContacts: {
            title: "Emergency Contacts",
            intro: "Please indicate at least one person to contact in case of unavailability.",
            contact1: {
                title: "Person 1 (required)",
                name: {
                    label: "Full Name",
                    placeholder: "Ex: ATANGANA Marie Claire"
                },
                phone: {
                    label: "Phone",
                    placeholder: "Ex: +237 6XXXXXXXX"
                },
                relation: {
                    label: "Relationship to applicant",
                    placeholder: "Ex: Brother, Spouse, Colleague",
                    help: "Specify your relationship with this person"
                }
            },
            contact2: {
                title: "Person 2 (optional)",
                name: {
                    label: "Full Name",
                    placeholder: "Ex: ESSOMBA Jean"
                },
                phone: {
                    label: "Phone",
                    placeholder: "Ex: +237 6XXXXXXXX"
                },
                relation: {
                    label: "Relationship to applicant",
                    placeholder: "Ex: Sister, Parent, Friend"
                }
            }
        },
        landStatus: {
            title: "Land Status",
            intro: "Please specify your status in relation to the land concerned by the procedure.",
            label: "Your Status",
            options: {
                owner: "Owner",
                heir: "Heir",
                buyer: "Buyer",
                applicant: "Allocation Applicant",
                other: "Other"
            },
            otherLabel: "Status Details",
            otherPlaceholder: "Please specify your status"
        },
        procedureType: {
            title: "Procedure Type",
            intro: "Please indicate the type of land procedure you wish to undertake.",
            label: "Desired Procedure Type",
            help: "This selection will determine the documents and procedures needed for your file",
            options: [
                "Special Ministerial Derogation",
                "Direct Registration",
                "Registration by Concession",
                "Amicable Decision",
                "Goodwill Protocol Agreement",
                "Land Division",
                "Total Transfer",
                "Transfer by Death",
                "Protocol for Purchasing Unregistered Land",
                "Technical File",
                "Compensation",
                "Retrocession",
                "Administrative Appeal",
                "Land Title Rehabilitation",
                "Other"
            ],
            otherLabel: "Procedure Details",
            otherPlaceholder: "Please specify the exact nature of your request",
            otherHelp: "Provide as much detail as possible to help us prepare your file"
        },
        additionalInfo: {
            label: "Additional Information",
            placeholder: "Enter any additional information regarding your request here",
            help: "Optional: any context or details that can help us better understand your situation"
        }
    },
    stepSummary: {
        title: "Summary and Submission",
        intro: "Please verify the information entered before submitting your request.",
        sections: {
            personal: {
                title: "Personal Information"
            },
            emergency: {
                title: "Emergency Contacts"
            },
            procedure: {
                title: "Procedure"
            },
            documents: {
                title: "Documents Provided"
            },
            location: {
                title: "Land Location"
            },
            editButton: "Edit this section"
        },
        additionalComments: {
            label: "Additional Comments",
            placeholder: "Add any additional information regarding your request here",
            help: "Optional: provide any information that might help us better process your request"
        },
        finalConsent: {
            dataProtection: "By submitting this form, you agree that the information provided will be processed by Charlie Oscar Consulting SARL strictly for your land request, in accordance with Cameroonian legislation on personal data protection.",
            label: "I confirm the accuracy of the information provided and agree to submit my request"
        },
        submit: {
            button: "Submit my request",
            processing: "Processing..."
        },
        confirmation: {
            title: "Request Successfully Submitted",
            message: "Your request has been registered. A Charlie Oscar Consulting advisor will contact you as soon as possible.",
            referenceLabel: "Your request reference",
            referenceHelp: "Please keep this reference for any future communication regarding your file.",
            downloadButton: "Download summary",
            newFormButton: "Start a new form"
        },
        personalInfo: {
            title: "Personal Information",
            name: "Full Name",
            gender: "Gender",
            birthDate: "Date of Birth",
            birthPlace: "Place of Birth",
            nationality: "Nationality",
            profession: "Profession"
        },
        contactInfo: {
            title: "Contact Information",
            phone: "Phone",
            email: "Email",
            address: "Address"
        },
        locationInfo: {
            title: "Land Location",
            region: "Region",
            department: "Department",
            subdivision: "Subdivision",
            neighborhood: "Neighborhood/Village",
            area: "Area"
        },
        procedureInfo: {
            title: "Procedure",
            status: "Status",
            type: "Procedure Type"
        },
        documents: {
            title: "Documents Provided",
            none: "No documents provided"
        }
    },
    uploadPage: {
        title: "Upload Documents",
        description: "Upload your completed form and supporting documents for your land request.",
        mainFile: "Main Document",
        mainFileDescription: "Upload your completed form or main document (PDF only).",
        mainFileRequired: "Please upload your main document before submitting.",
        relatedFiles: "Supporting Documents",
        relatedFilesDescription: "Upload any supporting documents related to your request.",
        addFiles: "Add Files",
        dragOrClick: "Drag and drop files here or click to browse",
        uploadedFiles: "Uploaded Files",
        uploadSuccess: "Your documents have been successfully uploaded. A Charlie Oscar Consulting advisor will contact you very soon.",
        uploadSuccessWithRef: "Your documents have been successfully uploaded with reference number: {ref}. A Charlie Oscar Consulting advisor will contact you very soon.",
        dropMainFile: "Drop your main document here or click to browse",
        selectFile: "Select a file to upload",
        fileName: "File Name/Description",
        fileNamePlaceholder: "Enter a name or description for this file",
        pdfPreview: "PDF Preview",
        confirmWithErrors: "Some files have validation errors. Do you want to continue without them?",
        validationErrors: "Some files have validation errors and will be excluded from submission.",
        mainFileValidationError: "The main document has validation errors. Please fix them before submitting."
    }
};
const __TURBOPACK__default__export__ = en;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/translations/index.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__),
    "translations": (()=>translations)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$translations$2f$fr$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/translations/fr.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$translations$2f$en$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/translations/en.ts [app-client] (ecmascript)");
;
;
const translations = {
    fr: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$translations$2f$fr$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"],
    en: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$translations$2f$en$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]
};
const __TURBOPACK__default__export__ = translations;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/translations/language-context.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "LanguageProvider": (()=>LanguageProvider),
    "useLanguage": (()=>useLanguage)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.1_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.1_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$translations$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/translations/index.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature();
"use client";
;
;
const defaultLanguage = 'fr';
const LanguageContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])({
    language: defaultLanguage,
    setLanguage: ()=>{},
    t: ()=>''
});
const useLanguage = ()=>{
    _s();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(LanguageContext);
};
_s(useLanguage, "gDsCjeeItUuvgOWf1v4qoK9RF6k=");
const LanguageProvider = ({ children })=>{
    _s1();
    const [language, setLanguage] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(defaultLanguage);
    // Function to get a translation by key (using dot notation)
    const t = (key)=>{
        const keys = key.split('.');
        let value = __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$translations$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["translations"][language];
        for (const k of keys){
            if (value === undefined) return key;
            value = value[k];
        }
        return value !== undefined ? value : key;
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(LanguageContext.Provider, {
        value: {
            language,
            setLanguage,
            t
        },
        children: children
    }, void 0, false, {
        fileName: "[project]/app/translations/language-context.tsx",
        lineNumber: 39,
        columnNumber: 5
    }, this);
};
_s1(LanguageProvider, "copmaurz3zzgNEPsMkmub0B9JdE=");
_c = LanguageProvider;
var _c;
__turbopack_context__.k.register(_c, "LanguageProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/contexts/online-status-context.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "OnlineStatusProvider": (()=>OnlineStatusProvider),
    "useOnlineStatus": (()=>useOnlineStatus)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.1_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.1_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature();
"use client";
;
// Create the context with a default value
const OnlineStatusContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])({
    isOnline: true
});
function OnlineStatusProvider({ children }) {
    _s();
    // Default to true (optimistic) and update once we're on the client
    const [isOnline, setIsOnline] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "OnlineStatusProvider.useEffect": ()=>{
            // Update the online status initially
            setIsOnline(navigator.onLine);
            // Event handlers for online and offline events
            const handleOnline = {
                "OnlineStatusProvider.useEffect.handleOnline": ()=>{
                    console.log('Network status: Online');
                    setIsOnline(true);
                }
            }["OnlineStatusProvider.useEffect.handleOnline"];
            const handleOffline = {
                "OnlineStatusProvider.useEffect.handleOffline": ()=>{
                    console.log('Network status: Offline');
                    setIsOnline(false);
                }
            }["OnlineStatusProvider.useEffect.handleOffline"];
            // Add event listeners
            window.addEventListener('online', handleOnline);
            window.addEventListener('offline', handleOffline);
            // Clean up event listeners on unmount
            return ({
                "OnlineStatusProvider.useEffect": ()=>{
                    window.removeEventListener('online', handleOnline);
                    window.removeEventListener('offline', handleOffline);
                }
            })["OnlineStatusProvider.useEffect"];
        }
    }["OnlineStatusProvider.useEffect"], []);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(OnlineStatusContext.Provider, {
        value: {
            isOnline
        },
        children: children
    }, void 0, false, {
        fileName: "[project]/app/contexts/online-status-context.tsx",
        lineNumber: 45,
        columnNumber: 5
    }, this);
}
_s(OnlineStatusProvider, "mRBquyBAMh60D2Q5WI/A8/L/7j4=");
_c = OnlineStatusProvider;
function useOnlineStatus() {
    _s1();
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(OnlineStatusContext);
    if (context === undefined) {
        throw new Error("useOnlineStatus must be used within an OnlineStatusProvider");
    }
    return context;
}
_s1(useOnlineStatus, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
var _c;
__turbopack_context__.k.register(_c, "OnlineStatusProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/components/connection-status-toast.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "ConnectionStatusToast": (()=>ConnectionStatusToast)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.1_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.1_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$contexts$2f$online$2d$status$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/contexts/online-status-context.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$translations$2f$language$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/translations/language-context.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
"use client";
;
;
;
function ConnectionStatusToast() {
    _s();
    const { isOnline } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$contexts$2f$online$2d$status$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useOnlineStatus"])();
    const { t } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$translations$2f$language$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLanguage"])();
    const [showToast, setShowToast] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [previousOnlineStatus, setPreviousOnlineStatus] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ConnectionStatusToast.useEffect": ()=>{
            // Only show toast when status changes, not on initial render
            if (previousOnlineStatus !== isOnline) {
                setShowToast(true);
                const timer = setTimeout({
                    "ConnectionStatusToast.useEffect.timer": ()=>{
                        setShowToast(false);
                    }
                }["ConnectionStatusToast.useEffect.timer"], 5000); // Hide after 5 seconds
                return ({
                    "ConnectionStatusToast.useEffect": ()=>clearTimeout(timer)
                })["ConnectionStatusToast.useEffect"];
            }
            setPreviousOnlineStatus(isOnline);
        }
    }["ConnectionStatusToast.useEffect"], [
        isOnline,
        previousOnlineStatus
    ]);
    if (!showToast) return null;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `fixed bottom-4 right-4 z-50 p-4 rounded-md shadow-lg max-w-sm transition-all duration-300 ${isOnline ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"}`,
        role: "alert",
        "data-testid": "connection-status-toast",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "flex items-center",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex-shrink-0",
                    children: isOnline ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                        className: "h-5 w-5 text-green-500",
                        xmlns: "http://www.w3.org/2000/svg",
                        viewBox: "0 0 20 20",
                        fill: "currentColor",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                            fillRule: "evenodd",
                            d: "M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",
                            clipRule: "evenodd"
                        }, void 0, false, {
                            fileName: "[project]/app/components/connection-status-toast.tsx",
                            lineNumber: 46,
                            columnNumber: 15
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/app/components/connection-status-toast.tsx",
                        lineNumber: 40,
                        columnNumber: 13
                    }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                        className: "h-5 w-5 text-red-500",
                        xmlns: "http://www.w3.org/2000/svg",
                        viewBox: "0 0 20 20",
                        fill: "currentColor",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                            fillRule: "evenodd",
                            d: "M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",
                            clipRule: "evenodd"
                        }, void 0, false, {
                            fileName: "[project]/app/components/connection-status-toast.tsx",
                            lineNumber: 59,
                            columnNumber: 15
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/app/components/connection-status-toast.tsx",
                        lineNumber: 53,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/app/components/connection-status-toast.tsx",
                    lineNumber: 38,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "ml-3",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-sm font-medium",
                        children: isOnline ? t('common.connectionRestored') : t('common.connectionLost')
                    }, void 0, false, {
                        fileName: "[project]/app/components/connection-status-toast.tsx",
                        lineNumber: 68,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/app/components/connection-status-toast.tsx",
                    lineNumber: 67,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/app/components/connection-status-toast.tsx",
            lineNumber: 37,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/app/components/connection-status-toast.tsx",
        lineNumber: 30,
        columnNumber: 5
    }, this);
}
_s(ConnectionStatusToast, "WfJygzRIUXEiFHfTQbHS1w6elEo=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$contexts$2f$online$2d$status$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useOnlineStatus"],
        __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$translations$2f$language$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLanguage"]
    ];
});
_c = ConnectionStatusToast;
var _c;
__turbopack_context__.k.register(_c, "ConnectionStatusToast");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/components/toast-container.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "ToastContainer": (()=>ToastContainer)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.1_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$connection$2d$status$2d$toast$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/components/connection-status-toast.tsx [app-client] (ecmascript)");
"use client";
;
;
function ToastContainer() {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$connection$2d$status$2d$toast$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ConnectionStatusToast"], {}, void 0, false, {
        fileName: "[project]/app/components/toast-container.tsx",
        lineNumber: 7,
        columnNumber: 10
    }, this);
}
_c = ToastContainer;
var _c;
__turbopack_context__.k.register(_c, "ToastContainer");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/lib/utils.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "cn": (()=>cn)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$clsx$40$2$2e$1$2e$1$2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/clsx@2.1.1/node_modules/clsx/dist/clsx.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$tailwind$2d$merge$40$3$2e$2$2e$0$2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/tailwind-merge@3.2.0/node_modules/tailwind-merge/dist/bundle-mjs.mjs [app-client] (ecmascript)");
;
;
function cn(...inputs) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$tailwind$2d$merge$40$3$2e$2$2e$0$2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["twMerge"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$clsx$40$2$2e$1$2e$1$2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["clsx"])(inputs));
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/ui/button.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Button": (()=>Button),
    "buttonVariants": (()=>buttonVariants)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.1_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$radix$2d$ui$2b$react$2d$slot$40$1$2e$2$2e$0_$40$types$2b$react$40$19$2e$1$2e$2_react$40$19$2e$1$2e$0$2f$node_modules$2f40$radix$2d$ui$2f$react$2d$slot$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@radix-ui+react-slot@1.2.0_@types+react@19.1.2_react@19.1.0/node_modules/@radix-ui/react-slot/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$class$2d$variance$2d$authority$40$0$2e$7$2e$1$2f$node_modules$2f$class$2d$variance$2d$authority$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/class-variance-authority@0.7.1/node_modules/class-variance-authority/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/utils.ts [app-client] (ecmascript)");
;
;
;
;
const buttonVariants = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$class$2d$variance$2d$authority$40$0$2e$7$2e$1$2f$node_modules$2f$class$2d$variance$2d$authority$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cva"])("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive", {
    variants: {
        variant: {
            default: "bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",
            destructive: "bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",
            outline: "border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",
            secondary: "bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",
            ghost: "hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",
            link: "text-primary underline-offset-4 hover:underline"
        },
        size: {
            default: "h-9 px-4 py-2 has-[>svg]:px-3",
            sm: "h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",
            lg: "h-10 rounded-md px-6 has-[>svg]:px-4",
            icon: "size-9"
        }
    },
    defaultVariants: {
        variant: "default",
        size: "default"
    }
});
function Button({ className, variant, size, asChild = false, ...props }) {
    const Comp = asChild ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$radix$2d$ui$2b$react$2d$slot$40$1$2e$2$2e$0_$40$types$2b$react$40$19$2e$1$2e$2_react$40$19$2e$1$2e$0$2f$node_modules$2f40$radix$2d$ui$2f$react$2d$slot$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Slot"] : "button";
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Comp, {
        "data-slot": "button",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])(buttonVariants({
            variant,
            size,
            className
        })),
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/button.tsx",
        lineNumber: 51,
        columnNumber: 5
    }, this);
}
_c = Button;
;
var _c;
__turbopack_context__.k.register(_c, "Button");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/ui/sheet.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Sheet": (()=>Sheet),
    "SheetClose": (()=>SheetClose),
    "SheetContent": (()=>SheetContent),
    "SheetDescription": (()=>SheetDescription),
    "SheetFooter": (()=>SheetFooter),
    "SheetHeader": (()=>SheetHeader),
    "SheetOverlay": (()=>SheetOverlay),
    "SheetPortal": (()=>SheetPortal),
    "SheetTitle": (()=>SheetTitle),
    "SheetTrigger": (()=>SheetTrigger)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.1_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.1_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$radix$2d$ui$2b$react$2d$dialog$40$1$2e$1$2e$11_$40$types$2b$react$2d$dom$40$19$2e$1$2e$3_$40$types$2b$react$40$19$2e$1$2e$2_$5f40$types$2b$react$40$19$2e$1$2e$_o4zzp44p6iyyudsa5c6ktcno7y$2f$node_modules$2f40$radix$2d$ui$2f$react$2d$dialog$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@radix-ui+react-dialog@1.1.11_@types+react-dom@19.1.3_@types+react@19.1.2__@types+react@19.1._o4zzp44p6iyyudsa5c6ktcno7y/node_modules/@radix-ui/react-dialog/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$class$2d$variance$2d$authority$40$0$2e$7$2e$1$2f$node_modules$2f$class$2d$variance$2d$authority$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/class-variance-authority@0.7.1/node_modules/class-variance-authority/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$lucide$2d$react$40$0$2e$503$2e$0_react$40$19$2e$1$2e$0$2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__X$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/lucide-react@0.503.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/x.js [app-client] (ecmascript) <export default as X>");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/utils.ts [app-client] (ecmascript)");
"use client";
;
;
;
;
;
;
const Sheet = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$radix$2d$ui$2b$react$2d$dialog$40$1$2e$1$2e$11_$40$types$2b$react$2d$dom$40$19$2e$1$2e$3_$40$types$2b$react$40$19$2e$1$2e$2_$5f40$types$2b$react$40$19$2e$1$2e$_o4zzp44p6iyyudsa5c6ktcno7y$2f$node_modules$2f40$radix$2d$ui$2f$react$2d$dialog$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Root"];
const SheetTrigger = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$radix$2d$ui$2b$react$2d$dialog$40$1$2e$1$2e$11_$40$types$2b$react$2d$dom$40$19$2e$1$2e$3_$40$types$2b$react$40$19$2e$1$2e$2_$5f40$types$2b$react$40$19$2e$1$2e$_o4zzp44p6iyyudsa5c6ktcno7y$2f$node_modules$2f40$radix$2d$ui$2f$react$2d$dialog$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Trigger"];
const SheetClose = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$radix$2d$ui$2b$react$2d$dialog$40$1$2e$1$2e$11_$40$types$2b$react$2d$dom$40$19$2e$1$2e$3_$40$types$2b$react$40$19$2e$1$2e$2_$5f40$types$2b$react$40$19$2e$1$2e$_o4zzp44p6iyyudsa5c6ktcno7y$2f$node_modules$2f40$radix$2d$ui$2f$react$2d$dialog$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Close"];
const SheetPortal = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$radix$2d$ui$2b$react$2d$dialog$40$1$2e$1$2e$11_$40$types$2b$react$2d$dom$40$19$2e$1$2e$3_$40$types$2b$react$40$19$2e$1$2e$2_$5f40$types$2b$react$40$19$2e$1$2e$_o4zzp44p6iyyudsa5c6ktcno7y$2f$node_modules$2f40$radix$2d$ui$2f$react$2d$dialog$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Portal"];
const sheetVariants = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$class$2d$variance$2d$authority$40$0$2e$7$2e$1$2f$node_modules$2f$class$2d$variance$2d$authority$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cva"])("fixed z-50 gap-4 bg-background p-6 shadow-lg transition ease-in-out data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:duration-300 data-[state=open]:duration-500", {
    variants: {
        side: {
            top: "inset-x-0 top-0 border-b data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top",
            bottom: "inset-x-0 bottom-0 border-t data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom",
            left: "inset-y-0 left-0 h-full w-3/4 border-r data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left sm:max-w-sm",
            right: "inset-y-0 right-0 h-full w-3/4  border-l data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right sm:max-w-sm"
        }
    },
    defaultVariants: {
        side: "right"
    }
});
const SheetContent = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])(_c = ({ side = "right", className, children, ...props }, ref)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(SheetPortal, {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(SheetOverlay, {}, void 0, false, {
                fileName: "[project]/components/ui/sheet.tsx",
                lineNumber: 46,
                columnNumber: 5
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$radix$2d$ui$2b$react$2d$dialog$40$1$2e$1$2e$11_$40$types$2b$react$2d$dom$40$19$2e$1$2e$3_$40$types$2b$react$40$19$2e$1$2e$2_$5f40$types$2b$react$40$19$2e$1$2e$_o4zzp44p6iyyudsa5c6ktcno7y$2f$node_modules$2f40$radix$2d$ui$2f$react$2d$dialog$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Content"], {
                ref: ref,
                className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])(sheetVariants({
                    side
                }), className),
                ...props,
                children: [
                    children,
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$radix$2d$ui$2b$react$2d$dialog$40$1$2e$1$2e$11_$40$types$2b$react$2d$dom$40$19$2e$1$2e$3_$40$types$2b$react$40$19$2e$1$2e$2_$5f40$types$2b$react$40$19$2e$1$2e$_o4zzp44p6iyyudsa5c6ktcno7y$2f$node_modules$2f40$radix$2d$ui$2f$react$2d$dialog$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Close"], {
                        className: "absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-secondary",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$lucide$2d$react$40$0$2e$503$2e$0_react$40$19$2e$1$2e$0$2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__X$3e$__["X"], {
                                className: "h-4 w-4"
                            }, void 0, false, {
                                fileName: "[project]/components/ui/sheet.tsx",
                                lineNumber: 54,
                                columnNumber: 9
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "sr-only",
                                children: "Close"
                            }, void 0, false, {
                                fileName: "[project]/components/ui/sheet.tsx",
                                lineNumber: 55,
                                columnNumber: 9
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/components/ui/sheet.tsx",
                        lineNumber: 53,
                        columnNumber: 7
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/components/ui/sheet.tsx",
                lineNumber: 47,
                columnNumber: 5
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/components/ui/sheet.tsx",
        lineNumber: 45,
        columnNumber: 3
    }, this));
_c1 = SheetContent;
SheetContent.displayName = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$radix$2d$ui$2b$react$2d$dialog$40$1$2e$1$2e$11_$40$types$2b$react$2d$dom$40$19$2e$1$2e$3_$40$types$2b$react$40$19$2e$1$2e$2_$5f40$types$2b$react$40$19$2e$1$2e$_o4zzp44p6iyyudsa5c6ktcno7y$2f$node_modules$2f40$radix$2d$ui$2f$react$2d$dialog$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Content"].displayName;
const SheetOverlay = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])(({ className, ...props }, ref)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$radix$2d$ui$2b$react$2d$dialog$40$1$2e$1$2e$11_$40$types$2b$react$2d$dom$40$19$2e$1$2e$3_$40$types$2b$react$40$19$2e$1$2e$2_$5f40$types$2b$react$40$19$2e$1$2e$_o4zzp44p6iyyudsa5c6ktcno7y$2f$node_modules$2f40$radix$2d$ui$2f$react$2d$dialog$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Overlay"], {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("fixed inset-0 z-50 bg-background/80 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0", className),
        ...props,
        ref: ref
    }, void 0, false, {
        fileName: "[project]/components/ui/sheet.tsx",
        lineNumber: 66,
        columnNumber: 3
    }, this));
_c2 = SheetOverlay;
SheetOverlay.displayName = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$radix$2d$ui$2b$react$2d$dialog$40$1$2e$1$2e$11_$40$types$2b$react$2d$dom$40$19$2e$1$2e$3_$40$types$2b$react$40$19$2e$1$2e$2_$5f40$types$2b$react$40$19$2e$1$2e$_o4zzp44p6iyyudsa5c6ktcno7y$2f$node_modules$2f40$radix$2d$ui$2f$react$2d$dialog$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Overlay"].displayName;
const SheetHeader = ({ className, ...props })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("flex flex-col space-y-2 text-center sm:text-left", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/sheet.tsx",
        lineNumber: 81,
        columnNumber: 3
    }, this);
_c3 = SheetHeader;
SheetHeader.displayName = "SheetHeader";
const SheetFooter = ({ className, ...props })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/sheet.tsx",
        lineNumber: 95,
        columnNumber: 3
    }, this);
_c4 = SheetFooter;
SheetFooter.displayName = "SheetFooter";
const SheetTitle = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])(_c5 = ({ className, ...props }, ref)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$radix$2d$ui$2b$react$2d$dialog$40$1$2e$1$2e$11_$40$types$2b$react$2d$dom$40$19$2e$1$2e$3_$40$types$2b$react$40$19$2e$1$2e$2_$5f40$types$2b$react$40$19$2e$1$2e$_o4zzp44p6iyyudsa5c6ktcno7y$2f$node_modules$2f40$radix$2d$ui$2f$react$2d$dialog$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Title"], {
        ref: ref,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("text-lg font-semibold text-foreground", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/sheet.tsx",
        lineNumber: 109,
        columnNumber: 3
    }, this));
_c6 = SheetTitle;
SheetTitle.displayName = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$radix$2d$ui$2b$react$2d$dialog$40$1$2e$1$2e$11_$40$types$2b$react$2d$dom$40$19$2e$1$2e$3_$40$types$2b$react$40$19$2e$1$2e$2_$5f40$types$2b$react$40$19$2e$1$2e$_o4zzp44p6iyyudsa5c6ktcno7y$2f$node_modules$2f40$radix$2d$ui$2f$react$2d$dialog$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Title"].displayName;
const SheetDescription = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])(_c7 = ({ className, ...props }, ref)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$radix$2d$ui$2b$react$2d$dialog$40$1$2e$1$2e$11_$40$types$2b$react$2d$dom$40$19$2e$1$2e$3_$40$types$2b$react$40$19$2e$1$2e$2_$5f40$types$2b$react$40$19$2e$1$2e$_o4zzp44p6iyyudsa5c6ktcno7y$2f$node_modules$2f40$radix$2d$ui$2f$react$2d$dialog$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Description"], {
        ref: ref,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("text-sm text-muted-foreground", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/sheet.tsx",
        lineNumber: 121,
        columnNumber: 3
    }, this));
_c8 = SheetDescription;
SheetDescription.displayName = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$radix$2d$ui$2b$react$2d$dialog$40$1$2e$1$2e$11_$40$types$2b$react$2d$dom$40$19$2e$1$2e$3_$40$types$2b$react$40$19$2e$1$2e$2_$5f40$types$2b$react$40$19$2e$1$2e$_o4zzp44p6iyyudsa5c6ktcno7y$2f$node_modules$2f40$radix$2d$ui$2f$react$2d$dialog$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Description"].displayName;
;
var _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7, _c8;
__turbopack_context__.k.register(_c, "SheetContent$React.forwardRef");
__turbopack_context__.k.register(_c1, "SheetContent");
__turbopack_context__.k.register(_c2, "SheetOverlay");
__turbopack_context__.k.register(_c3, "SheetHeader");
__turbopack_context__.k.register(_c4, "SheetFooter");
__turbopack_context__.k.register(_c5, "SheetTitle$React.forwardRef");
__turbopack_context__.k.register(_c6, "SheetTitle");
__turbopack_context__.k.register(_c7, "SheetDescription$React.forwardRef");
__turbopack_context__.k.register(_c8, "SheetDescription");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/layout/header.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Header": (()=>Header)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.1_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.1_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.1_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.1_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/button.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$sheet$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/sheet.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$lucide$2d$react$40$0$2e$503$2e$0_react$40$19$2e$1$2e$0$2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$menu$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Menu$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/lucide-react@0.503.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/menu.js [app-client] (ecmascript) <export default as Menu>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$lucide$2d$react$40$0$2e$503$2e$0_react$40$19$2e$1$2e$0$2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$down$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronDown$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/lucide-react@0.503.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/chevron-down.js [app-client] (ecmascript) <export default as ChevronDown>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$lucide$2d$react$40$0$2e$503$2e$0_react$40$19$2e$1$2e$0$2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$building$2d$2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Building2$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/lucide-react@0.503.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/building-2.js [app-client] (ecmascript) <export default as Building2>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$lucide$2d$react$40$0$2e$503$2e$0_react$40$19$2e$1$2e$0$2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$house$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Home$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/lucide-react@0.503.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/house.js [app-client] (ecmascript) <export default as Home>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$lucide$2d$react$40$0$2e$503$2e$0_react$40$19$2e$1$2e$0$2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$hammer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Hammer$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/lucide-react@0.503.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/hammer.js [app-client] (ecmascript) <export default as Hammer>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$lucide$2d$react$40$0$2e$503$2e$0_react$40$19$2e$1$2e$0$2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$phone$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Phone$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/lucide-react@0.503.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/phone.js [app-client] (ecmascript) <export default as Phone>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$lucide$2d$react$40$0$2e$503$2e$0_react$40$19$2e$1$2e$0$2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$mail$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Mail$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/lucide-react@0.503.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/mail.js [app-client] (ecmascript) <export default as Mail>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$lucide$2d$react$40$0$2e$503$2e$0_react$40$19$2e$1$2e$0$2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$facebook$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Facebook$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/lucide-react@0.503.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/facebook.js [app-client] (ecmascript) <export default as Facebook>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$lucide$2d$react$40$0$2e$503$2e$0_react$40$19$2e$1$2e$0$2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$linkedin$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Linkedin$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/lucide-react@0.503.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/linkedin.js [app-client] (ecmascript) <export default as Linkedin>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$lucide$2d$react$40$0$2e$503$2e$0_react$40$19$2e$1$2e$0$2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$message$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__MessageCircle$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/lucide-react@0.503.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/message-circle.js [app-client] (ecmascript) <export default as MessageCircle>");
;
var _s = __turbopack_context__.k.signature();
"use client";
;
;
;
;
;
;
function Header() {
    _s();
    const [isScrolled, setIsScrolled] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [isMobileMenuOpen, setIsMobileMenuOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [activeDropdown, setActiveDropdown] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [searchQuery, setSearchQuery] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])("");
    const pathname = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePathname"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "Header.useEffect": ()=>{
            const handleScroll = {
                "Header.useEffect.handleScroll": ()=>{
                    setIsScrolled(window.scrollY > 100);
                }
            }["Header.useEffect.handleScroll"];
            window.addEventListener("scroll", handleScroll);
            return ({
                "Header.useEffect": ()=>window.removeEventListener("scroll", handleScroll)
            })["Header.useEffect"];
        }
    }["Header.useEffect"], []);
    const navigation = [
        {
            name: "Accueil",
            href: "/",
            current: pathname === "/"
        },
        {
            name: "Services",
            href: "#",
            current: false,
            dropdown: [
                {
                    name: "Foncier",
                    href: "/foncier",
                    description: "Sécurisation et conseil foncier",
                    icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$lucide$2d$react$40$0$2e$503$2e$0_react$40$19$2e$1$2e$0$2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$building$2d$2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Building2$3e$__["Building2"],
                    featured: true
                },
                {
                    name: "Immobilier",
                    href: "/immobilier",
                    description: "Conseil et investissement immobilier",
                    icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$lucide$2d$react$40$0$2e$503$2e$0_react$40$19$2e$1$2e$0$2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$house$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Home$3e$__["Home"]
                },
                {
                    name: "Construction",
                    href: "/construction",
                    description: "Expertise technique et suivi",
                    icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$lucide$2d$react$40$0$2e$503$2e$0_react$40$19$2e$1$2e$0$2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$hammer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Hammer$3e$__["Hammer"]
                }
            ]
        },
        // {
        //   name: "Formulaires",
        //   href: "#",
        //   current: false,
        //   dropdown: [
        //     {
        //       name: "Formulaire Foncier",
        //       href: "/formulaire",
        //       description: "Demande d'accompagnement foncier",
        //       icon: FileText,
        //       featured: true
        //     },
        //     {
        //       name: "Évaluation Immobilière",
        //       href: "/formulaire/evaluation",
        //       description: "Demande d'évaluation de bien",
        //       icon: Calculator
        //     },
        //     {
        //       name: "Consultation Générale",
        //       href: "/formulaire/consultation",
        //       description: "Demande de consultation",
        //       icon: ClipboardCheck
        //     }
        //   ]
        // },
        {
            name: "À propos",
            href: "/a-propos",
            current: pathname === "/a-propos"
        },
        // {
        //   name: "Projets",
        //   href: "/projets",
        //   current: pathname === "/projets"
        // },
        {
            name: "Blog",
            href: "/blog",
            current: pathname === "/blog"
        }
    ];
    const socialLinks = [
        {
            name: "Facebook",
            href: "https://www.facebook.com/charlieoscarconsulting",
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$lucide$2d$react$40$0$2e$503$2e$0_react$40$19$2e$1$2e$0$2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$facebook$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Facebook$3e$__["Facebook"]
        },
        {
            name: "LinkedIn",
            href: "https://www.linkedin.com/company/charlie-oscar-consulting",
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$lucide$2d$react$40$0$2e$503$2e$0_react$40$19$2e$1$2e$0$2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$linkedin$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Linkedin$3e$__["Linkedin"]
        }
    ];
    return pathname.startsWith("/m") ? null : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("header", {
        className: "fixed top-0 left-0 right-0 z-50",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: `bg-gray-900 text-white transition-all duration-500 ease-in-out ${isScrolled ? 'h-0 overflow-hidden opacity-0' : 'h-12 opacity-100'}`,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "container mx-auto px-4",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center justify-between h-12",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center space-x-6 text-sm",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("a", {
                                        href: "tel:+237682658037",
                                        className: "flex items-center space-x-2 hover:text-primary transition-colors",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$lucide$2d$react$40$0$2e$503$2e$0_react$40$19$2e$1$2e$0$2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$phone$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Phone$3e$__["Phone"], {
                                                className: "w-4 h-4"
                                            }, void 0, false, {
                                                fileName: "[project]/components/layout/header.tsx",
                                                lineNumber: 142,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                children: "+237682658037"
                                            }, void 0, false, {
                                                fileName: "[project]/components/layout/header.tsx",
                                                lineNumber: 143,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/components/layout/header.tsx",
                                        lineNumber: 141,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("a", {
                                        href: "mailto:<EMAIL>",
                                        className: "flex items-center space-x-2 hover:text-primary transition-colors",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$lucide$2d$react$40$0$2e$503$2e$0_react$40$19$2e$1$2e$0$2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$mail$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Mail$3e$__["Mail"], {
                                                className: "w-4 h-4"
                                            }, void 0, false, {
                                                fileName: "[project]/components/layout/header.tsx",
                                                lineNumber: 146,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "hidden md:inline",
                                                children: "<EMAIL>"
                                            }, void 0, false, {
                                                fileName: "[project]/components/layout/header.tsx",
                                                lineNumber: 147,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/components/layout/header.tsx",
                                        lineNumber: 145,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/components/layout/header.tsx",
                                lineNumber: 140,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center space-x-4",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex items-center space-x-2",
                                    children: [
                                        socialLinks.map((social)=>{
                                            const Icon = social.icon;
                                            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("a", {
                                                href: social.href,
                                                className: "w-8 h-8 flex items-center justify-center hover:bg-primary rounded transition-colors",
                                                "aria-label": social.name,
                                                title: `Suivez-nous sur ${social.name}`,
                                                target: "_blank",
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Icon, {
                                                    className: "w-4 h-4"
                                                }, void 0, false, {
                                                    fileName: "[project]/components/layout/header.tsx",
                                                    lineNumber: 178,
                                                    columnNumber: 23
                                                }, this)
                                            }, social.name, false, {
                                                fileName: "[project]/components/layout/header.tsx",
                                                lineNumber: 170,
                                                columnNumber: 21
                                            }, this);
                                        }),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("a", {
                                            href: "https://wa.me/+237682658037",
                                            className: "w-8 h-8 flex items-center justify-center hover:bg-primary rounded transition-colors",
                                            "aria-label": "WhatsApp",
                                            target: "_blank",
                                            title: "Contactez-nous sur WhatsApp",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$lucide$2d$react$40$0$2e$503$2e$0_react$40$19$2e$1$2e$0$2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$message$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__MessageCircle$3e$__["MessageCircle"], {
                                                className: "w-4 h-4"
                                            }, void 0, false, {
                                                fileName: "[project]/components/layout/header.tsx",
                                                lineNumber: 189,
                                                columnNumber: 19
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/components/layout/header.tsx",
                                            lineNumber: 182,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/components/layout/header.tsx",
                                    lineNumber: 166,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/components/layout/header.tsx",
                                lineNumber: 152,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/components/layout/header.tsx",
                        lineNumber: 138,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/components/layout/header.tsx",
                    lineNumber: 137,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/components/layout/header.tsx",
                lineNumber: 132,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: `bg-white/95 backdrop-blur-md shadow-lg border-b border-gray-200 transition-all duration-500 ease-in-out ${isScrolled ? 'translate-y-0' : 'translate-y-0'}`,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "container mx-auto px-4",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center justify-between h-20",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                href: "/",
                                className: "flex items-center space-x-3 group",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                                    src: "/logo.svg",
                                    alt: "Charlie Oscar Consulting Logo",
                                    className: "w-40 h-40 sm:w-52 sm:h-52  rounded-lg transition-transform duration-300 group-hover:scale-110 object-contain"
                                }, void 0, false, {
                                    fileName: "[project]/components/layout/header.tsx",
                                    lineNumber: 208,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/components/layout/header.tsx",
                                lineNumber: 207,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("nav", {
                                className: "hidden lg:flex items-center space-x-1",
                                children: navigation.map((item)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "relative",
                                        children: item.dropdown ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "relative",
                                            onMouseEnter: ()=>setActiveDropdown(item.name),
                                            onMouseLeave: ()=>setActiveDropdown(null),
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                    className: `flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 ${item.current ? 'text-primary bg-primary/10' : 'text-gray-700 hover:text-primary hover:bg-gray-50'}`,
                                                    children: [
                                                        item.name,
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$lucide$2d$react$40$0$2e$503$2e$0_react$40$19$2e$1$2e$0$2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$down$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronDown$3e$__["ChevronDown"], {
                                                            className: `ml-1 h-4 w-4 transition-transform duration-200 ${activeDropdown === item.name ? 'rotate-180' : ''}`
                                                        }, void 0, false, {
                                                            fileName: "[project]/components/layout/header.tsx",
                                                            lineNumber: 233,
                                                            columnNumber: 25
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/components/layout/header.tsx",
                                                    lineNumber: 225,
                                                    columnNumber: 23
                                                }, this),
                                                activeDropdown === item.name && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "absolute top-full left-0 mt-2 w-80 bg-white rounded-xl shadow-xl border border-gray-200 py-4 z-50",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "px-4 pb-3 border-b border-gray-100",
                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                                className: "text-sm font-semibold text-gray-900",
                                                                children: item.name === "Services" ? "Nos domaines d'expertise" : "Nos formulaires"
                                                            }, void 0, false, {
                                                                fileName: "[project]/components/layout/header.tsx",
                                                                lineNumber: 242,
                                                                columnNumber: 29
                                                            }, this)
                                                        }, void 0, false, {
                                                            fileName: "[project]/components/layout/header.tsx",
                                                            lineNumber: 241,
                                                            columnNumber: 27
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "py-2",
                                                            children: item.dropdown.map((dropdownItem)=>{
                                                                const Icon = dropdownItem.icon;
                                                                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                                    href: dropdownItem.href,
                                                                    className: `flex items-start space-x-3 px-4 py-3 hover:bg-gray-50 transition-colors duration-200 ${dropdownItem.featured ? 'bg-primary/5' : ''}`,
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                            className: `w-10 h-10 rounded-lg flex items-center justify-center ${dropdownItem.featured ? 'bg-primary/10 text-primary' : 'bg-gray-100 text-gray-600'}`,
                                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Icon, {
                                                                                className: "w-5 h-5"
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/components/layout/header.tsx",
                                                                                lineNumber: 262,
                                                                                columnNumber: 37
                                                                            }, this)
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/components/layout/header.tsx",
                                                                            lineNumber: 257,
                                                                            columnNumber: 35
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                            className: "flex-1",
                                                                            children: [
                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                                    className: `font-medium ${dropdownItem.featured ? 'text-primary' : 'text-gray-900'}`,
                                                                                    children: [
                                                                                        dropdownItem.name,
                                                                                        dropdownItem.featured && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                                            className: "ml-2 text-xs bg-primary text-white px-2 py-0.5 rounded-full",
                                                                                            children: "Principal"
                                                                                        }, void 0, false, {
                                                                                            fileName: "[project]/components/layout/header.tsx",
                                                                                            lineNumber: 270,
                                                                                            columnNumber: 41
                                                                                        }, this)
                                                                                    ]
                                                                                }, void 0, true, {
                                                                                    fileName: "[project]/components/layout/header.tsx",
                                                                                    lineNumber: 265,
                                                                                    columnNumber: 37
                                                                                }, this),
                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                                    className: "text-sm text-gray-600",
                                                                                    children: dropdownItem.description
                                                                                }, void 0, false, {
                                                                                    fileName: "[project]/components/layout/header.tsx",
                                                                                    lineNumber: 275,
                                                                                    columnNumber: 37
                                                                                }, this)
                                                                            ]
                                                                        }, void 0, true, {
                                                                            fileName: "[project]/components/layout/header.tsx",
                                                                            lineNumber: 264,
                                                                            columnNumber: 35
                                                                        }, this)
                                                                    ]
                                                                }, dropdownItem.name, true, {
                                                                    fileName: "[project]/components/layout/header.tsx",
                                                                    lineNumber: 250,
                                                                    columnNumber: 33
                                                                }, this);
                                                            })
                                                        }, void 0, false, {
                                                            fileName: "[project]/components/layout/header.tsx",
                                                            lineNumber: 246,
                                                            columnNumber: 27
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/components/layout/header.tsx",
                                                    lineNumber: 240,
                                                    columnNumber: 25
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/components/layout/header.tsx",
                                            lineNumber: 220,
                                            columnNumber: 21
                                        }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            href: item.href,
                                            className: `px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 ${item.current ? 'text-primary bg-primary/10' : 'text-gray-700 hover:text-primary hover:bg-gray-50'}`,
                                            children: item.name
                                        }, void 0, false, {
                                            fileName: "[project]/components/layout/header.tsx",
                                            lineNumber: 287,
                                            columnNumber: 21
                                        }, this)
                                    }, item.name, false, {
                                        fileName: "[project]/components/layout/header.tsx",
                                        lineNumber: 218,
                                        columnNumber: 17
                                    }, this))
                            }, void 0, false, {
                                fileName: "[project]/components/layout/header.tsx",
                                lineNumber: 216,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "hidden lg:flex items-center space-x-4",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                    asChild: true,
                                    className: "group",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                        href: "/formulaire",
                                        className: "flex items-center",
                                        children: [
                                            "Demander un accompagnement",
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$lucide$2d$react$40$0$2e$503$2e$0_react$40$19$2e$1$2e$0$2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$down$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronDown$3e$__["ChevronDown"], {
                                                className: "ml-2 h-4 w-4 rotate-[-90deg] transition-transform group-hover:translate-x-1"
                                            }, void 0, false, {
                                                fileName: "[project]/components/layout/header.tsx",
                                                lineNumber: 307,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/components/layout/header.tsx",
                                        lineNumber: 305,
                                        columnNumber: 17
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/components/layout/header.tsx",
                                    lineNumber: 304,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/components/layout/header.tsx",
                                lineNumber: 303,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$sheet$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Sheet"], {
                                open: isMobileMenuOpen,
                                onOpenChange: setIsMobileMenuOpen,
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$sheet$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SheetTrigger"], {
                                        asChild: true,
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                            variant: "ghost",
                                            size: "icon",
                                            className: "lg:hidden",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$lucide$2d$react$40$0$2e$503$2e$0_react$40$19$2e$1$2e$0$2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$menu$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Menu$3e$__["Menu"], {
                                                className: "h-6 w-6"
                                            }, void 0, false, {
                                                fileName: "[project]/components/layout/header.tsx",
                                                lineNumber: 316,
                                                columnNumber: 19
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/components/layout/header.tsx",
                                            lineNumber: 315,
                                            columnNumber: 17
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/components/layout/header.tsx",
                                        lineNumber: 314,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$sheet$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SheetContent"], {
                                        side: "right",
                                        className: "w-full sm:w-80",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex flex-col h-full",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "flex items-center justify-between pb-6 border-b border-gray-200",
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                        href: "/",
                                                        className: "flex items-center space-x-3",
                                                        onClick: ()=>setIsMobileMenuOpen(false),
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "w-32 h-24  bg-white rounded-lg flex items-center justify-center",
                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                                                                src: "/logo.svg",
                                                                alt: "Charlie Oscar Consulting Logo",
                                                                className: "w-full h-full"
                                                            }, void 0, false, {
                                                                fileName: "[project]/components/layout/header.tsx",
                                                                lineNumber: 326,
                                                                columnNumber: 25
                                                            }, this)
                                                        }, void 0, false, {
                                                            fileName: "[project]/components/layout/header.tsx",
                                                            lineNumber: 325,
                                                            columnNumber: 23
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/components/layout/header.tsx",
                                                        lineNumber: 324,
                                                        columnNumber: 21
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/components/layout/header.tsx",
                                                    lineNumber: 323,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("nav", {
                                                    className: "flex-1 py-6 space-y-2",
                                                    children: navigation.map((item)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            children: item.dropdown ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "space-y-2",
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                        className: "text-sm font-semibold text-gray-900 px-3 py-2",
                                                                        children: item.name
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/components/layout/header.tsx",
                                                                        lineNumber: 337,
                                                                        columnNumber: 29
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                        className: "space-y-1 pl-4",
                                                                        children: item.dropdown.map((dropdownItem)=>{
                                                                            const Icon = dropdownItem.icon;
                                                                            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                                                href: dropdownItem.href,
                                                                                className: "flex items-center space-x-3 px-3 py-2 text-gray-700 hover:text-primary hover:bg-gray-50 rounded-lg transition-colors",
                                                                                onClick: ()=>setIsMobileMenuOpen(false),
                                                                                children: [
                                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Icon, {
                                                                                        className: "w-5 h-5"
                                                                                    }, void 0, false, {
                                                                                        fileName: "[project]/components/layout/header.tsx",
                                                                                        lineNumber: 350,
                                                                                        columnNumber: 37
                                                                                    }, this),
                                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                                        children: dropdownItem.name
                                                                                    }, void 0, false, {
                                                                                        fileName: "[project]/components/layout/header.tsx",
                                                                                        lineNumber: 351,
                                                                                        columnNumber: 37
                                                                                    }, this),
                                                                                    dropdownItem.featured && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                                        className: "text-xs bg-primary text-white px-2 py-0.5 rounded-full",
                                                                                        children: "Principal"
                                                                                    }, void 0, false, {
                                                                                        fileName: "[project]/components/layout/header.tsx",
                                                                                        lineNumber: 353,
                                                                                        columnNumber: 39
                                                                                    }, this)
                                                                                ]
                                                                            }, dropdownItem.name, true, {
                                                                                fileName: "[project]/components/layout/header.tsx",
                                                                                lineNumber: 344,
                                                                                columnNumber: 35
                                                                            }, this);
                                                                        })
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/components/layout/header.tsx",
                                                                        lineNumber: 340,
                                                                        columnNumber: 29
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/components/layout/header.tsx",
                                                                lineNumber: 336,
                                                                columnNumber: 27
                                                            }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                                href: item.href,
                                                                className: `block px-3 py-2 text-sm font-medium rounded-lg transition-colors ${item.current ? 'text-primary bg-primary/10' : 'text-gray-700 hover:text-primary hover:bg-gray-50'}`,
                                                                onClick: ()=>setIsMobileMenuOpen(false),
                                                                children: item.name
                                                            }, void 0, false, {
                                                                fileName: "[project]/components/layout/header.tsx",
                                                                lineNumber: 363,
                                                                columnNumber: 27
                                                            }, this)
                                                        }, item.name, false, {
                                                            fileName: "[project]/components/layout/header.tsx",
                                                            lineNumber: 334,
                                                            columnNumber: 23
                                                        }, this))
                                                }, void 0, false, {
                                                    fileName: "[project]/components/layout/header.tsx",
                                                    lineNumber: 332,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "border-t border-gray-200 pt-6 space-y-4",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "space-y-3",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("a", {
                                                                    href: "tel:+237682658037",
                                                                    className: "flex items-center space-x-3 text-gray-700 hover:text-primary transition-colors",
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$lucide$2d$react$40$0$2e$503$2e$0_react$40$19$2e$1$2e$0$2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$phone$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Phone$3e$__["Phone"], {
                                                                            className: "w-5 h-5"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/components/layout/header.tsx",
                                                                            lineNumber: 396,
                                                                            columnNumber: 25
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                            children: "+237682658037"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/components/layout/header.tsx",
                                                                            lineNumber: 397,
                                                                            columnNumber: 25
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/components/layout/header.tsx",
                                                                    lineNumber: 395,
                                                                    columnNumber: 23
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("a", {
                                                                    href: "mailto:<EMAIL>",
                                                                    className: "flex items-center space-x-3 text-gray-700 hover:text-primary transition-colors",
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$lucide$2d$react$40$0$2e$503$2e$0_react$40$19$2e$1$2e$0$2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$mail$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Mail$3e$__["Mail"], {
                                                                            className: "w-5 h-5"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/components/layout/header.tsx",
                                                                            lineNumber: 400,
                                                                            columnNumber: 25
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                            className: "",
                                                                            children: "<EMAIL>"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/components/layout/header.tsx",
                                                                            lineNumber: 401,
                                                                            columnNumber: 25
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/components/layout/header.tsx",
                                                                    lineNumber: 399,
                                                                    columnNumber: 23
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/components/layout/header.tsx",
                                                            lineNumber: 394,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "flex items-center space-x-3",
                                                            children: [
                                                                socialLinks.map((social)=>{
                                                                    const Icon = social.icon;
                                                                    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("a", {
                                                                        href: social.href,
                                                                        className: "w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center hover:bg-primary hover:text-white transition-colors",
                                                                        target: "_blank",
                                                                        "aria-label": social.name,
                                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Icon, {
                                                                            className: "w-5 h-5"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/components/layout/header.tsx",
                                                                            lineNumber: 417,
                                                                            columnNumber: 29
                                                                        }, this)
                                                                    }, social.name, false, {
                                                                        fileName: "[project]/components/layout/header.tsx",
                                                                        lineNumber: 410,
                                                                        columnNumber: 27
                                                                    }, this);
                                                                }),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("a", {
                                                                    href: "https://wa.me/+237682658037",
                                                                    className: "w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center hover:bg-primary hover:text-white transition-colors",
                                                                    "aria-label": "WhatsApp",
                                                                    target: "_blank",
                                                                    title: "Contactez-nous sur WhatsApp",
                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$lucide$2d$react$40$0$2e$503$2e$0_react$40$19$2e$1$2e$0$2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$message$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__MessageCircle$3e$__["MessageCircle"], {
                                                                        className: "w-4 h-4"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/components/layout/header.tsx",
                                                                        lineNumber: 428,
                                                                        columnNumber: 25
                                                                    }, this)
                                                                }, void 0, false, {
                                                                    fileName: "[project]/components/layout/header.tsx",
                                                                    lineNumber: 421,
                                                                    columnNumber: 23
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/components/layout/header.tsx",
                                                            lineNumber: 406,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                                            asChild: true,
                                                            className: "w-full",
                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                                href: "/formulaire",
                                                                onClick: ()=>setIsMobileMenuOpen(false),
                                                                children: "Demander un accompagnement"
                                                            }, void 0, false, {
                                                                fileName: "[project]/components/layout/header.tsx",
                                                                lineNumber: 433,
                                                                columnNumber: 23
                                                            }, this)
                                                        }, void 0, false, {
                                                            fileName: "[project]/components/layout/header.tsx",
                                                            lineNumber: 432,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/components/layout/header.tsx",
                                                    lineNumber: 380,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/components/layout/header.tsx",
                                            lineNumber: 321,
                                            columnNumber: 17
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/components/layout/header.tsx",
                                        lineNumber: 320,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/components/layout/header.tsx",
                                lineNumber: 313,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/components/layout/header.tsx",
                        lineNumber: 205,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/components/layout/header.tsx",
                    lineNumber: 204,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/components/layout/header.tsx",
                lineNumber: 199,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/components/layout/header.tsx",
        lineNumber: 130,
        columnNumber: 5
    }, this);
}
_s(Header, "zHCgR/1g+MWspiab9Tn9d9HhX1c=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePathname"]
    ];
});
_c = Header;
var _c;
__turbopack_context__.k.register(_c, "Header");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/ui/input.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Input": (()=>Input)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.1_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/utils.ts [app-client] (ecmascript)");
;
;
function Input({ className, type, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
        type: type,
        "data-slot": "input",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm", "focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]", "aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/input.tsx",
        lineNumber: 7,
        columnNumber: 5
    }, this);
}
_c = Input;
;
var _c;
__turbopack_context__.k.register(_c, "Input");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/cms/data/contact.json (json)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v(JSON.parse("{\"heroSection\":{\"title\":\"Contactez-Nous\",\"subtitle\":\"Nous sommes là pour vous accompagner dans vos projets\"},\"form\":{\"fields\":[{\"name\":\"nom\",\"label\":\"Nom complet\",\"type\":\"text\",\"placeholder\":\"Votre nom complet\"},{\"name\":\"email\",\"label\":\"Email\",\"type\":\"email\",\"placeholder\":\"<EMAIL>\"},{\"name\":\"telephone\",\"label\":\"Téléphone\",\"type\":\"tel\",\"placeholder\":\"+237 6XX XXX XXX\"},{\"name\":\"sujet\",\"label\":\"Sujet\",\"type\":\"text\",\"placeholder\":\"Sujet de votre demande\"},{\"name\":\"message\",\"label\":\"Message\",\"type\":\"textarea\",\"placeholder\":\"Décrivez votre projet ou votre demande...\"}],\"submitLabel\":\"Envoyer le message\",\"successMessage\":\"Merci pour votre message ! Nous vous répondrons dans les plus brefs délais.\"},\"contactInfo\":{\"phone\":\"+237 6 82 65 80 37\",\"email\":\"<EMAIL>\",\"whatsapp\":\"+237 6 82 65 80 37\",\"locations\":[\"573 Rue Sylvani, Akwa, Douala, Cameroon\"],\"socials\":[{\"platform\":\"Facebook\",\"url\":\"https://facebook.com/charlieoscarconsulting\",\"icon\":\"facebook\"},{\"platform\":\"LinkedIn\",\"url\":\"https://linkedin.com/company/charlieoscarconsulting\",\"icon\":\"linkedin\"},{\"platform\":\"WhatsApp\",\"url\":\"https://wa.me/237682658037\",\"icon\":\"whatsapp\"}]},\"addressCard\":{\"title\":\"Adresse\",\"content\":\"573 Rue Sylvani, Akwa, Douala, Cameroon\"},\"phoneCard\":{\"title\":\"Téléphone\",\"content\":\"+237 6 82 65 80 37\"},\"emailCard\":{\"title\":\"Email\",\"content\":\"<EMAIL>\"},\"whatsappCard\":{\"title\":\"WhatsApp\",\"content\":\"+237 6 82 65 80 37\"},\"quickActions\":[{\"label\":\"Remplir le formulaire en ligne\",\"url\":\"/formulaire\"},{\"label\":\"Consulter nos services fonciers\",\"url\":\"/foncier\"}]}"));}}),
"[project]/app/cms/utils/contact.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "getContactInfo": (()=>getContactInfo)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$cms$2f$data$2f$contact$2e$json__$28$json$29$__ = __turbopack_context__.i("[project]/app/cms/data/contact.json (json)");
;
function getContactInfo() {
    return __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$cms$2f$data$2f$contact$2e$json__$28$json$29$__["default"];
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/lib/foncier-services-data.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "foncierServices": (()=>foncierServices),
    "getAllFoncierServices": (()=>getAllFoncierServices),
    "getFeaturedFoncierServices": (()=>getFeaturedFoncierServices),
    "getFoncierServiceBySlug": (()=>getFoncierServiceBySlug),
    "getFoncierServicesByCategory": (()=>getFoncierServicesByCategory)
});
const foncierServices = [
    {
        id: "1",
        slug: "derogation-speciale",
        title: "Dérogation Spéciale",
        shortDescription: "Obtenez l'autorisation ministérielle pour initier votre immatriculation directe ou votre concession domaniale",
        detailedDescription: "Une dérogation spéciale est une autorisation administrative émise par le ministre des Domaines, autorisant par dérogation l'immatriculation cadastrale ou l'octroi d'une concession domaniale dans le cadre du droit foncier. ",
        category: "Procédures spéciales",
        estimatedDuration: "3-6 mois",
        complexity: "Complexe",
        featured: true,
        prestations: [
            {
                title: "Analyse approfondie de l'éligibilité",
                description: "Évaluation de la situation de votre terrain et de votre projet pour déterminer la faisabilité et les fondements juridiques d'une dérogation."
            },
            {
                title: "Constitution et dépôt du dossier ministériel",
                description: "Préparation exhaustive de tous les documents requis et dépôt officiel auprès du Ministère des Domaines, du Cadastre et des Affaires Foncières (MINDCAF)."
            },
            {
                title: "Suivi personnalisé et relances administratives",
                description: "Coordination et suivi proactif de votre dossier auprès des différentes administrations impliquées, avec des relances régulières pour accélérer le processus."
            },
            {
                title: "Rédaction de requêtes et mémoires justificatifs",
                description: "Élaboration des arguments juridiques et des courriers officiels pour appuyer votre demande de dérogation."
            },
            {
                title: "Conseils stratégiques et juridiques",
                description: "Orientation sur les meilleures approches et solutions pour surmonter les obstacles et sécuriser l'obtention de votre dérogation."
            }
        ],
        advantages: [
            {
                title: "Légalisation de situations foncières complexes",
                description: "Permet de régulariser des projets ou des occupations de terrain qui sortent du cadre réglementaire habituel."
            },
            {
                title: "Sécurisation de vos futurs droits fonciers",
                description: "La dérogation est la première étape essentielle pour obtenir un titre foncier ou une concession sur un bien atypique."
            },
            {
                title: "Accélération des processus d'immatriculation/concession",
                description: "Notre expertise des rouages administratifs réduit les délais et les risques de blocage."
            },
            {
                title: "Conformité administrative garantie",
                description: "La procédure est menée dans le respect strict des textes de loi, évitant les litiges ultérieurs."
            }
        ],
        whenToCall: [
            "Votre projet foncier (construction, développement) ne respecte pas les normes urbanistiques ou foncières standard et nécessite une autorisation exceptionnelle.",
            "Vous souhaitez immatriculer un terrain ou obtenir une concession sur un bien présentant des spécificités juridiques ou des contraintes particulières.",
            "Vous avez besoin d'une décision ministérielle pour débloquer une situation foncière complexe ou initiée de manière non conventionnelle."
        ]
    },
    {
        id: "2",
        slug: "dossier-technique",
        title: "Dossier Technique",
        shortDescription: "Études et validation technique nécessaires pour les projets fonciers.",
        detailedDescription: "Un dossier technique est un document essentiel lors de l'acquisition d'un terrain. Il contient des informations cruciales telles que la délimitation précise du terrain, les éventuelles servitudes et le certificat de propriété de la parcelle. Ce dossier est établi par un géomètre agréé et permet de garantir l'exactitude des dimensions et des limites du terrain. ",
        category: "Études techniques",
        estimatedDuration: "2-4 mois",
        complexity: "Modéré",
        featured: false,
        prestations: [
            {
                title: "Bornage et délimitation officielle",
                description: "Mise en place ou vérification des bornes de votre terrain par un géomètre assermenté pour définir ses limites exactes."
            },
            {
                title: "Établissement du plan topographique",
                description: "Création d'un plan détaillé de votre parcelle incluant les caractéristiques du terrain et des environs."
            },
            {
                title: "Rédaction du procès-verbal de bornage",
                description: "Formalisation du document officiel attestant des limites du terrain, signé par toutes les parties concernées."
            },
            {
                title: "Obtention du certificat de propriété",
                description: "Vérification et obtention des documents prouvant la propriété et les éventuelles charges foncières."
            },
            {
                title: "Validation et enregistrement cadastral",
                description: "Dépôt et suivi du dossier auprès des services du Cadastre pour son approbation et son enregistrement."
            }
        ],
        advantages: [
            {
                title: "Sécurisation de vos acquisitions foncières",
                description: "Assure l'exactitude des dimensions et limites du terrain, évitant les surprises et les litiges post-achat."
            },
            {
                title: "Clarté des limites de propriété",
                description: "Définit sans équivoque les frontières de votre parcelle, prévenant les conflits avec les voisins."
            },
            {
                title: "Conformité légale et réglementaire",
                description: "Indispensable pour toute transaction ou projet foncier, garantissant la validité juridique de vos démarches."
            },
            {
                title: "Facilite les démarches ultérieures",
                description: "Un dossier technique à jour et validé simplifie les demandes de permis de construire, morcellement, ou vente."
            }
        ],
        whenToCall: [
            "Vous envisagez d'acheter ou de vendre un terrain et avez besoin de la certitude de ses limites et de sa superficie.",
            "Vous souhaitez construire sur votre terrain et avez besoin d'un plan précis et conforme.",
            "Vous avez des doutes ou des désaccords avec un voisin concernant les limites de votre propriété.",
            "Vous préparez un dossier pour l'obtention d'un titre foncier ou pour un morcellement."
        ]
    },
    {
        id: "3",
        slug: "immatriculation-directe",
        title: "Immatriculation Directe",
        shortDescription: "Nous facilitons l'immatriculation directe de votre terrain occupé pour sécuriser votre bien.",
        detailedDescription: "L’immatriculation directe est un processus administratif qui permet d’obtenir un titre foncier ( une reconnaissance officielle des droits sur le terrain.) pour un terrain appartenant au domaine national de première catégorie (occupé avant le 5 août 1974), ou dont l’occupation antérieure a été mise en valeur.",
        category: "Immobilier",
        estimatedDuration: "3-6 mois",
        complexity: "Modéré",
        featured: true,
        prestations: [
            {
                title: "Constitution complète du dossier de demande",
                description: "Collecte, vérification et organisation de tous les documents requis (demande timbrée, croquis, attestations d'occupation, etc.)."
            },
            {
                title: "Dépôt et suivi diligent de la procédure",
                description: "Enregistrement de votre dossier auprès des services fonciers et suivi rigoureux à chaque étape administrative."
            },
            {
                title: "Accompagnement lors de la descente de la commission consultative",
                description: "Préparation et assistance durant la visite de la commission chargée de constater l'occupation et la mise en valeur de votre terrain."
            },
            {
                title: "Gestion des publications légales et délais d'opposition",
                description: "Veille et suivi des publications officielles, et gestion des éventuelles oppositions dans les délais légaux."
            },
            {
                title: "Formalités de bornage et paiement des redevances",
                description: "Prise en charge des démarches de bornage et gestion des paiements des taxes et redevances foncières."
            }
        ],
        advantages: [
            {
                title: "Obtention d'un Titre Foncier définitif",
                description: "Votre propriété est reconnue officiellement et devient inattaquable, vous conférant des droits absolus."
            },
            {
                title: "Sécurisation juridique de votre patrimoine",
                description: "Protège votre bien contre les spoliations, les litiges et les revendications de tiers, assurant une possession paisible."
            },
            {
                title: "Valorisation de votre bien immobilier",
                description: "Un terrain titré prend de la valeur et est plus facile à vendre, à léguer ou à utiliser comme garantie."
            },
            {
                title: "Accès facilité aux services bancaires",
                description: "Le titre foncier est une garantie acceptée par les banques pour l'obtention de crédits et de financements."
            }
        ],
        whenToCall: [
            "Vous occupez un terrain non titré depuis de nombreuses années (avant le 5 août 1974) et l'avez mis en valeur de manière significative.",
            "Vous avez hérité d'un terrain dont le statut foncier n'a jamais été régularisé et vous souhaitez en devenir le propriétaire officiel.",
            "Vous désirez vendre votre terrain, mais celui-ci n'est pas encore titré et vous voulez le sécuriser pour la transaction."
        ]
    },
    {
        id: "4",
        slug: "achat-terrain-non-titre",
        title: "Achat de Terrain Non Titré",
        shortDescription: " Acquérez un terrain non immatriculé en toute sécurité.",
        detailedDescription: "L’achat d’un terrain non titré comporte des risques. Nous vérifions les antécédents fonciers, sécurisons la transaction et vous accompagnons dans les démarches administratives pour obtenir un titre foncier. ",
        category: "Transaction",
        estimatedDuration: "2-5 mois",
        complexity: "Modéré",
        featured: false,
        prestations: [
            {
                title: "Audit foncier approfondi et vérification des antécédents",
                description: "Recherche minutieuse de l'historique du terrain, des propriétaires précédents et identification des risques de litiges ou de doubles ventes."
            },
            {
                title: "Assistance à la négociation et rédaction des actes de cession",
                description: "Accompagnement dans les discussions avec le vendeur et élaboration des documents juridiques pertinents (convention de vente, abandon de droits coutumiers)."
            },
            {
                title: "Obtention des visas administratifs nécessaires",
                description: "Démarches pour l'obtention des autorisations requises, notamment pour les acquéreurs étrangers ou non-nationaux."
            },
            {
                title: "Légalisation et authentification des documents",
                description: "Validation des actes de vente auprès des autorités locales (chefferies, sous-préfectures) pour leur conférer une force probante."
            },
            {
                title: "Accompagnement vers l'immatriculation du terrain",
                description: "Prise en charge des démarches post-acquisition pour transformer le terrain non titré en un bien doté d'un titre foncier."
            }
        ],
        advantages: [
            {
                title: "Réduction drastique des risques d'arnaque",
                description: "Notre expertise vous protège des vendeurs frauduleux et des litiges coûteux après l'achat."
            },
            {
                title: "Sécurisation progressive de votre investissement",
                description: "Nous mettons en place des garanties juridiques pour protéger votre acquisition, même avant l'obtention du titre foncier."
            },
            {
                title: "Obtention d'un titre foncier à terme",
                description: "Notre accompagnement vise l'immatriculation de votre terrain, garantissant sa sécurité et sa valorisation à long terme."
            },
            {
                title: "Tranquillité d'esprit et confiance",
                description: "Vous achetez votre terrain en toute sérénité, sachant que toutes les précautions ont été prises pour protéger votre bien."
            }
        ],
        whenToCall: [
            "Vous avez trouvé un terrain à acquérir qui ne possède pas encore de titre foncier.",
            "Vous souhaitez acheter un terrain et vous voulez être sûr d'éviter les problèmes juridiques et les arnaques foncières.",
            "Vous êtes un investisseur ou un particulier cherchant à acquérir un terrain non titré et vous avez besoin d'un cadre légal sécurisé.",
            "Vous désirez un accompagnement complet, de la vérification initiale à l'obtention du titre foncier."
        ]
    },
    {
        id: "5",
        slug: "concession-domaniale",
        title: "Concession Domaniale",
        shortDescription: "Nous vous guidons pour obtenir une concession temporaire ou définitive sur un terrain du domaine public ou national de 2nde catégorie.",
        detailedDescription: "La concession domaniale au Cameroun est la procédure par laquelle l'État cède un terrain du domaine public ou national à une personne, pour un projet de développement, moyennant le respect d'un cahier des charges et le paiement de redevances foncières. Elle se déroule en deux phases : provisoire et définitive, et implique des démarches administratives auprès des services des domaines.",
        category: "Domaine public",
        estimatedDuration: "6-12 mois",
        complexity: "Complexe",
        featured: true,
        prestations: [
            {
                title: "Élaboration du dossier de concession provisoire",
                description: "Préparation minutieuse des documents requis, incluant le projet de mise en valeur détaillé et le plan de situation du terrain."
            },
            {
                title: "Suivi auprès des administrations (Préfecture, MINDCAF)",
                description: "Dépôt du dossier et suivi rigoureux auprès des services préfectoraux et du Ministère des Domaines pour l'obtention de l'arrêté de concession provisoire."
            },
            {
                title: "Accompagnement pour le constat de mise en valeur",
                description: "Préparation de votre dossier et de votre site pour la visite de la commission chargée de vérifier l'effectivité de votre projet."
            },
            {
                title: "Constitution du dossier de concession définitive",
                description: "Préparation des pièces et formalités pour la transformation de la concession provisoire en concession définitive, après validation de la mise en valeur."
            },
            {
                title: "Gestion des redevances et obligations contractuelles",
                description: "Calcul et gestion des paiements des redevances foncières et suivi du respect des clauses du cahier des charges."
            }
        ],
        advantages: [
            {
                title: "Accès à des terrains adaptés aux projets d'envergure",
                description: "Permet de développer des projets agricoles, industriels, ou d'infrastructures sur des superficies importantes du domaine national."
            },
            {
                title: "Sécurisation de l'usage du terrain sur le long terme",
                description: "La concession offre un cadre légal pour l'exploitation et la jouissance du terrain, avec la possibilité d'une immatriculation future."
            },
            {
                title: "Reconnaissance et soutien institutionnel",
                description: "L'obtention d'une concession prouve la validation et le soutien de l'État pour votre projet, facilitant d'autres démarches."
            },
            {
                title: "Possibilité de conversion en Titre Foncier",
                description: "Après la phase de mise en valeur et l'obtention de la concession définitive, le terrain peut être immatriculé à votre nom."
            }
        ],
        whenToCall: [
            "Vous avez un projet de développement (agricole, industriel, immobilier) qui nécessite une parcelle de grande taille, située sur le domaine public ou national de 2nde catégorie.",
            "Vous souhaitez obtenir une autorisation légale d'occuper et d'exploiter un terrain appartenant à l'État.",
            "Votre projet implique un engagement à mettre en valeur le terrain et à respecter un cahier des charges spécifique."
        ]
    },
    {
        id: "6",
        slug: "achat-gre-a-gre",
        title: "Achat de Gré à Gré (Lotissement Domanial)",
        shortDescription: "devenez propriétaire d'une parcelle issue du domaine privé de l'État",
        detailedDescription: "L'achat d'un terrain par 'gré à gré' dans un lotissement domanial au Cameroun implique une procédure spécifique. Il s'agit d'une vente directe entre l'État (représenté par les services du domaine) et l'acquéreur, sans passer par une adjudication publique",
        category: "Transaction",
        estimatedDuration: "3-6 mois",
        complexity: "Modéré",
        featured: false,
        prestations: [
            {
                title: "Dépôt de la demande d'acquisition",
                description: "Préparation et soumission de votre demande d'attribution de terrain par vente de gré à gré au Ministre des Domaines, sous couvert du Préfet."
            },
            {
                title: "Obtention des autorisations préfectorales et ministérielles",
                description: "Suivi actif du dossier pour obtenir les arrêtés préfectoraux et les décisions ministérielles autorisant la vente directe."
            },
            {
                title: "Gestion des frais et redevances foncières",
                description: "Prise en charge du calcul et du paiement des frais d'ouverture de dossier et des redevances dues à l'État."
            },
            {
                title: "Établissement et approbation de l'acte de vente",
                description: "Rédaction de l'acte de vente officiel entre l'État et vous, et assurance de son approbation par les autorités compétentes."
            },
            {
                title: "Finalisation de l'obtention du titre foncier",
                description: "Accompagnement jusqu'à l'enregistrement de l'acte de vente et la délivrance du titre foncier définitif à votre nom."
            }
        ],
        advantages: [
            {
                title: "Acquisition sécurisée d'une propriété de l'État",
                description: "Vous devenez propriétaire d'un terrain dont l'origine est officielle, incontestable et garantie par l'État."
            },
            {
                title: "Processus d'acquisition simplifié et direct",
                description: "Évitez les complexités et les incertitudes des adjudications publiques, en optant pour une vente directe."
            },
            {
                title: "Accès à des parcelles dans des lotissements officiels",
                description: "Bénéficiez de terrains déjà viabilisés ou intégrés dans des plans d'urbanisme, facilitant vos projets de construction."
            },
            {
                title: "Titre Foncier définitif et garanti",
                description: "La procédure aboutit à l'obtention d'un titre foncier en bonne et due forme, sécurisant pleinement votre investissement."
            }
        ],
        whenToCall: [
            "Vous souhaitez acquérir une parcelle située dans un lotissement domanial (terrain appartenant au domaine privé de l'État).",
            "Vous préférez une transaction directe et sécurisée avec l'État plutôt que de passer par des enchères publiques.",
            "Vous recherchez un terrain dont la légitimité et l'historique sont garantis par l'administration foncière.",
            "Vous avez un projet de construction ou d'investissement nécessitant un terrain officiel et facilement titrable."
        ]
    },
    {
        id: "7",
        slug: "rehabilitation-titres-fonciers",
        title: "Réhabilitation des Titres Fonciers",
        shortDescription: "Restauration de titres fonciers perdus, abîmés ou irréguliers",
        detailedDescription: "La réhabilitation des titres fonciers est un processus visant à corriger les irrégularités ou erreurs dans les titres fonciers existants, afin d'assurer la légitimité des droits de propriété.",
        category: "Procédures correctives",
        estimatedDuration: "2-6 mois",
        complexity: "Modéré",
        featured: false,
        prestations: [
            {
                title: "Analyse approfondie des irrégularités",
                description: "Identification précise des erreurs, omissions, chevauchements ou cas de duplicata de votre titre foncier."
            },
            {
                title: "Constitution du dossier de recours/rectification",
                description: "Préparation des documents justificatifs, preuves et arguments juridiques pour corriger les anomalies de votre titre."
            },
            {
                title: "Dépôt et suivi auprès du MINDCAF et des tribunaux",
                description: "Enregistrement de la demande auprès des services des Domaines, Cadastre et Affaires Foncières, et suivi actif de la procédure, y compris les démarches judiciaires si nécessaire."
            },
            {
                title: "Rédaction des requêtes et plaidoyers",
                description: "Élaboration des correspondances officielles, mémoires et argumentaires pour défendre vos droits devant les autorités compétentes."
            },
            {
                title: "Obtention du titre foncier réhabilité/corrigé",
                description: "Accompagnement jusqu'à la délivrance d'un nouveau titre foncier, débarrassé de toute irrégularité et pleinement valide."
            }
        ],
        advantages: [
            {
                title: "Sécurité et validité juridique retrouvées",
                description: "Votre titre foncier redevient un document légalement inattaquable, garantissant vos droits de propriété."
            },
            {
                title: "Prévention des litiges et fraudes",
                description: "La correction des irrégularités élimine les risques de contestation, de double vente ou de spoliation de votre bien."
            },
            {
                title: "Valorisation de votre patrimoine immobilier",
                description: "Un titre foncier sain facilite grandement la vente, l'hypothèque ou la transmission de votre propriété."
            },
            {
                title: "Tranquillité d'esprit et confiance",
                description: "Vous retrouvez la sérénité en sachant que votre propriété est légalement protégée et sans défauts."
            }
        ],
        whenToCall: [
            "Votre titre foncier comporte des erreurs matérielles (faute de frappe, mauvaise description, superficie inexacte).",
            "Vous constatez qu'un même terrain fait l'objet de plusieurs titres fonciers (duplicata).",
            "Votre titre foncier a été perdu, volé, ou est illisible/endommagé.",
            "Une décision administrative a retiré, annulé ou modifié votre titre foncier et vous souhaitez la contester."
        ]
    },
    {
        id: "12",
        slug: "mutation-par-deces",
        title: "Mutation par Décès",
        shortDescription: "Transférez à votre nom votre terrain hérité grâce à un accompagnement complet du dossier",
        detailedDescription: "La mutation par décès désigne le transfert des droits de propriété d'un bien immobilier (immeuble ou terrain) d'une personne décédée à ses héritiers. Ce processus est essentiel pour régulariser la situation foncière après un décès et permettre aux héritiers d'exercer leurs droits sur les biens. ",
        category: "Succession",
        estimatedDuration: "2-6 mois",
        complexity: "Modéré",
        featured: true,
        prestations: [
            {
                title: "Analyse complète de la succession",
                description: "Évaluation de la situation familiale, identification de tous les héritiers légaux et vérification des documents de succession (testament, état civil)."
            },
            {
                title: "Obtention des actes d'hérédité et certificats",
                description: "Assistance pour l'obtention du jugement d'hérédité, du certificat de décès, du certificat de non-opposition et autres documents notariaux requis."
            },
            {
                title: "Constitution et dépôt du dossier de mutation",
                description: "Préparation exhaustive de tous les documents nécessaires et dépôt officiel auprès du Conservateur Foncier compétent."
            },
            {
                title: "Suivi des publications légales et délais",
                description: "Vérification des publications obligatoires au bulletin foncier et gestion des délais légaux pour les éventuelles oppositions."
            },
            {
                title: "Retrait du nouveau titre foncier",
                description: "Accompagnement jusqu'à la délivrance du titre foncier mis à jour, établi au nom des héritiers ou de l'ayant droit."
            }
        ],
        advantages: [
            {
                title: "Officialisation de la propriété pour les héritiers",
                description: "Les héritiers deviennent légalement propriétaires du bien, avec un titre foncier incontestable à leur nom."
            },
            {
                title: "Sécurisation juridique de l'héritage",
                description: "Protège le bien immobilier des litiges futurs, des spoliations et des revendications infondées, assurant une transmission paisible."
            },
            {
                title: "Facilitation de la gestion et de la vente du bien",
                description: "Un titre foncier à jour simplifie toute opération future, qu'il s'agisse de vendre, de louer, de morceler ou d'hypothéquer le terrain."
            },
            {
                title: "Prévention des conflits familiaux",
                description: "Une procédure transparente et légale aide à distribuer les biens conformément à la loi, minimisant les désaccords entre cohéritiers."
            }
        ],
        whenToCall: [
            "Le propriétaire d'un terrain ou d'un immeuble est décédé et le titre foncier est toujours à son nom.",
            "Vous êtes un héritier et souhaitez formaliser légalement votre part de propriété sur le terrain hérité.",
            "Vous envisagez de vendre, de partager ou de réaliser des opérations sur un bien immobilier issu d'une succession."
        ]
    },
    {
        id: "8",
        slug: "morcellement-mutation-achat",
        title: "Morcellement & Mutation par Achat",
        shortDescription: "Divisez votre terrain en parcelles distinctes et sécurisez la vente ou l'acquisition de chacune d'elles.",
        detailedDescription: "Le morcellement implique la division d'un terrain en plusieurs lots, souvent pour la vente, tandis que la mutation par achat concerne le transfert de la propriété d'un terrain existant à un nouvel acquéreur. ",
        category: "Transaction",
        estimatedDuration: "3-7 mois",
        complexity: "Modéré",
        featured: false,
        prestations: [
            {
                title: "Étude de faisabilité et élaboration du plan de morcellement",
                description: "Analyse des contraintes urbanistiques et techniques, et conception d'un plan de division qui maximise la valeur de votre terrain."
            },
            {
                title: "Bornage et délimitation des nouvelles parcelles",
                description: "Réalisation des opérations de bornage par un géomètre assermenté pour délimiter précisément chaque nouveau lot créé."
            },
            {
                title: "Constitution et validation du dossier technique de morcellement",
                description: "Préparation et dépôt de tous les documents techniques et administratifs requis auprès des services du Cadastre."
            },
            {
                title: "Rédaction et signature de l'acte de vente notarié",
                description: "Assistance pour la rédaction de l'acte authentique de vente de chaque parcelle et suivi de son enregistrement."
            },
            {
                title: "Suivi de la mutation et obtention des nouveaux titres fonciers",
                description: "Accompagnement jusqu'à la délivrance des titres fonciers individuels pour chaque parcelle morcelée et vendue."
            }
        ],
        advantages: [
            {
                title: "Optimisation de la valeur foncière de votre bien",
                description: "Diviser un grand terrain en lots plus petits augmente souvent sa valeur marchande et facilite la vente."
            },
            {
                title: "Sécurisation totale des transactions de vente",
                description: "Chaque parcelle vendue dispose de son propre titre foncier, offrant une sécurité maximale à l'acheteur et au vendeur."
            },
            {
                title: "Gestion simplifiée des propriétés",
                description: "Les nouvelles parcelles sont clairement définies et enregistrées, facilitant leur gestion individuelle."
            },
            {
                title: "Conformité légale et urbanistique",
                description: "Toutes les étapes du morcellement et de la mutation sont réalisées dans le strict respect de la réglementation en vigueur."
            }
        ],
        whenToCall: [
            "Vous êtes propriétaire d'un grand terrain et souhaitez le diviser pour vendre des parcelles séparées.",
            "Vous avez acquis une portion d'un terrain plus grand et vous avez besoin d'un titre foncier distinct pour votre part.",
            "Vous voulez sécuriser la transaction d'une parcelle nouvellement créée issue d'une division foncière.",
            "Vous êtes un promoteur immobilier et souhaitez créer des lots constructibles à partir d'une grande parcelle."
        ]
    },
    {
        id: "9",
        slug: "retrocession",
        title: "Rétrocession",
        shortDescription: "Récupérez un terrain précédemment exproprié ou intégré dans le domaine public suite à une carence administrative ou un projet abandonné.",
        detailedDescription: "la rétrocession se réfère au droit pour les anciens propriétaires ou leurs ayants droit de demander la restitution d'un terrain exproprié pour cause d'utilité publique, si ce terrain n'est pas utilisé conformément à la destination prévue dans le délai imparti",
        category: "Procédures correctives",
        estimatedDuration: "6-18 mois",
        complexity: "Complexe",
        featured: false,
        prestations: [
            {
                title: "Analyse de la recevabilité de la demande",
                description: "Vérification des motifs d'expropriation, du non-respect de l'affectation du terrain ou de l'abandon du projet public."
            },
            {
                title: "Constitution du dossier de rétrocession",
                description: "Préparation des documents justificatifs prouvant l'expropriation et la non-utilisation du terrain conformément à l'objectif initial."
            },
            {
                title: "Dépôt et suivi des recours administratifs",
                description: "Enregistrement de la demande de rétrocession auprès des autorités compétentes et suivi rigoureux du processus."
            },
            {
                title: "Négociation et représentation",
                description: "Défense de vos intérêts et dialogue avec l'administration pour parvenir à la restitution de votre terrain."
            },
            {
                title: "Accompagnement en cas de contentieux",
                description: "Soutien juridique et assistance si la procédure nécessite une action devant les tribunaux pour faire valoir vos droits."
            }
        ],
        advantages: [
            {
                title: "Récupération de votre propriété légitime",
                description: "Vous retrouvez la pleine jouissance d'un bien foncier qui vous a été retiré ou dont l'usage n'a pas été respecté."
            },
            {
                title: "Réparation d'un préjudice foncier",
                description: "Obtenez justice et compensation pour la perte ou l'usage abusif de votre terrain par l'administration."
            },
            {
                title: "Sécurisation de vos droits fonciers",
                description: "Le terrain est officiellement réintégré dans votre patrimoine, avec toutes les garanties juridiques."
            },
            {
                title: "Rétablissement de la justice administrative",
                description: "Faire valoir vos droits face aux manquements ou abus de l'administration, renforçant la sécurité foncière."
            }
        ],
        whenToCall: [
            "Votre terrain a été exproprié pour un projet public qui n'a pas été réalisé, a été abandonné, ou n'est pas utilisé conformément à l'objectif initial.",
            "Vous estimez avoir subi un préjudice foncier suite à une décision administrative contestable ou non respectée.",
            "Vous souhaitez faire valoir votre droit à la restitution d'un bien foncier intégré au domaine public sans juste cause ou compensation."
        ]
    },
    {
        id: "10",
        slug: "bornage-reconstitution",
        title: "Bornage et Reconstitution des Bornes",
        shortDescription: "Identifiez et délimitez précisément les limites de votre terrain avec l'aide d'un géomètre assermenté.",
        detailedDescription: "le bornage et la reconstitution des bornes sont des procédures cadastrales importantes pour délimiter les propriétés foncières et éviter les conflits de voisinage. Le bornage consiste à fixer les limites d'un terrain à l'aide de bornes, tandis que la reconstitution concerne la remise en place de bornes manquantes ou endommagées.",
        category: "Délimitation",
        estimatedDuration: "1-2 mois",
        complexity: "Modéré",
        featured: false,
        prestations: [
            {
                title: "Analyse documentaire et cadastrale",
                description: "Étude des titres de propriété, plans cadastraux et autres documents pour établir les points de repère et les limites existantes."
            },
            {
                title: "Intervention d'un géomètre assermenté sur site",
                description: "Descente sur le terrain avec un expert pour identifier, mesurer et matérialiser les limites précises par la pose de bornes officielles."
            },
            {
                title: "Rédaction et homologation du procès-verbal de bornage",
                description: "Établissement du document officiel décrivant les opérations de bornage et les limites fixées, signé par toutes les parties et enregistré."
            },
            {
                title: "Gestion des notifications aux propriétaires voisins",
                description: "Information des propriétaires des parcelles contiguës et gestion des éventuelles observations ou contestations."
            },
            {
                title: "Conseils pour la prévention et la résolution des litiges",
                description: "Accompagnement pour prévenir ou résoudre à l'amiable les conflits de voisinage liés aux limites foncières."
            }
        ],
        advantages: [
            {
                title: "Délimitation claire et incontestable de votre propriété",
                description: "Élimine les incertitudes sur les frontières de votre terrain, évitant les empiètements et les malentendus."
            },
            {
                title: "Sécurité juridique de vos limites foncières",
                description: "Le procès-verbal de bornage est un document officiel qui a force probante, protégeant vos droits de propriété."
            },
            {
                title: "Prévention et résolution des conflits de voisinage",
                description: "Des limites clairement définies favorisent de bonnes relations et évitent les litiges coûteux avec les propriétés adjacentes."
            },
            {
                title: "Valorisation de votre bien immobilier",
                description: "Un terrain clairement borné est plus attractif et sécurisant pour les futurs acquéreurs ou pour tout projet de développement."
            }
        ],
        whenToCall: [
            "Vous avez des doutes sur les limites exactes de votre propriété ou de celles de vos voisins.",
            "Votre terrain n'a jamais été officiellement borné ou les bornes existantes sont manquantes, déplacées ou endommagées.",
            "Vous envisagez de construire une clôture, un mur ou de réaliser des aménagements importants en limite de propriété.",
            "Vous préparez la vente ou l'acquisition d'un terrain et vous souhaitez garantir la précision de sa superficie et de ses limites."
        ]
    },
    {
        id: "11",
        slug: "indemnisation",
        title: "Indemnisation",
        shortDescription: "Obtenez une compensation équitable en cas d'expropriation ou de préjudice foncier.",
        detailedDescription: "L'indemnisation vise à compenser les pertes subies par les individus du fait de la perte de leurs terres ou constructions. ",
        category: "Procédures correctives",
        estimatedDuration: "6-12 mois",
        complexity: "Complexe",
        featured: false,
        prestations: [
            {
                title: "Évaluation du préjudice foncier et des droits",
                description: "Analyse approfondie de la situation pour déterminer la nature et l'étendue de votre préjudice et estimer la juste compensation à réclamer."
            },
            {
                title: "Constitution et dépôt du dossier d'indemnisation",
                description: "Préparation exhaustive des documents justificatifs (titre foncier, plans, preuves de mise en valeur, etc.) pour appuyer votre demande."
            },
            {
                title: "Négociation avec l'administration ou les entités expropriatrices",
                description: "Défense active de vos intérêts et recherche d'un accord amiable sur le montant de l'indemnité."
            },
            {
                title: "Suivi devant les commissions d'évaluation et les tribunaux",
                description: "Accompagnement et représentation lors des audiences des commissions d'évaluation et, si nécessaire, devant les juridictions compétentes."
            },
            {
                title: "Assistance au paiement et à la réception de l'indemnité",
                description: "Veille au bon déroulement du processus de paiement et à la réception effective de la compensation due."
            }
        ],
        advantages: [
            {
                title: "Obtention d'une indemnisation juste et équitable",
                description: "Nous veillons à ce que la compensation versée reflète la valeur réelle de votre bien et les préjudices subis (pertes de revenus, de jouissance, etc.)."
            },
            {
                title: "Défense experte de vos droits de propriétaire",
                description: "Vous êtes accompagné par des professionnels aguerris face aux procédures d'expropriation souvent complexes et intimidantes."
            },
            {
                title: "Accélération du processus d'indemnisation",
                description: "Notre connaissance des procédures et des acteurs réduit les délais d'attente pour l'obtention de votre compensation."
            },
            {
                title: "Sécurisation juridique de vos recours",
                description: "Nous garantissons la conformité de vos démarches et la protection de vos droits face à des montants jugés insuffisants ou injustes."
            }
        ],
        whenToCall: [
            "Votre terrain, votre maison ou vos biens sont menacés d'expropriation pour cause d'utilité publique.",
            "Vous avez subi un préjudice lié à un projet public (construction d'une route, d'une infrastructure) qui affecte votre propriété.",
            "Le montant d'indemnisation proposé par l'administration est insuffisant, contestable ou ne correspond pas à la valeur de votre bien."
        ]
    },
    {
        id: "13",
        slug: "lotissement",
        title: "Lotissement",
        shortDescription: "Création et aménagement de lotissements en toute conformité",
        detailedDescription: "Le lotissement consiste en la division d’un terrain pour créer des parcelles destinées à la vente ou à l’aménagement. Il nécessite une étude de faisabilité, un plan d’implantation, des autorisations urbanistiques, et un suivi des travaux. Charlie Oscar Consulting vous accompagne de la planification à l’obtention de toutes les autorisations nécessaires.",
        category: "Aménagement",
        estimatedDuration: "6-12 mois",
        complexity: "Modéré",
        featured: false,
        prestations: [
            {
                title: "Étude de faisabilité technique et réglementaire",
                description: "Analyse approfondie du terrain, des règles d'urbanisme et des potentialités de division pour votre projet de lotissement."
            },
            {
                title: "Conception du plan de lotissement et d'aménagement",
                description: "Élaboration de plans détaillés pour la division des parcelles, la création de voies d'accès, d'espaces verts et d'infrastructures."
            },
            {
                title: "Obtention des autorisations urbanistiques (permis de lotir)",
                description: "Dépôt et suivi des demandes de permis et autorisations nécessaires auprès des services d'urbanisme."
            },
            {
                title: "Coordination des travaux d'aménagement",
                description: "Accompagnement dans la supervision des travaux de viabilisation (voies, eau, électricité) conformément au plan approuvé."
            },
            {
                title: "Suivi jusqu'à la réception définitive du lotissement",
                description: "Assistance pour les démarches finales, y compris la réception des travaux et l'intégration au domaine public des équipements communs."
            }
        ],
        advantages: [
            {
                title: "Création de parcelles valorisables et attractives",
                description: "Divisez votre terrain en lots optimisés pour la vente, augmentant significativement leur valeur marchande."
            },
            {
                title: "Conformité totale avec les normes urbanistiques",
                description: "Votre lotissement est réalisé dans le respect strict des réglementations, évitant tout risque de blocage ou de sanction."
            },
            {
                title: "Sécurité juridique pour les futurs acquéreurs",
                description: "Les lots sont créés avec toutes les garanties légales, facilitant leur vente et l'obtention de titres fonciers individuels."
            },
            {
                title: "Gestion simplifiée de projets complexes",
                description: "Un accompagnement expert qui prend en charge toutes les étapes, de la conception à la commercialisation des lots."
            }
        ],
        whenToCall: [
            "Vous possédez un grand terrain et souhaitez le diviser en plusieurs parcelles constructibles pour les vendre.",
            "Vous êtes un promoteur immobilier et avez un projet de développement résidentiel ou commercial nécessitant la création d'un lotissement.",
            "Vous avez besoin d'une assistance complète pour les études, les plans, les autorisations et le suivi des travaux d'aménagement d'un terrain."
        ]
    },
    {
        id: "15",
        slug: "recours-gracieux",
        title: "Recours Gracieux",
        shortDescription: "Demande de réexamen administratif pour vos contentieux fonciers",
        detailedDescription: "Un recours gracieux est une procédure administrative par laquelle une personne demande à l'administration de reconsidérer une décision qu'elle a prise. C'est une étape préalable obligatoire avant de saisir le juge administratif dans de nombreux cas. Le recours gracieux est un outil de droit public qui permet de résoudre amiablement des litiges administratifs. ",
        category: "Procédures correctives",
        estimatedDuration: "3-6 mois",
        complexity: "Modéré",
        featured: false,
        prestations: [
            {
                title: "Analyse approfondie de la décision contestée",
                description: "Évaluation de la légalité et du bien-fondé de la décision administrative que vous souhaitez contester."
            },
            {
                title: "Rédaction et formalisation de la requête gracieuse",
                description: "Élaboration d'un recours détaillé et argumenté, incluant toutes les pièces justificatives et les arguments juridiques pertinents."
            },
            {
                title: "Dépôt et suivi proactif auprès du Ministère compétent",
                description: "Enregistrement de votre recours au MINDCAF et suivi régulier auprès de la cellule contentieux pour accélérer son traitement."
            },
            {
                title: "Négociation et échange avec l'administration",
                description: "Dialogue avec les services concernés pour trouver une solution amiable et obtenir la révision de la décision."
            },
            {
                title: "Préparation à un éventuel recours contentieux",
                description: "Si le recours gracieux n'aboutit pas, nous préparons les bases pour une action devant le juge administratif."
            }
        ],
        advantages: [
            {
                title: "Solution amiable et rapide",
                description: "Permet de résoudre un litige avec l'administration sans passer par une procédure judiciaire longue et coûteuse."
            },
            {
                title: "Préservation de vos droits fonciers",
                description: "Conteste une décision administrative qui affecte votre titre foncier ou vos droits sur un bien immobilier."
            },
            {
                title: "Expertise des procédures administratives",
                description: "Nous maîtrisons les exigences formelles et les circuits administratifs pour optimiser vos chances de succès."
            },
            {
                title: "Préparation à des actions plus poussées",
                description: "Même en cas d'échec, le recours gracieux est une étape obligatoire qui renforce votre position pour un recours contentieux."
            }
        ],
        whenToCall: [
            "Un titre foncier vous a été retiré, annulé ou modifié par l'administration sans motif valable.",
            "Votre demande d'immatriculation, de concession ou toute autre démarche foncière a été refusée de manière contestable.",
            "Vous souhaitez contester une décision administrative relative à un bien foncier sans engager immédiatement une procédure judiciaire.",
            "Vous avez subi un préjudice suite à un acte administratif illégal et vous cherchez une réparation."
        ]
    }
];
function getFoncierServiceBySlug(slug) {
    return foncierServices.find((service)=>service.slug === slug);
}
function getAllFoncierServices() {
    return foncierServices;
}
function getFeaturedFoncierServices() {
    return foncierServices.filter((service)=>service.featured);
}
function getFoncierServicesByCategory(category) {
    return foncierServices.filter((service)=>service.category === category);
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/layout/footer.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Footer": (()=>Footer)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.1_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.1_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/button.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/input.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$lucide$2d$react$40$0$2e$503$2e$0_react$40$19$2e$1$2e$0$2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$building$2d$2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Building2$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/lucide-react@0.503.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/building-2.js [app-client] (ecmascript) <export default as Building2>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$lucide$2d$react$40$0$2e$503$2e$0_react$40$19$2e$1$2e$0$2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$house$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Home$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/lucide-react@0.503.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/house.js [app-client] (ecmascript) <export default as Home>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$lucide$2d$react$40$0$2e$503$2e$0_react$40$19$2e$1$2e$0$2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$hammer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Hammer$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/lucide-react@0.503.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/hammer.js [app-client] (ecmascript) <export default as Hammer>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$lucide$2d$react$40$0$2e$503$2e$0_react$40$19$2e$1$2e$0$2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$phone$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Phone$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/lucide-react@0.503.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/phone.js [app-client] (ecmascript) <export default as Phone>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$lucide$2d$react$40$0$2e$503$2e$0_react$40$19$2e$1$2e$0$2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$mail$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Mail$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/lucide-react@0.503.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/mail.js [app-client] (ecmascript) <export default as Mail>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$lucide$2d$react$40$0$2e$503$2e$0_react$40$19$2e$1$2e$0$2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$map$2d$pin$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__MapPin$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/lucide-react@0.503.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/map-pin.js [app-client] (ecmascript) <export default as MapPin>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$lucide$2d$react$40$0$2e$503$2e$0_react$40$19$2e$1$2e$0$2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$facebook$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Facebook$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/lucide-react@0.503.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/facebook.js [app-client] (ecmascript) <export default as Facebook>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$lucide$2d$react$40$0$2e$503$2e$0_react$40$19$2e$1$2e$0$2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$twitter$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Twitter$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/lucide-react@0.503.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/twitter.js [app-client] (ecmascript) <export default as Twitter>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$lucide$2d$react$40$0$2e$503$2e$0_react$40$19$2e$1$2e$0$2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$linkedin$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Linkedin$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/lucide-react@0.503.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/linkedin.js [app-client] (ecmascript) <export default as Linkedin>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$lucide$2d$react$40$0$2e$503$2e$0_react$40$19$2e$1$2e$0$2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$instagram$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Instagram$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/lucide-react@0.503.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/instagram.js [app-client] (ecmascript) <export default as Instagram>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$lucide$2d$react$40$0$2e$503$2e$0_react$40$19$2e$1$2e$0$2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$arrow$2d$right$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ArrowRight$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/lucide-react@0.503.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/arrow-right.js [app-client] (ecmascript) <export default as ArrowRight>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$lucide$2d$react$40$0$2e$503$2e$0_react$40$19$2e$1$2e$0$2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$message$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__MessageCircle$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/lucide-react@0.503.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/message-circle.js [app-client] (ecmascript) <export default as MessageCircle>");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$cms$2f$utils$2f$contact$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/cms/utils/contact.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$foncier$2d$services$2d$data$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/foncier-services-data.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.1_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/navigation.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
"use client";
;
;
;
;
;
;
;
function Footer() {
    _s();
    const currentYear = new Date().getFullYear();
    const contact = (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$cms$2f$utils$2f$contact$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getContactInfo"])();
    const contactInfo = contact.contactInfo || {};
    const pathname = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePathname"])();
    // Socials from CMS (array of { platform, url, icon })
    const socialLinks = Array.isArray(contactInfo.socials) ? contactInfo.socials.map((s)=>({
            name: s.platform,
            href: s.url,
            icon: s.icon
        })) : [];
    const foncierServices = (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$foncier$2d$services$2d$data$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getAllFoncierServices"])();
    const last10FoncierServices = foncierServices.slice(-12).reverse();
    const services = [
        {
            name: "Foncier",
            href: "/foncier",
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$lucide$2d$react$40$0$2e$503$2e$0_react$40$19$2e$1$2e$0$2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$building$2d$2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Building2$3e$__["Building2"],
            items: [
                ...last10FoncierServices.map((service)=>({
                        label: service.title,
                        href: `/foncier/services/${service.slug}`
                    }))
            ]
        },
        {
            name: "Immobilier",
            href: "/immobilier",
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$lucide$2d$react$40$0$2e$503$2e$0_react$40$19$2e$1$2e$0$2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$house$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Home$3e$__["Home"],
            items: [
                {
                    href: "/immobilier/services/#achat-vente-terrains",
                    label: "Achat & Vente de Terrains",
                    description: "Sécurisez vos transactions foncières avec un accompagnement juridique complet et une validation notariée."
                },
                {
                    href: "/immobilier/services/#achat-vente-biens",
                    label: "Achat & Vente de Biens",
                    description: "Trouvez ou vendez votre maison ou appartement avec une expertise qui optimise votre investissement."
                },
                {
                    href: "/immobilier/services/#location-bureaux",
                    label: "Location de Bureaux",
                    description: "Des solutions clés en main pour vos espaces professionnels, de la sélection à la négociation de bail."
                },
                {
                    href: "/immobilier/services/#location-residentielle",
                    label: "Location Résidentielle",
                    description: "Mettez ou trouvez des maisons et appartements (meublés ou non) avec un processus simple et sécurisé."
                },
                {
                    href: "/immobilier/services/#location-terrains",
                    label: "Location de Terrains",
                    description: "Facilitez la location de terrains pour divers usages, avec une conformité légale assurée."
                },
                {
                    href: "/immobilier/services/#conseil-expertise-juridique",
                    label: "Conseil & Expertise Juridique",
                    description: "Bénéficiez de conseils de nos juristes expérimentés pour toutes vos questions juridiques immobilières."
                }
            ]
        },
        {
            name: "Construction",
            href: "/construction",
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$lucide$2d$react$40$0$2e$503$2e$0_react$40$19$2e$1$2e$0$2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$hammer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Hammer$3e$__["Hammer"],
            items: [
                {
                    href: "/construction/services/#etudes-preliminaires",
                    label: "Études Préliminaires",
                    description: "Analyse complète de faisabilité et études techniques préalables"
                },
                {
                    href: "/construction/services/#conception-architecturale",
                    label: "Conception Architecturale",
                    description: "Plans détaillés et design architectural sur mesure"
                },
                {
                    href: "/construction/services/#ingenierie",
                    label: "Ingénierie Structure",
                    description: "Calculs de structure et solutions techniques avancées"
                },
                {
                    href: "/construction/services/#gestion-projet",
                    label: "Gestion de Projet",
                    description: "Coordination complète et suivi de vos projets de construction"
                },
                {
                    href: "/construction/services/#smart-building",
                    label: "Smart Building",
                    description: "Technologies intelligentes et solutions connectées"
                }
            ]
        }
    ];
    const quickLinks = [
        {
            name: "À propos",
            href: "/a-propos"
        },
        // { name: "Nos projets", href: "/projets" },
        {
            name: "Blog",
            href: "/blog"
        }
    ];
    const legalLinks = [
        {
            name: "Mentions légales",
            href: "/mentions-legales"
        },
        {
            name: "Politique de confidentialité",
            href: "/confidentialite"
        },
        {
            name: "Conditions d'utilisation",
            href: "/conditions"
        },
        {
            name: "Plan du site",
            href: "/site-map"
        }
    ];
    return pathname.startsWith("/m") ? null : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("footer", {
        className: "bg-gray-900 text-white",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "border-b border-gray-800",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "container mx-auto px-4 py-12",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "max-w-4xl mx-auto text-center space-y-6",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                className: "text-2xl font-bold",
                                children: "Restez informé de nos actualités"
                            }, void 0, false, {
                                fileName: "[project]/components/layout/footer.tsx",
                                lineNumber: 146,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-gray-400 max-w-2xl mx-auto",
                                children: "Recevez nos derniers articles, conseils d'experts et actualités du secteur foncier et immobilier camerounais."
                            }, void 0, false, {
                                fileName: "[project]/components/layout/footer.tsx",
                                lineNumber: 149,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex flex-col sm:flex-row gap-4 max-w-md mx-auto",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Input"], {
                                        type: "email",
                                        placeholder: "Votre adresse email",
                                        className: "bg-gray-800 border-gray-700 text-white placeholder:text-gray-400 focus:border-primary"
                                    }, void 0, false, {
                                        fileName: "[project]/components/layout/footer.tsx",
                                        lineNumber: 154,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                        className: "group",
                                        children: [
                                            "S'abonner",
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$lucide$2d$react$40$0$2e$503$2e$0_react$40$19$2e$1$2e$0$2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$arrow$2d$right$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ArrowRight$3e$__["ArrowRight"], {
                                                className: "ml-2 h-4 w-4 transition-transform group-hover:translate-x-1"
                                            }, void 0, false, {
                                                fileName: "[project]/components/layout/footer.tsx",
                                                lineNumber: 161,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/components/layout/footer.tsx",
                                        lineNumber: 159,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/components/layout/footer.tsx",
                                lineNumber: 153,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-xs text-gray-500",
                                children: "Pas de spam, désabonnement possible à tout moment."
                            }, void 0, false, {
                                fileName: "[project]/components/layout/footer.tsx",
                                lineNumber: 164,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/components/layout/footer.tsx",
                        lineNumber: 145,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/components/layout/footer.tsx",
                    lineNumber: 144,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/components/layout/footer.tsx",
                lineNumber: 143,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "container mx-auto px-4 py-16",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "grid lg:grid-cols-4 gap-12",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "lg:col-span-1 space-y-6",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                    href: "/",
                                    className: "flex items-center space-x-3",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                                        src: "/logo_2.svg",
                                        alt: "Charlie Oscar Consulting Secondary Logo",
                                        className: "w-40 h-40 sm:w-54 sm:h-54 md:w-60 md:h-60 rounded-lg transition-transform duration-300 group-hover:scale-110 object-contain"
                                    }, void 0, false, {
                                        fileName: "[project]/components/layout/footer.tsx",
                                        lineNumber: 177,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/components/layout/footer.tsx",
                                    lineNumber: 176,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "text-gray-400 leading-relaxed",
                                    children: "Votre partenaire de confiance pour tous vos projets fonciers, immobiliers et de construction au Cameroun."
                                }, void 0, false, {
                                    fileName: "[project]/components/layout/footer.tsx",
                                    lineNumber: 183,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "space-y-3",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex items-center space-x-3 text-gray-400",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$lucide$2d$react$40$0$2e$503$2e$0_react$40$19$2e$1$2e$0$2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$map$2d$pin$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__MapPin$3e$__["MapPin"], {
                                                    className: "w-5 h-5 text-primary flex-shrink-0"
                                                }, void 0, false, {
                                                    fileName: "[project]/components/layout/footer.tsx",
                                                    lineNumber: 191,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    children: contactInfo.locations?.[0] || 'Adresse non renseignée'
                                                }, void 0, false, {
                                                    fileName: "[project]/components/layout/footer.tsx",
                                                    lineNumber: 192,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/components/layout/footer.tsx",
                                            lineNumber: 190,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex items-center space-x-3 text-gray-400",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$lucide$2d$react$40$0$2e$503$2e$0_react$40$19$2e$1$2e$0$2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$phone$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Phone$3e$__["Phone"], {
                                                    className: "w-5 h-5 text-primary flex-shrink-0"
                                                }, void 0, false, {
                                                    fileName: "[project]/components/layout/footer.tsx",
                                                    lineNumber: 195,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    children: contactInfo.phone || 'Téléphone non renseigné'
                                                }, void 0, false, {
                                                    fileName: "[project]/components/layout/footer.tsx",
                                                    lineNumber: 196,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/components/layout/footer.tsx",
                                            lineNumber: 194,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex items-center space-x-3 text-gray-400",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$lucide$2d$react$40$0$2e$503$2e$0_react$40$19$2e$1$2e$0$2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$mail$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Mail$3e$__["Mail"], {
                                                    className: "w-5 h-5 text-primary flex-shrink-0"
                                                }, void 0, false, {
                                                    fileName: "[project]/components/layout/footer.tsx",
                                                    lineNumber: 199,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    children: contactInfo.email || 'Email non renseigné'
                                                }, void 0, false, {
                                                    fileName: "[project]/components/layout/footer.tsx",
                                                    lineNumber: 200,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/components/layout/footer.tsx",
                                            lineNumber: 198,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex items-center space-x-3 text-gray-400",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$lucide$2d$react$40$0$2e$503$2e$0_react$40$19$2e$1$2e$0$2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$message$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__MessageCircle$3e$__["MessageCircle"], {
                                                    className: "w-5 h-5 text-primary flex-shrink-0"
                                                }, void 0, false, {
                                                    fileName: "[project]/components/layout/footer.tsx",
                                                    lineNumber: 203,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    children: contactInfo.whatsapp || 'WhatsApp non renseigné'
                                                }, void 0, false, {
                                                    fileName: "[project]/components/layout/footer.tsx",
                                                    lineNumber: 204,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/components/layout/footer.tsx",
                                            lineNumber: 202,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/components/layout/footer.tsx",
                                    lineNumber: 189,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex space-x-4",
                                    children: socialLinks.map((social, idx)=>{
                                        // Use Lucide icons if available, fallback to <i> if not
                                        const LucideIcon = {
                                            facebook: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$lucide$2d$react$40$0$2e$503$2e$0_react$40$19$2e$1$2e$0$2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$facebook$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Facebook$3e$__["Facebook"],
                                            twitter: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$lucide$2d$react$40$0$2e$503$2e$0_react$40$19$2e$1$2e$0$2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$twitter$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Twitter$3e$__["Twitter"],
                                            linkedin: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$lucide$2d$react$40$0$2e$503$2e$0_react$40$19$2e$1$2e$0$2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$linkedin$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Linkedin$3e$__["Linkedin"],
                                            instagram: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$lucide$2d$react$40$0$2e$503$2e$0_react$40$19$2e$1$2e$0$2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$instagram$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Instagram$3e$__["Instagram"],
                                            whatsapp: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$lucide$2d$react$40$0$2e$503$2e$0_react$40$19$2e$1$2e$0$2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$message$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__MessageCircle$3e$__["MessageCircle"]
                                        }[social.icon?.toLowerCase()];
                                        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("a", {
                                            href: social.href,
                                            className: "w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-primary transition-colors duration-300 group",
                                            "aria-label": social.name,
                                            target: "_blank",
                                            rel: "noopener noreferrer",
                                            children: LucideIcon ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(LucideIcon, {
                                                className: "w-5 h-5 text-gray-400 group-hover:text-white"
                                            }, void 0, false, {
                                                fileName: "[project]/components/layout/footer.tsx",
                                                lineNumber: 229,
                                                columnNumber: 23
                                            }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                                className: `icon-${social.icon} w-5 h-5 text-gray-400 group-hover:text-white`
                                            }, void 0, false, {
                                                fileName: "[project]/components/layout/footer.tsx",
                                                lineNumber: 231,
                                                columnNumber: 23
                                            }, this)
                                        }, social.name, false, {
                                            fileName: "[project]/components/layout/footer.tsx",
                                            lineNumber: 220,
                                            columnNumber: 19
                                        }, this);
                                    })
                                }, void 0, false, {
                                    fileName: "[project]/components/layout/footer.tsx",
                                    lineNumber: 209,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/layout/footer.tsx",
                            lineNumber: 175,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "lg:col-span-2",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                    className: "text-lg font-semibold mb-6",
                                    children: "Nos Services"
                                }, void 0, false, {
                                    fileName: "[project]/components/layout/footer.tsx",
                                    lineNumber: 241,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "grid md:grid-cols-3 gap-8",
                                    children: services.map((service)=>{
                                        const Icon = service.icon;
                                        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "space-y-4",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                    href: service.href,
                                                    className: "flex items-center space-x-3 text-white hover:text-primary transition-colors duration-300 group",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Icon, {
                                                            className: "w-5 h-5 text-primary"
                                                        }, void 0, false, {
                                                            fileName: "[project]/components/layout/footer.tsx",
                                                            lineNumber: 251,
                                                            columnNumber: 23
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: "font-medium",
                                                            children: service.name
                                                        }, void 0, false, {
                                                            fileName: "[project]/components/layout/footer.tsx",
                                                            lineNumber: 252,
                                                            columnNumber: 23
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/components/layout/footer.tsx",
                                                    lineNumber: 247,
                                                    columnNumber: 21
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                                                    className: "space-y-2",
                                                    children: service.items.map((item, index)=>typeof item === 'string' ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                                href: service.href,
                                                                className: "text-gray-400 hover:text-white transition-colors duration-300 text-sm",
                                                                children: item
                                                            }, void 0, false, {
                                                                fileName: "[project]/components/layout/footer.tsx",
                                                                lineNumber: 258,
                                                                columnNumber: 29
                                                            }, this)
                                                        }, index, false, {
                                                            fileName: "[project]/components/layout/footer.tsx",
                                                            lineNumber: 257,
                                                            columnNumber: 27
                                                        }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                                href: item.href,
                                                                className: "text-gray-400 hover:text-white transition-colors duration-300 text-sm",
                                                                children: item.label
                                                            }, void 0, false, {
                                                                fileName: "[project]/components/layout/footer.tsx",
                                                                lineNumber: 267,
                                                                columnNumber: 29
                                                            }, this)
                                                        }, item.href, false, {
                                                            fileName: "[project]/components/layout/footer.tsx",
                                                            lineNumber: 266,
                                                            columnNumber: 27
                                                        }, this))
                                                }, void 0, false, {
                                                    fileName: "[project]/components/layout/footer.tsx",
                                                    lineNumber: 254,
                                                    columnNumber: 21
                                                }, this)
                                            ]
                                        }, service.name, true, {
                                            fileName: "[project]/components/layout/footer.tsx",
                                            lineNumber: 246,
                                            columnNumber: 19
                                        }, this);
                                    })
                                }, void 0, false, {
                                    fileName: "[project]/components/layout/footer.tsx",
                                    lineNumber: 242,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/layout/footer.tsx",
                            lineNumber: 240,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "space-y-8",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                        className: "text-lg font-semibold mb-6",
                                        children: "Liens Rapides"
                                    }, void 0, false, {
                                        fileName: "[project]/components/layout/footer.tsx",
                                        lineNumber: 286,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                                        className: "space-y-3",
                                        children: quickLinks.map((link)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                    href: link.href,
                                                    className: "text-gray-400 hover:text-white transition-colors duration-300 flex items-center group",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$lucide$2d$react$40$0$2e$503$2e$0_react$40$19$2e$1$2e$0$2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$arrow$2d$right$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ArrowRight$3e$__["ArrowRight"], {
                                                            className: "w-3 h-3 mr-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                                                        }, void 0, false, {
                                                            fileName: "[project]/components/layout/footer.tsx",
                                                            lineNumber: 294,
                                                            columnNumber: 23
                                                        }, this),
                                                        link.name
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/components/layout/footer.tsx",
                                                    lineNumber: 290,
                                                    columnNumber: 21
                                                }, this)
                                            }, link.name, false, {
                                                fileName: "[project]/components/layout/footer.tsx",
                                                lineNumber: 289,
                                                columnNumber: 19
                                            }, this))
                                    }, void 0, false, {
                                        fileName: "[project]/components/layout/footer.tsx",
                                        lineNumber: 287,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/components/layout/footer.tsx",
                                lineNumber: 285,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/components/layout/footer.tsx",
                            lineNumber: 284,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/components/layout/footer.tsx",
                    lineNumber: 173,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/components/layout/footer.tsx",
                lineNumber: 172,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "border-t border-gray-800",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "container mx-auto px-4 py-8",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "text-gray-400 text-sm",
                                    children: [
                                        "© ",
                                        currentYear,
                                        " Charlie Oscar Consulting. Tous droits réservés."
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/components/layout/footer.tsx",
                                    lineNumber: 322,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex flex-wrap justify-center md:justify-end gap-6",
                                    children: legalLinks.map((link)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            href: link.href,
                                            className: "text-gray-400 hover:text-white transition-colors duration-300 text-sm",
                                            children: link.name
                                        }, link.name, false, {
                                            fileName: "[project]/components/layout/footer.tsx",
                                            lineNumber: 328,
                                            columnNumber: 17
                                        }, this))
                                }, void 0, false, {
                                    fileName: "[project]/components/layout/footer.tsx",
                                    lineNumber: 326,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/layout/footer.tsx",
                            lineNumber: 321,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "mt-8 pt-8 border-t border-gray-800 text-center",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-gray-500 text-xs",
                                children: "Développé avec ❤️ pour accompagner vos projets au Cameroun"
                            }, void 0, false, {
                                fileName: "[project]/components/layout/footer.tsx",
                                lineNumber: 340,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/components/layout/footer.tsx",
                            lineNumber: 339,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/components/layout/footer.tsx",
                    lineNumber: 320,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/components/layout/footer.tsx",
                lineNumber: 319,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/components/layout/footer.tsx",
        lineNumber: 141,
        columnNumber: 5
    }, this);
}
_s(Footer, "xbyQPtUVMO7MNj7WjJlpdWqRcTo=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$1_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePathname"]
    ];
});
_c = Footer;
var _c;
__turbopack_context__.k.register(_c, "Footer");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=_d7b97408._.js.map