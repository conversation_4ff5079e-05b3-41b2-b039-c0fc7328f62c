{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/charlie-oscar-consulting/website/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card\"\r\n      className={cn(\r\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-header\"\r\n      className={cn(\r\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-title\"\r\n      className={cn(\"leading-none font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-action\"\r\n      className={cn(\r\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-content\"\r\n      className={cn(\"px-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-footer\"\r\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Card,\r\n  CardHeader,\r\n  CardFooter,\r\n  CardTitle,\r\n  CardAction,\r\n  CardDescription,\r\n  CardContent,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6WAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6WAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6WAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6WAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6WAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6WAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6WAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 127, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/charlie-oscar-consulting/website/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst badgeVariants = cva(\r\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\r\n        secondary:\r\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\r\n        destructive:\r\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\r\n        outline:\r\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nfunction Badge({\r\n  className,\r\n  variant,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"span\"> &\r\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\r\n  const Comp = asChild ? Slot : \"span\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"badge\"\r\n      className={cn(badgeVariants({ variant }), className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Badge, badgeVariants }\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,2OAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,oSAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6WAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 173, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/charlie-oscar-consulting/website/components/ui/accordion.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Accordion = registerClientReference(\n    function() { throw new Error(\"Attempted to call Accordion() from the server but Accordion is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/accordion.tsx <module evaluation>\",\n    \"Accordion\",\n);\nexport const AccordionContent = registerClientReference(\n    function() { throw new Error(\"Attempted to call AccordionContent() from the server but AccordionContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/accordion.tsx <module evaluation>\",\n    \"AccordionContent\",\n);\nexport const AccordionItem = registerClientReference(\n    function() { throw new Error(\"Attempted to call AccordionItem() from the server but AccordionItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/accordion.tsx <module evaluation>\",\n    \"AccordionItem\",\n);\nexport const AccordionTrigger = registerClientReference(\n    function() { throw new Error(\"Attempted to call AccordionTrigger() from the server but AccordionTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/accordion.tsx <module evaluation>\",\n    \"AccordionTrigger\",\n);\n"], "names": [], "mappings": ";;;;;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,oXAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,6DACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,oXAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,6DACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,oXAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,6DACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,oXAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,6DACA", "debugId": null}}, {"offset": {"line": 199, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/charlie-oscar-consulting/website/components/ui/accordion.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Accordion = registerClientReference(\n    function() { throw new Error(\"Attempted to call Accordion() from the server but Accordion is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/accordion.tsx\",\n    \"Accordion\",\n);\nexport const AccordionContent = registerClientReference(\n    function() { throw new Error(\"Attempted to call AccordionContent() from the server but AccordionContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/accordion.tsx\",\n    \"AccordionContent\",\n);\nexport const AccordionItem = registerClientReference(\n    function() { throw new Error(\"Attempted to call AccordionItem() from the server but AccordionItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/accordion.tsx\",\n    \"AccordionItem\",\n);\nexport const AccordionTrigger = registerClientReference(\n    function() { throw new Error(\"Attempted to call AccordionTrigger() from the server but AccordionTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/accordion.tsx\",\n    \"AccordionTrigger\",\n);\n"], "names": [], "mappings": ";;;;;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,oXAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,yCACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,oXAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,yCACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,oXAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,yCACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,oXAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,yCACA", "debugId": null}}, {"offset": {"line": 225, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 240, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/charlie-oscar-consulting/website/app/cms/utils/immobilier.ts"], "sourcesContent": ["import immobilierData from '../data/immobilier.json';\n\n/**\n * Récupère toutes les données de la section immobilier\n * @returns Données complètes de la section immobilier\n */\nexport function getImmobilierContent() {\n  return immobilierData;\n}\n\n/**\n * Récupère les métadonnées de la page immobilier\n * @returns Métadonnées pour le SEO\n */\nexport function getImmobilierMetadata() {\n  return immobilierData.metadata;\n}\n\n/**\n * Récupère les données de la section hero\n * @returns Données de la section hero\n */\nexport function getImmobilierHero() {\n  return immobilierData.hero;\n}\n\n/**\n * Récupère les données de la section à propos\n * @returns Données de la section à propos\n */\nexport function getImmobilierAbout() {\n  return immobilierData.about;\n}\n\n/**\n * Récupère la liste des services immobiliers\n * @returns Liste des services avec leurs détails\n */\nexport function getImmobilierServices() {\n  return immobilierData.services;\n}\n\n/**\n * Récupère un service spécifique par son ID\n * @param serviceId - L'ID du service à récupérer\n * @returns Le service correspondant ou undefined\n */\nexport function getImmobilierServiceById(serviceId: string) {\n  return immobilierData.services.items.find(service => service.id === serviceId);\n}\n\n/**\n * Récupère les avantages de Charlie Oscar Consulting\n * @returns Liste des avantages\n */\nexport function getImmobilierAdvantages() {\n  return immobilierData.advantages;\n}\n\n/**\n * Récupère les données de la section CTA\n * @returns Données de la section call-to-action\n */\nexport function getImmobilierCTA() {\n  return immobilierData.cta;\n}\n\n/**\n * Récupère tous les services avec pagination\n * @param page - Numéro de page (commence à 1)\n * @param limit - Nombre d'éléments par page\n * @returns Services paginés\n */\nexport function getImmobilierServicesPaginated(page: number = 1, limit: number = 6) {\n  const services = immobilierData.services.items;\n  const startIndex = (page - 1) * limit;\n  const endIndex = startIndex + limit;\n  \n  return {\n    services: services.slice(startIndex, endIndex),\n    totalServices: services.length,\n    currentPage: page,\n    totalPages: Math.ceil(services.length / limit),\n    hasNextPage: endIndex < services.length,\n    hasPrevPage: page > 1\n  };\n}\n\n/**\n * Recherche des services par terme\n * @param searchTerm - Terme de recherche\n * @returns Services correspondant au terme de recherche\n */\nexport function searchImmobilierServices(searchTerm: string) {\n  const services = immobilierData.services.items;\n  const term = searchTerm.toLowerCase();\n  \n  return services.filter(service => \n    service.title.toLowerCase().includes(term) ||\n    service.description.toLowerCase().includes(term)\n  );\n}\n\n/**\n * Récupère les services par catégorie/couleur\n * @param color - Couleur/catégorie du service\n * @returns Services de la catégorie spécifiée\n */\nexport function getImmobilierServicesByCategory(color: string) {\n  return immobilierData.services.items.filter(service => service.color === color);\n}\n\n/**\n * Récupère tous les services détaillés pour la page services\n * @returns Liste complète des services détaillés\n */\nexport function getDetailedImmobilierServices() {\n  return immobilierData.detailedServices;\n}\n\n/**\n * Récupère un service détaillé par son ID\n * @param serviceId - L'ID du service à récupérer\n * @returns Le service détaillé correspondant ou undefined\n */\nexport function getDetailedImmobilierServiceById(serviceId: string) {\n  return immobilierData.detailedServices.find(service => service.id === serviceId);\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA;;AAMO,SAAS;IACd,OAAO,wGAAA,CAAA,UAAc;AACvB;AAMO,SAAS;IACd,OAAO,wGAAA,CAAA,UAAc,CAAC,QAAQ;AAChC;AAMO,SAAS;IACd,OAAO,wGAAA,CAAA,UAAc,CAAC,IAAI;AAC5B;AAMO,SAAS;IACd,OAAO,wGAAA,CAAA,UAAc,CAAC,KAAK;AAC7B;AAMO,SAAS;IACd,OAAO,wGAAA,CAAA,UAAc,CAAC,QAAQ;AAChC;AAOO,SAAS,yBAAyB,SAAiB;IACxD,OAAO,wGAAA,CAAA,UAAc,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;AACtE;AAMO,SAAS;IACd,OAAO,wGAAA,CAAA,UAAc,CAAC,UAAU;AAClC;AAMO,SAAS;IACd,OAAO,wGAAA,CAAA,UAAc,CAAC,GAAG;AAC3B;AAQO,SAAS,+BAA+B,OAAe,CAAC,EAAE,QAAgB,CAAC;IAChF,MAAM,WAAW,wGAAA,CAAA,UAAc,CAAC,QAAQ,CAAC,KAAK;IAC9C,MAAM,aAAa,CAAC,OAAO,CAAC,IAAI;IAChC,MAAM,WAAW,aAAa;IAE9B,OAAO;QACL,UAAU,SAAS,KAAK,CAAC,YAAY;QACrC,eAAe,SAAS,MAAM;QAC9B,aAAa;QACb,YAAY,KAAK,IAAI,CAAC,SAAS,MAAM,GAAG;QACxC,aAAa,WAAW,SAAS,MAAM;QACvC,aAAa,OAAO;IACtB;AACF;AAOO,SAAS,yBAAyB,UAAkB;IACzD,MAAM,WAAW,wGAAA,CAAA,UAAc,CAAC,QAAQ,CAAC,KAAK;IAC9C,MAAM,OAAO,WAAW,WAAW;IAEnC,OAAO,SAAS,MAAM,CAAC,CAAA,UACrB,QAAQ,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,SACrC,QAAQ,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC;AAE/C;AAOO,SAAS,gCAAgC,KAAa;IAC3D,OAAO,wGAAA,CAAA,UAAc,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA,UAAW,QAAQ,KAAK,KAAK;AAC3E;AAMO,SAAS;IACd,OAAO,wGAAA,CAAA,UAAc,CAAC,gBAAgB;AACxC;AAOO,SAAS,iCAAiC,SAAiB;IAChE,OAAO,wGAAA,CAAA,UAAc,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;AACxE", "debugId": null}}, {"offset": {"line": 314, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/charlie-oscar-consulting/website/app/immobilier/services/page.tsx"], "sourcesContent": ["import { <PERSON>ada<PERSON> } from \"next\";\r\nimport Link from \"next/link\";\r\nimport Image from \"next/image\";\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\r\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\";\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport {\r\n  Accordion,\r\n  AccordionContent,\r\n  AccordionItem,\r\n  AccordionTrigger,\r\n} from \"@/components/ui/accordion\";\r\nimport {\r\n  ArrowRight,\r\n  Home,\r\n  Building2,\r\n  MapPin,\r\n  Key,\r\n  Users,\r\n  Shield,\r\n  CheckCircle,\r\n  Clock,\r\n  Star,\r\n  Scale,\r\n  Compass\r\n} from \"lucide-react\";\r\nimport { getDetailedImmobilierServices, getImmobilierContent } from \"@/app/cms/utils/immobilier\";\r\n\r\nconst immobilierData = getImmobilierContent();\r\nconst detailedServices = getDetailedImmobilierServices();\r\n\r\nexport const metadata: Metadata = {\r\n  title: \"Nos Services Immobiliers - Charlie Oscar Consulting\",\r\n  description: \"Découvrez tous nos services immobiliers au Cameroun : achat, vente, location, conseil juridique. Expertise complète pour tous vos projets.\",\r\n  keywords: \"services immobiliers Cameroun, achat vente immobilier, location immobilier, conseil juridique immobilier\",\r\n  openGraph: {\r\n    title: \"Nos Services Immobiliers - Charlie Oscar Consulting\",\r\n    description: \"Expertise complète en immobilier au Cameroun. Achat, vente, location et conseil juridique.\",\r\n    type: \"website\",\r\n    locale: \"fr_FR\",\r\n    siteName: \"Charlie Oscar Consulting\"\r\n  },\r\n  alternates: {\r\n    canonical: \"/immobilier/services\"\r\n  }\r\n};\r\n\r\nexport default function ImmobilierServicesPage() {\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-white\">\r\n      {/* Hero Section - Clean & Minimal */}\r\n      <section className=\"relative py-32 bg-gradient-to-br from-gray-50 to-white\">\r\n        <div className=\"container mx-auto px-4\">\r\n          <div className=\"max-w-4xl mx-auto text-center space-y-12\">\r\n            <div className=\"space-y-8\">\r\n              <div className=\"inline-flex items-center px-6 py-3 bg-white text-blue-700 rounded-full text-sm font-medium shadow-lg border border-blue-100\">\r\n                <Home className=\"w-4 h-4 mr-2\" />\r\n                Services Immobiliers\r\n              </div>\r\n\r\n              <h1 className=\"text-5xl lg:text-7xl font-bold text-gray-900 leading-tight\">\r\n                Nos Services\r\n                <span className=\"block text-blue-600\">Immobiliers</span>\r\n              </h1>\r\n\r\n              <p className=\"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed\">\r\n                Une gamme complète de services pour tous vos besoins immobiliers au Cameroun.\r\n                De l'achat à la vente, de la location au conseil juridique.\r\n              </p>\r\n            </div>\r\n\r\n            <div className=\"flex flex-col sm:flex-row gap-6 justify-center\">\r\n              <Button asChild size=\"lg\" className=\"bg-blue-600 hover:bg-blue-700 px-8 py-4 text-lg group\">\r\n                <Link href=\"/contact\" className=\"flex items-center\">\r\n                  Demander un devis\r\n                  <ArrowRight className=\"ml-2 h-5 w-5 transition-transform group-hover:translate-x-1\" />\r\n                </Link>\r\n              </Button>\r\n\r\n              <Button asChild size=\"lg\" variant=\"outline\" className=\"border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white px-8 py-4 text-lg\">\r\n                <Link href=\"/contact\">\r\n                  Nous contacter\r\n                </Link>\r\n              </Button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Services Section with Accordions */}\r\n      <section className=\"py-32\">\r\n        <div className=\"container mx-auto px-4\">\r\n          <div className=\"max-w-6xl mx-auto space-y-16\">\r\n            {detailedServices.map((service) => (\r\n              <div key={service.id} id={service.id} className=\"scroll-mt-24\">\r\n                <Card className=\"overflow-hidden border-0 shadow-2xl bg-white\">\r\n                  <div className=\"grid lg:grid-cols-2 gap-0\">\r\n                    {/* Image Section */}\r\n                    <div className=\"relative h-80 lg:h-auto\">\r\n                      <Image\r\n                        src={service.image}\r\n                        alt={service.title}\r\n                        fill\r\n                        className=\"object-cover\"\r\n                      />\r\n                      <div className=\"absolute inset-0 bg-gradient-to-r from-black/20 to-transparent\"></div>\r\n                    </div>\r\n\r\n                    {/* Content Section */}\r\n                    <div className=\"p-12 space-y-8\">\r\n                      <div className=\"space-y-6\">\r\n                        <div className=\"flex items-center space-x-4\">\r\n                          <Badge className={`\r\n                            ${service.color === 'orange' ? 'bg-orange-100 text-orange-800 border-orange-200' : ''}\r\n                            ${service.color === 'blue' ? 'bg-blue-100 text-blue-800 border-blue-200' : ''}\r\n                            ${service.color === 'green' ? 'bg-green-100 text-green-800 border-green-200' : ''}\r\n                            ${service.color === 'purple' ? 'bg-purple-100 text-purple-800 border-purple-200' : ''}\r\n                            ${service.color === 'yellow' ? 'bg-yellow-100 text-yellow-800 border-yellow-200' : ''}\r\n                            ${service.color === 'red' ? 'bg-red-100 text-red-800 border-red-200' : ''}\r\n                            px-4 py-2 text-sm font-medium\r\n                          `}>\r\n                            {service.icon === 'map' && <MapPin className=\"w-3 h-3 mr-1\" />}\r\n                            {service.icon === 'home' && <Home className=\"w-3 h-3 mr-1\" />}\r\n                            {service.icon === 'building' && <Building2 className=\"w-3 h-3 mr-1\" />}\r\n                            {service.icon === 'key' && <Key className=\"w-3 h-3 mr-1\" />}\r\n                            {service.icon === 'compass' && <Compass className=\"w-3 h-3 mr-1\" />}\r\n                            {service.icon === 'scale' && <Scale className=\"w-3 h-3 mr-1\" />}\r\n                            Service Spécialisé\r\n                          </Badge>\r\n                        </div>\r\n\r\n                        <h2 className=\"text-3xl font-bold text-gray-900\">\r\n                          {service.title}\r\n                        </h2>\r\n\r\n                        <p className=\"text-lg text-gray-600 leading-relaxed\">\r\n                          {service.description}\r\n                        </p>\r\n                      </div>\r\n\r\n                      {/* Services Accordion */}\r\n                      <Accordion type=\"single\" collapsible className=\"w-full\">\r\n                        {service.services.map((subService, subIndex) => (\r\n                          <AccordionItem key={subIndex} value={`item-${subIndex}`} className=\"border-gray-200\">\r\n                            <AccordionTrigger className=\"text-left hover:no-underline hover:text-blue-600 transition-colors\">\r\n                              <span className=\"font-semibold\">{subService.title}</span>\r\n                            </AccordionTrigger>\r\n                            <AccordionContent className=\"text-gray-600 leading-relaxed pt-2\">\r\n                              {subService.description}\r\n                            </AccordionContent>\r\n                          </AccordionItem>\r\n                        ))}\r\n                      </Accordion>\r\n\r\n                      <div className=\"pt-4\">\r\n                        <Button asChild className={`\r\n                          ${service.color === 'orange' ? 'bg-orange-600 hover:bg-orange-700' : ''}\r\n                          ${service.color === 'blue' ? 'bg-blue-600 hover:bg-blue-700' : ''}\r\n                          ${service.color === 'green' ? 'bg-green-600 hover:bg-green-700' : ''}\r\n                          ${service.color === 'purple' ? 'bg-purple-600 hover:bg-purple-700' : ''}\r\n                          ${service.color === 'yellow' ? 'bg-yellow-600 hover:bg-yellow-700' : ''}\r\n                          ${service.color === 'red' ? 'bg-red-600 hover:bg-red-700' : ''}\r\n                          text-white group\r\n                        `}>\r\n                          <Link href=\"/contact\" className=\"flex items-center\">\r\n                            Demander ce service\r\n                            <ArrowRight className=\"ml-2 h-4 w-4 transition-transform group-hover:translate-x-1\" />\r\n                          </Link>\r\n                        </Button>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </Card>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      </section>\r\n      {/* CTA Section - Clean & Modern */}\r\n      <section className=\"py-32 bg-gradient-to-br from-blue-600 via-blue-700 to-blue-800 text-white relative overflow-hidden\">\r\n        {/* Background Pattern */}\r\n        <div className=\"absolute inset-0 opacity-10\">\r\n          <div className=\"absolute top-0 left-0 w-full h-full bg-[url('data:image/svg+xml,%3Csvg%20width%3D%2260%22%20height%3D%2260%22%20viewBox%3D%220%200%2060%2060%22%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%3E%3Cg%20fill%3D%22none%22%20fill-rule%3D%22evenodd%22%3E%3Cg%20fill%3D%22%23ffffff%22%20fill-opacity%3D%220.1%22%3E%3Ccircle%20cx%3D%2230%22%20cy%3D%2230%22%20r%3D%222%22/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')]\"></div>\r\n        </div>\r\n\r\n        <div className=\"container mx-auto px-4 relative z-10\">\r\n          <div className=\"max-w-4xl mx-auto text-center space-y-12\">\r\n            <div className=\"space-y-6\">\r\n              <h2 className=\"text-4xl md:text-5xl font-bold leading-tight\">\r\n                Besoin d'un Service Personnalisé ?\r\n              </h2>\r\n\r\n              <p className=\"text-xl text-blue-100 leading-relaxed max-w-3xl mx-auto\">\r\n                Chaque projet immobilier est unique. Nos experts analysent votre situation\r\n                et vous proposent la solution la plus adaptée à vos besoins.\r\n              </p>\r\n            </div>\r\n\r\n            <div className=\"flex flex-col sm:flex-row gap-6 justify-center\">\r\n              <Button\r\n                asChild\r\n                size=\"lg\"\r\n                className=\"bg-white text-blue-600 hover:bg-gray-100 px-8 py-4 text-lg group\"\r\n                variant=\"secondary\"\r\n              >\r\n                <Link href=\"/contact\" className=\"flex items-center\">\r\n                  Contactez-nous maintenant !\r\n                  <ArrowRight className=\"ml-2 h-5 w-5 transition-transform group-hover:translate-x-1\" />\r\n                </Link>\r\n              </Button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AACA;AACA;AACA;AACA;AAMA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA;;;;;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,iIAAA,CAAA,uBAAoB,AAAD;AAC1C,MAAM,mBAAmB,CAAA,GAAA,iIAAA,CAAA,gCAA6B,AAAD;AAE9C,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,UAAU;IACV,WAAW;QACT,OAAO;QACP,aAAa;QACb,MAAM;QACN,QAAQ;QACR,UAAU;IACZ;IACA,YAAY;QACV,WAAW;IACb;AACF;AAEe,SAAS;IAEtB,qBACE,6WAAC;QAAI,WAAU;;0BAEb,6WAAC;gBAAQ,WAAU;0BACjB,cAAA,6WAAC;oBAAI,WAAU;8BACb,cAAA,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,uRAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAInC,6WAAC;wCAAG,WAAU;;4CAA6D;0DAEzE,6WAAC;gDAAK,WAAU;0DAAsB;;;;;;;;;;;;kDAGxC,6WAAC;wCAAE,WAAU;kDAA0D;;;;;;;;;;;;0CAMzE,6WAAC;gCAAI,WAAU;;kDACb,6WAAC,2HAAA,CAAA,SAAM;wCAAC,OAAO;wCAAC,MAAK;wCAAK,WAAU;kDAClC,cAAA,6WAAC,2RAAA,CAAA,UAAI;4CAAC,MAAK;4CAAW,WAAU;;gDAAoB;8DAElD,6WAAC,sSAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAI1B,6WAAC,2HAAA,CAAA,SAAM;wCAAC,OAAO;wCAAC,MAAK;wCAAK,SAAQ;wCAAU,WAAU;kDACpD,cAAA,6WAAC,2RAAA,CAAA,UAAI;4CAAC,MAAK;sDAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUhC,6WAAC;gBAAQ,WAAU;0BACjB,cAAA,6WAAC;oBAAI,WAAU;8BACb,cAAA,6WAAC;wBAAI,WAAU;kCACZ,iBAAiB,GAAG,CAAC,CAAC,wBACrB,6WAAC;gCAAqB,IAAI,QAAQ,EAAE;gCAAE,WAAU;0CAC9C,cAAA,6WAAC,yHAAA,CAAA,OAAI;oCAAC,WAAU;8CACd,cAAA,6WAAC;wCAAI,WAAU;;0DAEb,6WAAC;gDAAI,WAAU;;kEACb,6WAAC,4PAAA,CAAA,UAAK;wDACJ,KAAK,QAAQ,KAAK;wDAClB,KAAK,QAAQ,KAAK;wDAClB,IAAI;wDACJ,WAAU;;;;;;kEAEZ,6WAAC;wDAAI,WAAU;;;;;;;;;;;;0DAIjB,6WAAC;gDAAI,WAAU;;kEACb,6WAAC;wDAAI,WAAU;;0EACb,6WAAC;gEAAI,WAAU;0EACb,cAAA,6WAAC,0HAAA,CAAA,QAAK;oEAAC,WAAW,CAAC;4BACjB,EAAE,QAAQ,KAAK,KAAK,WAAW,oDAAoD,GAAG;4BACtF,EAAE,QAAQ,KAAK,KAAK,SAAS,8CAA8C,GAAG;4BAC9E,EAAE,QAAQ,KAAK,KAAK,UAAU,iDAAiD,GAAG;4BAClF,EAAE,QAAQ,KAAK,KAAK,WAAW,oDAAoD,GAAG;4BACtF,EAAE,QAAQ,KAAK,KAAK,WAAW,oDAAoD,GAAG;4BACtF,EAAE,QAAQ,KAAK,KAAK,QAAQ,2CAA2C,GAAG;;0BAE5E,CAAC;;wEACE,QAAQ,IAAI,KAAK,uBAAS,6WAAC,8RAAA,CAAA,SAAM;4EAAC,WAAU;;;;;;wEAC5C,QAAQ,IAAI,KAAK,wBAAU,6WAAC,uRAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;wEAC3C,QAAQ,IAAI,KAAK,4BAAc,6WAAC,oSAAA,CAAA,YAAS;4EAAC,WAAU;;;;;;wEACpD,QAAQ,IAAI,KAAK,uBAAS,6WAAC,oRAAA,CAAA,MAAG;4EAAC,WAAU;;;;;;wEACzC,QAAQ,IAAI,KAAK,2BAAa,6WAAC,4RAAA,CAAA,UAAO;4EAAC,WAAU;;;;;;wEACjD,QAAQ,IAAI,KAAK,yBAAW,6WAAC,wRAAA,CAAA,QAAK;4EAAC,WAAU;;;;;;wEAAkB;;;;;;;;;;;;0EAKpE,6WAAC;gEAAG,WAAU;0EACX,QAAQ,KAAK;;;;;;0EAGhB,6WAAC;gEAAE,WAAU;0EACV,QAAQ,WAAW;;;;;;;;;;;;kEAKxB,6WAAC,8HAAA,CAAA,YAAS;wDAAC,MAAK;wDAAS,WAAW;wDAAC,WAAU;kEAC5C,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAC,YAAY,yBACjC,6WAAC,8HAAA,CAAA,gBAAa;gEAAgB,OAAO,CAAC,KAAK,EAAE,UAAU;gEAAE,WAAU;;kFACjE,6WAAC,8HAAA,CAAA,mBAAgB;wEAAC,WAAU;kFAC1B,cAAA,6WAAC;4EAAK,WAAU;sFAAiB,WAAW,KAAK;;;;;;;;;;;kFAEnD,6WAAC,8HAAA,CAAA,mBAAgB;wEAAC,WAAU;kFACzB,WAAW,WAAW;;;;;;;+DALP;;;;;;;;;;kEAWxB,6WAAC;wDAAI,WAAU;kEACb,cAAA,6WAAC,2HAAA,CAAA,SAAM;4DAAC,OAAO;4DAAC,WAAW,CAAC;0BAC1B,EAAE,QAAQ,KAAK,KAAK,WAAW,sCAAsC,GAAG;0BACxE,EAAE,QAAQ,KAAK,KAAK,SAAS,kCAAkC,GAAG;0BAClE,EAAE,QAAQ,KAAK,KAAK,UAAU,oCAAoC,GAAG;0BACrE,EAAE,QAAQ,KAAK,KAAK,WAAW,sCAAsC,GAAG;0BACxE,EAAE,QAAQ,KAAK,KAAK,WAAW,sCAAsC,GAAG;0BACxE,EAAE,QAAQ,KAAK,KAAK,QAAQ,gCAAgC,GAAG;;wBAEjE,CAAC;sEACC,cAAA,6WAAC,2RAAA,CAAA,UAAI;gEAAC,MAAK;gEAAW,WAAU;;oEAAoB;kFAElD,6WAAC,sSAAA,CAAA,aAAU;wEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;+BAxE1B,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;0BAqF5B,6WAAC;gBAAQ,WAAU;;kCAEjB,6WAAC;wBAAI,WAAU;kCACb,cAAA,6WAAC;4BAAI,WAAU;;;;;;;;;;;kCAGjB,6WAAC;wBAAI,WAAU;kCACb,cAAA,6WAAC;4BAAI,WAAU;;8CACb,6WAAC;oCAAI,WAAU;;sDACb,6WAAC;4CAAG,WAAU;sDAA+C;;;;;;sDAI7D,6WAAC;4CAAE,WAAU;sDAA0D;;;;;;;;;;;;8CAMzE,6WAAC;oCAAI,WAAU;8CACb,cAAA,6WAAC,2HAAA,CAAA,SAAM;wCACL,OAAO;wCACP,MAAK;wCACL,WAAU;wCACV,SAAQ;kDAER,cAAA,6WAAC,2RAAA,CAAA,UAAI;4CAAC,MAAK;4CAAW,WAAU;;gDAAoB;8DAElD,6WAAC,sSAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxC", "debugId": null}}]}