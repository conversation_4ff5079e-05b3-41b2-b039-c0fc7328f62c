{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/charlie-oscar-consulting/website/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card\"\r\n      className={cn(\r\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-header\"\r\n      className={cn(\r\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-title\"\r\n      className={cn(\"leading-none font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-action\"\r\n      className={cn(\r\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-content\"\r\n      className={cn(\"px-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-footer\"\r\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Card,\r\n  CardHeader,\r\n  CardFooter,\r\n  CardTitle,\r\n  CardAction,\r\n  CardDescription,\r\n  CardContent,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,4TAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,4TAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,4TAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,4TAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,4TAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,4TAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,4TAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 127, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/charlie-oscar-consulting/website/app/cms/utils/foncier.ts"], "sourcesContent": ["import foncier from \"@/app/cms/data/foncier.json\";\r\n\r\nexport function getFoncierContent() {\r\n  return foncier;\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;;AAEO,SAAS;IACd,OAAO,qGAAA,CAAA,UAAO;AAChB", "debugId": null}}, {"offset": {"line": 144, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/charlie-oscar-consulting/website/components/foncier/process-section.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState } from \"react\";\r\nimport Link from \"next/link\";\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport { Card, CardContent } from \"@/components/ui/card\";\r\nimport { Search, MapPin, FileText, Award, ArrowRight, CheckCircle, Clock } from \"lucide-react\";\r\nimport { getFoncierContent } from \"@/app/cms/utils/foncier\";\r\n\r\nexport function ProcessSection() {\r\n  const [activeStep, setActiveStep] = useState(1);\r\n  const processContent = getFoncierContent().process;\r\n\r\n  const iconMap = { Search, MapPin, FileText, Award };\r\n  const steps = processContent?.steps?.map((step: any) => ({\r\n    ...step,\r\n    icon: iconMap[step.icon] || FileText\r\n  })) || [];\r\n\r\n  return (\r\n    <section id=\"processus\" className=\"py-20 bg-white\">\r\n      <div className=\"container mx-auto px-4\">\r\n        <div className=\"max-w-6xl mx-auto\">\r\n          {/* Section Header */}\r\n          <div className=\"text-center space-y-4 mb-16\">\r\n            <div className=\"inline-flex items-center px-4 py-2 bg-primary/10 text-primary rounded-full text-sm font-medium\">\r\n              Notre méthode\r\n            </div>\r\n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900\">\r\n              {processContent?.title || \"Comment ça marche ?\"}\r\n            </h2>\r\n            <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\r\n              {processContent?.subtitle || \"Notre processus éprouvé en 4 étapes vous garantit l'obtention de votre titre foncier dans les meilleures conditions.\"}\r\n            </p>\r\n          </div>\r\n\r\n          {/* Timeline */}\r\n          <div className=\"mb-16\">\r\n            {/* Desktop Timeline */}\r\n            <div className=\"hidden lg:block\">\r\n              <div className=\"relative \">\r\n                {/* Timeline Line */}\r\n                <div className=\"absolute top-1/2 my-5 left-0 right-0 h-1 bg-gray-200 transform -translate-y-1/2\"></div>\r\n                <div\r\n                  className=\"absolute top-1/2 left-0 my-5 h-1 bg-primary transform -translate-y-1/2\"\r\n                  style={{ width: `${(activeStep / 4) * 100}%` }}\r\n                ></div>\r\n\r\n                {/* Steps */}\r\n                <div className=\"relative flex justify-between\">\r\n                  {steps.map((step, index) => {\r\n                    const Icon = step.icon;\r\n                    const isActive = activeStep >= step.id;\r\n                    const isCurrent = activeStep === step.id;\r\n                    \r\n                    return (\r\n                      <div\r\n                        key={step.id}\r\n                        className=\"flex flex-col items-center cursor-pointer group\"\r\n                        onClick={() => setActiveStep(step.id)}\r\n                      >\r\n                        {/* Step Circle */}\r\n                        <div className={`w-16 h-16 rounded-full flex items-center justify-center ${\r\n                          isActive\r\n                            ? 'bg-primary text-white shadow-lg'\r\n                            : 'bg-white border-2 border-gray-300 text-gray-400'\r\n                        }`}>\r\n                          <Icon className=\"w-7 h-7\" />\r\n                        </div>\r\n\r\n                        {/* Step Info */}\r\n                        <div className=\"mt-4 text-center max-w-xs\">\r\n                          <h3 className={`font-bold text-md ${\r\n                            isCurrent ? 'text-primary' : 'text-gray-900'\r\n                          }`}>\r\n                            {step.title}\r\n                          </h3>\r\n                          <p className=\"text-sm text-gray-600 mt-2 leading-relaxed\">\r\n                            {step.description.slice(0, 100)}{step.description.length > 100 ? '...' : ''}\r\n                          </p>\r\n                          {/* <div className=\"flex items-center justify-center mt-2 text-xs text-gray-500\">\r\n                            <Clock className=\"w-3 h-3 mr-1\" />\r\n                            {step.duration}\r\n                          </div> */}\r\n                        </div>\r\n                      </div>\r\n                    );\r\n                  })}\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Mobile Timeline */}\r\n            <div className=\"lg:hidden space-y-6\">\r\n              {steps.map((step, index) => {\r\n                const Icon = step.icon;\r\n                const isActive = activeStep >= step.id;\r\n                const isCurrent = activeStep === step.id;\r\n                \r\n                return (\r\n                  <div\r\n                    key={step.id}\r\n                    className=\"relative\"\r\n                  >\r\n                    <Card className={`border-2 ${isCurrent ? step.borderColor : 'border-gray-100'}`}>\r\n                      <CardContent className=\"p-6\">\r\n                        <div className=\"flex items-start space-x-4\">\r\n                          <div className={`w-12 h-12 rounded-full flex items-center justify-center flex-shrink-0 ${\r\n                            isActive ? 'bg-primary text-white' : 'bg-gray-100 text-gray-400'\r\n                          }`}>\r\n                            <Icon className=\"w-6 h-6\" />\r\n                          </div>\r\n                          \r\n                          <div className=\"flex-1\">\r\n                            <div className=\"flex items-center justify-between mb-2\">\r\n                              <h3 className={`font-bold text-lg ${isCurrent ? 'text-primary' : 'text-gray-900'}`}>\r\n                                {step.title}\r\n                              </h3>\r\n                              {/* <span className=\"text-xs text-gray-500 flex items-center\">\r\n                                <Clock className=\"w-3 h-3 mr-1\" />\r\n                                {step.duration}\r\n                              </span> */}\r\n                            </div>\r\n                            <p className=\"text-gray-600 text-sm leading-relaxed\">\r\n                              {step.description}\r\n                            </p>\r\n                          </div>\r\n                        </div>\r\n                      </CardContent>\r\n                    </Card>\r\n\r\n                    {/* Connection Line */}\r\n                    {index < steps.length - 1 && (\r\n                      <div className=\"flex justify-center\">\r\n                        <div className={`w-1 h-6 ${isActive ? 'bg-primary' : 'bg-gray-200'}`}></div>\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                );\r\n              })}\r\n            </div>\r\n          </div>\r\n\r\n          {/* Active Step Details */}\r\n          <div>\r\n            {steps.map((step) => {\r\n              if (step.id !== activeStep) return null;\r\n              \r\n              return (\r\n                <Card key={step.id} className={`border-2 ${step.borderColor} ${step.bgColor}`}>\r\n                  <CardContent className=\"p-8\">\r\n                    <div className=\"grid md:grid-cols-2 gap-8 items-center\">\r\n                      <div className=\"space-y-6\">\r\n                        <div className=\"space-y-3\">\r\n                          <div className=\"flex items-center space-x-3\">\r\n                            <div className={`w-12 h-12 bg-white rounded-lg flex items-center justify-center`}>\r\n                              <step.icon className={`w-6 h-6 ${step.color}`} />\r\n                            </div>\r\n                            <div>\r\n                              <h3 className=\"text-3xl font-bold text-gray-900\">\r\n                                Étape {step.id}: {step.title}\r\n                              </h3>\r\n                              {/* <div className=\"flex items-center text-sm text-gray-600\">\r\n                                <Clock className=\"w-4 h-4 mr-1\" />\r\n                                Durée: {step.duration}\r\n                              </div> */}\r\n                            </div>\r\n                          </div>\r\n                          \r\n                          <p className=\"text-gray-700 leading-relaxed\">\r\n                            {step.description}\r\n                          </p>\r\n                        </div>\r\n\r\n                        <div className=\"space-y-3\">\r\n                          <h4 className=\"font-semibold text-gray-900\">Actions réalisées :</h4>\r\n                          <ul className=\"space-y-2\">\r\n                            {step.details.map((detail, detailIndex) => (\r\n                              <li key={detailIndex} className=\"flex items-start space-x-3 text-sm text-gray-700\">\r\n                                <CheckCircle className={`w-4 h-4 ${step.color} flex-shrink-0 mt-0.5`} />\r\n                                <span>{detail}</span>\r\n                              </li>\r\n                            ))}\r\n                          </ul>\r\n                        </div>\r\n                      </div>\r\n\r\n                      <div className=\"text-center\">\r\n                        <div className=\"w-48 h-48 bg-white/50 rounded-full flex items-center justify-center mx-auto mb-6\">\r\n                          <step.icon className={`w-24 h-24 ${step.color}`} />\r\n                        </div>\r\n                        <p className=\"text-sm text-gray-600\">\r\n                          Étape {step.id} sur 4\r\n                        </p>\r\n                      </div>\r\n                    </div>\r\n                  </CardContent>\r\n                </Card>\r\n              );\r\n            })}\r\n          </div>\r\n\r\n          {/* CTA Section */}\r\n          <div className=\"text-center mt-16\">\r\n            <div className=\"bg-gray-50 rounded-2xl p-8\">\r\n              <h3 className=\"text-2xl font-bold text-gray-900 mb-4\">\r\n                Prêt à commencer votre démarche ?\r\n              </h3>\r\n              <p className=\"text-gray-600 mb-6 max-w-2xl mx-auto\">\r\n                Nos experts vous accompagnent à chaque étape pour garantir \r\n                le succès de votre projet foncier.\r\n              </p>\r\n              <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\r\n                <Button asChild size=\"lg\" className=\"group\">\r\n                  <Link href=\"/formulaire\" className=\"flex items-center\">\r\n                    Commencer maintenant\r\n                    <ArrowRight className=\"ml-2 h-4 w-4\" />\r\n                  </Link>\r\n                </Button>\r\n                {/* <Button asChild variant=\"outline\" size=\"lg\">\r\n                  <Link href=\"#ressources\">\r\n                    Télécharger le guide\r\n                  </Link>\r\n                </Button> */}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;;;AAPA;;;;;;;AASO,SAAS;;IACd,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,iBAAiB,CAAA,GAAA,iIAAA,CAAA,oBAAiB,AAAD,IAAI,OAAO;IAElD,MAAM,UAAU;QAAE,QAAA,6RAAA,CAAA,SAAM;QAAE,QAAA,iSAAA,CAAA,SAAM;QAAE,UAAA,qSAAA,CAAA,WAAQ;QAAE,OAAA,2RAAA,CAAA,QAAK;IAAC;IAClD,MAAM,QAAQ,gBAAgB,OAAO,IAAI,CAAC,OAAc,CAAC;YACvD,GAAG,IAAI;YACP,MAAM,OAAO,CAAC,KAAK,IAAI,CAAC,IAAI,qSAAA,CAAA,WAAQ;QACtC,CAAC,MAAM,EAAE;IAET,qBACE,4TAAC;QAAQ,IAAG;QAAY,WAAU;kBAChC,cAAA,4TAAC;YAAI,WAAU;sBACb,cAAA,4TAAC;gBAAI,WAAU;;kCAEb,4TAAC;wBAAI,WAAU;;0CACb,4TAAC;gCAAI,WAAU;0CAAiG;;;;;;0CAGhH,4TAAC;gCAAG,WAAU;0CACX,gBAAgB,SAAS;;;;;;0CAE5B,4TAAC;gCAAE,WAAU;0CACV,gBAAgB,YAAY;;;;;;;;;;;;kCAKjC,4TAAC;wBAAI,WAAU;;0CAEb,4TAAC;gCAAI,WAAU;0CACb,cAAA,4TAAC;oCAAI,WAAU;;sDAEb,4TAAC;4CAAI,WAAU;;;;;;sDACf,4TAAC;4CACC,WAAU;4CACV,OAAO;gDAAE,OAAO,GAAG,AAAC,aAAa,IAAK,IAAI,CAAC,CAAC;4CAAC;;;;;;sDAI/C,4TAAC;4CAAI,WAAU;sDACZ,MAAM,GAAG,CAAC,CAAC,MAAM;gDAChB,MAAM,OAAO,KAAK,IAAI;gDACtB,MAAM,WAAW,cAAc,KAAK,EAAE;gDACtC,MAAM,YAAY,eAAe,KAAK,EAAE;gDAExC,qBACE,4TAAC;oDAEC,WAAU;oDACV,SAAS,IAAM,cAAc,KAAK,EAAE;;sEAGpC,4TAAC;4DAAI,WAAW,CAAC,wDAAwD,EACvE,WACI,oCACA,mDACJ;sEACA,cAAA,4TAAC;gEAAK,WAAU;;;;;;;;;;;sEAIlB,4TAAC;4DAAI,WAAU;;8EACb,4TAAC;oEAAG,WAAW,CAAC,kBAAkB,EAChC,YAAY,iBAAiB,iBAC7B;8EACC,KAAK,KAAK;;;;;;8EAEb,4TAAC;oEAAE,WAAU;;wEACV,KAAK,WAAW,CAAC,KAAK,CAAC,GAAG;wEAAM,KAAK,WAAW,CAAC,MAAM,GAAG,MAAM,QAAQ;;;;;;;;;;;;;;mDArBxE,KAAK,EAAE;;;;;4CA8BlB;;;;;;;;;;;;;;;;;0CAMN,4TAAC;gCAAI,WAAU;0CACZ,MAAM,GAAG,CAAC,CAAC,MAAM;oCAChB,MAAM,OAAO,KAAK,IAAI;oCACtB,MAAM,WAAW,cAAc,KAAK,EAAE;oCACtC,MAAM,YAAY,eAAe,KAAK,EAAE;oCAExC,qBACE,4TAAC;wCAEC,WAAU;;0DAEV,4TAAC,4HAAA,CAAA,OAAI;gDAAC,WAAW,CAAC,SAAS,EAAE,YAAY,KAAK,WAAW,GAAG,mBAAmB;0DAC7E,cAAA,4TAAC,4HAAA,CAAA,cAAW;oDAAC,WAAU;8DACrB,cAAA,4TAAC;wDAAI,WAAU;;0EACb,4TAAC;gEAAI,WAAW,CAAC,sEAAsE,EACrF,WAAW,0BAA0B,6BACrC;0EACA,cAAA,4TAAC;oEAAK,WAAU;;;;;;;;;;;0EAGlB,4TAAC;gEAAI,WAAU;;kFACb,4TAAC;wEAAI,WAAU;kFACb,cAAA,4TAAC;4EAAG,WAAW,CAAC,kBAAkB,EAAE,YAAY,iBAAiB,iBAAiB;sFAC/E,KAAK,KAAK;;;;;;;;;;;kFAOf,4TAAC;wEAAE,WAAU;kFACV,KAAK,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;4CAQ1B,QAAQ,MAAM,MAAM,GAAG,mBACtB,4TAAC;gDAAI,WAAU;0DACb,cAAA,4TAAC;oDAAI,WAAW,CAAC,QAAQ,EAAE,WAAW,eAAe,eAAe;;;;;;;;;;;;uCAjCnE,KAAK,EAAE;;;;;gCAsClB;;;;;;;;;;;;kCAKJ,4TAAC;kCACE,MAAM,GAAG,CAAC,CAAC;4BACV,IAAI,KAAK,EAAE,KAAK,YAAY,OAAO;4BAEnC,qBACE,4TAAC,4HAAA,CAAA,OAAI;gCAAe,WAAW,CAAC,SAAS,EAAE,KAAK,WAAW,CAAC,CAAC,EAAE,KAAK,OAAO,EAAE;0CAC3E,cAAA,4TAAC,4HAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,4TAAC;wCAAI,WAAU;;0DACb,4TAAC;gDAAI,WAAU;;kEACb,4TAAC;wDAAI,WAAU;;0EACb,4TAAC;gEAAI,WAAU;;kFACb,4TAAC;wEAAI,WAAW,CAAC,8DAA8D,CAAC;kFAC9E,cAAA,4TAAC,KAAK,IAAI;4EAAC,WAAW,CAAC,QAAQ,EAAE,KAAK,KAAK,EAAE;;;;;;;;;;;kFAE/C,4TAAC;kFACC,cAAA,4TAAC;4EAAG,WAAU;;gFAAmC;gFACxC,KAAK,EAAE;gFAAC;gFAAG,KAAK,KAAK;;;;;;;;;;;;;;;;;;0EASlC,4TAAC;gEAAE,WAAU;0EACV,KAAK,WAAW;;;;;;;;;;;;kEAIrB,4TAAC;wDAAI,WAAU;;0EACb,4TAAC;gEAAG,WAAU;0EAA8B;;;;;;0EAC5C,4TAAC;gEAAG,WAAU;0EACX,KAAK,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,4BACzB,4TAAC;wEAAqB,WAAU;;0FAC9B,4TAAC,kTAAA,CAAA,cAAW;gFAAC,WAAW,CAAC,QAAQ,EAAE,KAAK,KAAK,CAAC,qBAAqB,CAAC;;;;;;0FACpE,4TAAC;0FAAM;;;;;;;uEAFA;;;;;;;;;;;;;;;;;;;;;;0DASjB,4TAAC;gDAAI,WAAU;;kEACb,4TAAC;wDAAI,WAAU;kEACb,cAAA,4TAAC,KAAK,IAAI;4DAAC,WAAW,CAAC,UAAU,EAAE,KAAK,KAAK,EAAE;;;;;;;;;;;kEAEjD,4TAAC;wDAAE,WAAU;;4DAAwB;4DAC5B,KAAK,EAAE;4DAAC;;;;;;;;;;;;;;;;;;;;;;;;+BA3Cd,KAAK,EAAE;;;;;wBAkDtB;;;;;;kCAIF,4TAAC;wBAAI,WAAU;kCACb,cAAA,4TAAC;4BAAI,WAAU;;8CACb,4TAAC;oCAAG,WAAU;8CAAwC;;;;;;8CAGtD,4TAAC;oCAAE,WAAU;8CAAuC;;;;;;8CAIpD,4TAAC;oCAAI,WAAU;8CACb,cAAA,4TAAC,8HAAA,CAAA,SAAM;wCAAC,OAAO;wCAAC,MAAK;wCAAK,WAAU;kDAClC,cAAA,4TAAC,8RAAA,CAAA,UAAI;4CAAC,MAAK;4CAAc,WAAU;;gDAAoB;8DAErD,4TAAC,ySAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAe1C;GA9NgB;KAAA", "debugId": null}}, {"offset": {"line": 711, "column": 0}, "map": {"version": 3, "file": "search.js", "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/charlie-oscar-consulting/website/node_modules/.pnpm/lucide-react%400.503.0_react%4019.1.0/node_modules/lucide-react/src/icons/search.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '11', cy: '11', r: '8', key: '4ej97u' }],\n  ['path', { d: 'm21 21-4.3-4.3', key: '1qie3q' }],\n];\n\n/**\n * @component @name Search\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMSIgY3k9IjExIiByPSI4IiAvPgogIDxwYXRoIGQ9Im0yMSAyMS00LjMtNC4zIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/search\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Search = createLucideIcon('search', __iconNode);\n\nexport default Search;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA,CAAA;IAClC;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA;YAAK,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA,CAAA;IACxD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAkB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CACjD,CAAA;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAS,CAAA,+PAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAU,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 759, "column": 0}, "map": {"version": 3, "file": "file-text.js", "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/charlie-oscar-consulting/website/node_modules/.pnpm/lucide-react%400.503.0_react%4019.1.0/node_modules/lucide-react/src/icons/file-text.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z', key: '1rqfz7' }],\n  ['path', { d: 'M14 2v4a2 2 0 0 0 2 2h4', key: 'tnqrlb' }],\n  ['path', { d: 'M10 9H8', key: 'b1mrlr' }],\n  ['path', { d: 'M16 13H8', key: 't4e002' }],\n  ['path', { d: 'M16 17H8', key: 'z1uh3a' }],\n];\n\n/**\n * @component @name FileText\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTUgMkg2YTIgMiAwIDAgMC0yIDJ2MTZhMiAyIDAgMCAwIDIgMmgxMmEyIDIgMCAwIDAgMi0yVjdaIiAvPgogIDxwYXRoIGQ9Ik0xNCAydjRhMiAyIDAgMCAwIDIgMmg0IiAvPgogIDxwYXRoIGQ9Ik0xMCA5SDgiIC8+CiAgPHBhdGggZD0iTTE2IDEzSDgiIC8+CiAgPHBhdGggZD0iTTE2IDE3SDgiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/file-text\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst FileText = createLucideIcon('file-text', __iconNode);\n\nexport default FileText;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA8D,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA,CAAA;IAC3F;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA2B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA,CAAA;IACxD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA,CAAA;IACxC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA,CAAA;IACzC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAC3C,CAAA;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAW,CAAA,+PAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAa,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 826, "column": 0}, "map": {"version": 3, "file": "award.js", "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/charlie-oscar-consulting/website/node_modules/.pnpm/lucide-react%400.503.0_react%4019.1.0/node_modules/lucide-react/src/icons/award.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'm15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526',\n      key: '1yiouv',\n    },\n  ],\n  ['circle', { cx: '12', cy: '8', r: '6', key: '1vp47v' }],\n];\n\n/**\n * @component @name Award\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTUuNDc3IDEyLjg5IDEuNTE1IDguNTI2YS41LjUgMCAwIDEtLjgxLjQ3bC0zLjU4LTIuNjg3YTEgMSAwIDAgMC0xLjE5NyAwbC0zLjU4NiAyLjY4NmEuNS41IDAgMCAxLS44MS0uNDY5bDEuNTE0LTguNTI2IiAvPgogIDxjaXJjbGUgY3g9IjEyIiBjeT0iOCIgcj0iNiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/award\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Award = createLucideIcon('award', __iconNode);\n\nexport default Award;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA,CAAA;IAClC,CAAA;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA,CAAA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,CAAA,CAAA,CAAK,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACP,CAAA;KACF,CAAA;IACA;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,GAAA,CAAK;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA;YAAK,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;CACzD,CAAA;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAQ,CAAA,+PAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 874, "column": 0}, "map": {"version": 3, "file": "circle-check-big.js", "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/charlie-oscar-consulting/website/node_modules/.pnpm/lucide-react%400.503.0_react%4019.1.0/node_modules/lucide-react/src/icons/circle-check-big.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M21.801 10A10 10 0 1 1 17 3.335', key: 'yps3ct' }],\n  ['path', { d: 'm9 11 3 3L22 4', key: '1pflzl' }],\n];\n\n/**\n * @component @name CircleCheckBig\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjEuODAxIDEwQTEwIDEwIDAgMSAxIDE3IDMuMzM1IiAvPgogIDxwYXRoIGQ9Im05IDExIDMgM0wyMiA0IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/circle-check-big\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst CircleCheckBig = createLucideIcon('circle-check-big', __iconNode);\n\nexport default CircleCheckBig;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAmC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA,CAAA;IAChE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAkB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CACjD,CAAA;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,cAAA,CAAiB,CAAA,+PAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,AAAjB,CAAA,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAoB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}]}