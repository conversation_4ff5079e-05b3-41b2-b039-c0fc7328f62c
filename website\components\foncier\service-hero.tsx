import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { FoncierService } from "@/lib/foncier-services-data";
import { ArrowRight, ChevronRight, Home, Star } from "lucide-react";

interface ServiceHeroProps {
  service: FoncierService;
}

export function ServiceHero({ service }: ServiceHeroProps) {
  return (
    <section className="relative bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-white py-20 overflow-hidden w-full min-h-[60vh] flex items-center" style={{backgroundImage: `url('/your-background-image.jpg')`, backgroundSize: 'cover', backgroundPosition: 'center'}}>
      <div className="absolute inset-0 opacity-60 bg-black" />
      <div className="container mx-auto px-4 relative z-10">
        <div className="max-w-6xl mx-auto">
          {/* Breadcrumb */}
          <div className="mb-8">
            <nav className="flex items-center space-x-2 text-sm text-gray-300">
              <Link href="/" className="hover:text-white transition-colors flex items-center">
                <Home className="w-4 h-4 mr-1" />
                Accueil
              </Link>
              <ChevronRight className="w-4 h-4" />
              <Link href="/foncier" className="hover:text-white transition-colors">
                Foncier
              </Link>
              <ChevronRight className="w-4 h-4" />
              <Link href="/foncier/services" className="hover:text-white transition-colors">
                Services
              </Link>
              <ChevronRight className="w-4 h-4" />
              <span className="text-primary">{service.title}</span>
            </nav>
          </div>

          <div className="space-y-8">
            {/* Service Category */}
            <div className="flex items-center space-x-3">
              <Badge variant="secondary" className="bg-primary/20 text-primary-foreground border-primary/30">
                {service.category}
              </Badge>
              {service.featured && (
                <Badge variant="secondary" className="bg-yellow-500/20 text-yellow-300 border-yellow-500/30 flex items-center">
                  <Star className="w-3 h-3 mr-1 fill-current" />
                  Service phare
                </Badge>
              )}
            </div>

            {/* Title */}
            <div className="space-y-4">
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold leading-tight">
                {service.title}
              </h1>
              <p className="text-xl md:text-2xl text-gray-300 leading-relaxed">
                {service.shortDescription}
              </p>
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4">
              <Button asChild size="lg" className="bg-primary hover:bg-primary/90 text-white group">
                <Link href={`/formulaire?service=${service.slug}`} className="flex items-center">
                  Demander un devis
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
              </Button>
              <Button asChild size="lg" variant="outline" className="border-white text-primary hover:bg-white hover:text-gray-900 px-8 py-4">
                <Link href="/contact">
                  Contactez-nous
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
