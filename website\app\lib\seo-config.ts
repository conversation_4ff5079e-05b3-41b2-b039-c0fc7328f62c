import { Metadata } from 'next';

// Base URL configuration
export const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL || 'https://charlie-oscar-consulting.com';

// Default SEO configuration
export const DEFAULT_SEO = {
  title: '<PERSON> | Foncier, Immobilier & Construction au Cameroun',
  description: 'Expert en foncier, immobilier et construction au Cameroun. Sécurisez vos droits fonciers, achetez/vendez vos biens immobiliers et réalisez vos projets de construction avec notre expertise reconnue.',
  keywords: [
    'foncier Cameroun',
    'titre foncier',
    'immobilier Cameroun',
    'construction Cameroun',
    'sécurisation foncière',
    '<PERSON>',
    'expertise foncière',
    'conseil immobilier',
    'Yaoundé',
    'Douala'
  ],
  author: '<PERSON>',
  creator: '<PERSON> Consul<PERSON>',
  publisher: '<PERSON> Consul<PERSON>',
  robots: 'index, follow',
  language: 'fr-CM',
  region: 'CM',
  country: 'Cameroun'
};

// Open Graph default configuration
export const DEFAULT_OG = {
  type: 'website' as const,
  locale: 'fr_CM',
  siteName: '<PERSON> Consulting',
  images: [
    {
      url: `${BASE_URL}/images/seo/og-default.jpg`,
      width: 1200,
      height: 630,
      alt: 'Charlie Oscar Consulting - Expert en Foncier, Immobilier & Construction'
    }
  ]
};

// Twitter Card default configuration
export const DEFAULT_TWITTER = {
  card: 'summary_large_image' as const,
  site: '@CharlieOscarCM',
  creator: '@CharlieOscarCM',
  images: [`${BASE_URL}/images/seo/twitter-card.jpg`]
};

// Generate metadata for pages
export function generateMetadata({
  title,
  description,
  keywords = [],
  canonical,
  ogImage,
  noIndex = false
}: {
  title?: string;
  description?: string;
  keywords?: string[];
  canonical?: string;
  ogImage?: string;
  noIndex?: boolean;
}): Metadata {
  const fullTitle = title ? `${title} | Charlie Oscar Consulting` : DEFAULT_SEO.title;
  const fullDescription = description || DEFAULT_SEO.description;
  const allKeywords = [...DEFAULT_SEO.keywords, ...keywords];
  const canonicalUrl = canonical ? `${BASE_URL}${canonical}` : BASE_URL;
  const imageUrl = ogImage ? `${BASE_URL}${ogImage}` : DEFAULT_OG.images[0].url;

  return {
    title: fullTitle,
    description: fullDescription,
    keywords: allKeywords.join(', '),
    authors: [{ name: DEFAULT_SEO.author }],
    creator: DEFAULT_SEO.creator,
    publisher: DEFAULT_SEO.publisher,
    robots: noIndex ? 'noindex, nofollow' : DEFAULT_SEO.robots,
    alternates: {
      canonical: canonicalUrl,
      languages: {
        'fr-CM': canonicalUrl,
        'fr': canonicalUrl
      }
    },
    openGraph: {
      title: fullTitle,
      description: fullDescription,
      url: canonicalUrl,
      siteName: DEFAULT_OG.siteName,
      locale: DEFAULT_OG.locale,
      type: DEFAULT_OG.type,
      images: [
        {
          url: imageUrl,
          width: 1200,
          height: 630,
          alt: fullTitle
        }
      ]
    },
    twitter: {
      ...DEFAULT_TWITTER,
      title: fullTitle,
      description: fullDescription,
      images: [imageUrl]
    },
    verification: {
      google: process.env.GOOGLE_SITE_VERIFICATION,
      other: {
        'facebook-domain-verification': process.env.FACEBOOK_DOMAIN_VERIFICATION || ''
      }
    },
    category: 'business'
  };
}

// Structured data generators
export function generateOrganizationSchema() {
  return {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name: 'Charlie Oscar Consulting',
    description: DEFAULT_SEO.description,
    url: BASE_URL,
    logo: `${BASE_URL}/logo.svg`,
    contactPoint: {
      '@type': 'ContactPoint',
      telephone: '+***********-000',
      contactType: 'customer service',
      availableLanguage: ['French']
    },
    address: {
      '@type': 'PostalAddress',
      addressCountry: 'CM',
      addressRegion: 'Centre',
      addressLocality: 'Yaoundé'
    },
    sameAs: [
      'https://www.facebook.com/charlieoscarconsulting',
      'https://www.linkedin.com/company/charlie-oscar-consulting'
    ],
    areaServed: {
      '@type': 'Country',
      name: 'Cameroun'
    },
    serviceType: [
      'Services Fonciers',
      'Services Immobiliers',
      'Services de Construction'
    ]
  };
}

export function generateWebsiteSchema() {
  return {
    '@context': 'https://schema.org',
    '@type': 'WebSite',
    name: 'Charlie Oscar Consulting',
    url: BASE_URL,
    description: DEFAULT_SEO.description,
    inLanguage: 'fr-CM',
    potentialAction: {
      '@type': 'SearchAction',
      target: `${BASE_URL}/search?q={search_term_string}`,
      'query-input': 'required name=search_term_string'
    }
  };
}

export function generateServiceSchema(service: {
  name: string;
  description: string;
  url: string;
  serviceType: string;
}) {
  return {
    '@context': 'https://schema.org',
    '@type': 'Service',
    name: service.name,
    description: service.description,
    url: `${BASE_URL}${service.url}`,
    serviceType: service.serviceType,
    provider: {
      '@type': 'Organization',
      name: 'Charlie Oscar Consulting',
      url: BASE_URL
    },
    areaServed: {
      '@type': 'Country',
      name: 'Cameroun'
    }
  };
}
