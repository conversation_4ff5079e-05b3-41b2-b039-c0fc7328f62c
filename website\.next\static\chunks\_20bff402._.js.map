{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/charlie-oscar-consulting/website/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst badgeVariants = cva(\r\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\r\n        secondary:\r\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\r\n        destructive:\r\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\r\n        outline:\r\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nfunction Badge({\r\n  className,\r\n  variant,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"span\"> &\r\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\r\n  const Comp = asChild ? Slot : \"span\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"badge\"\r\n      className={cn(badgeVariants({ variant }), className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Badge, badgeVariants }\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,8OAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,uSAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,4TAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 59, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/charlie-oscar-consulting/website/components/foncier/service-hero.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect } from \"react\";\r\nimport Link from \"next/link\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport { FoncierService } from \"@/lib/foncier-services-data\";\r\nimport { ArrowRight, ChevronRight, Home, Star } from \"lucide-react\";\r\n\r\ninterface ServiceHeroProps {\r\n  service: FoncierService;\r\n}\r\n\r\nexport function ServiceHero({ service }: ServiceHeroProps) {\r\n  const [isVisible, setIsVisible] = useState(false);\r\n\r\n  useEffect(() => {\r\n    setIsVisible(true);\r\n  }, []);\r\n\r\n  return (\r\n    <section className=\"relative bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-white py-20 overflow-hidden w-full min-h-[60vh] flex items-center\" style={{backgroundImage: `url('/your-background-image.jpg')`, backgroundSize: 'cover', backgroundPosition: 'center'}}>\r\n      <div className=\"absolute inset-0 opacity-60 bg-black\" />\r\n      <div className=\"container mx-auto px-4 relative z-10\">\r\n        <div className=\"max-w-6xl mx-auto\">\r\n          {/* Breadcrumb */}\r\n          <div className={`mb-8 transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`}>\r\n            <nav className=\"flex items-center space-x-2 text-sm text-gray-300\">\r\n              <Link href=\"/\" className=\"hover:text-white transition-colors flex items-center\">\r\n                <Home className=\"w-4 h-4 mr-1\" />\r\n                Accueil\r\n              </Link>\r\n              <ChevronRight className=\"w-4 h-4\" />\r\n              <Link href=\"/foncier\" className=\"hover:text-white transition-colors\">\r\n                Foncier\r\n              </Link>\r\n              <ChevronRight className=\"w-4 h-4\" />\r\n              <Link href=\"/foncier/services\" className=\"hover:text-white transition-colors\">\r\n                Services\r\n              </Link>\r\n              <ChevronRight className=\"w-4 h-4\" />\r\n              <span className=\"text-primary\">{service.title}</span>\r\n            </nav>\r\n          </div>\r\n\r\n          <div className={`space-y-8 transition-all duration-1000 delay-200 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}> \r\n            {/* Service Category */}\r\n            <div className=\"flex items-center space-x-3\">\r\n              <Badge variant=\"secondary\" className=\"bg-primary/20 text-primary-foreground border-primary/30\">\r\n                {service.category}\r\n              </Badge>\r\n              {service.featured && (\r\n                <Badge variant=\"secondary\" className=\"bg-yellow-500/20 text-yellow-300 border-yellow-500/30 flex items-center\">\r\n                  <Star className=\"w-3 h-3 mr-1 fill-current\" />\r\n                  Service phare\r\n                </Badge>\r\n              )}\r\n            </div>\r\n\r\n            {/* Title */}\r\n            <div className=\"space-y-4\">\r\n              <h1 className=\"text-4xl md:text-5xl lg:text-6xl font-bold leading-tight\">\r\n                {service.title}\r\n              </h1>\r\n              <p className=\"text-xl md:text-2xl text-gray-300 leading-relaxed\">\r\n                {service.shortDescription}\r\n              </p>\r\n            </div>\r\n\r\n            {/* CTA Buttons */}\r\n            <div className=\"flex flex-col sm:flex-row gap-4\">\r\n              <Button asChild size=\"lg\" className=\"bg-primary hover:bg-primary/90 text-white group\">\r\n                <Link href={`/formulaire?service=${service.slug}`} className=\"flex items-center\">\r\n                  Demander un devis\r\n                  <ArrowRight className=\"ml-2 h-5 w-5 transition-transform group-hover:translate-x-1\" />\r\n                </Link>\r\n              </Button>\r\n              <Button asChild size=\"lg\" variant=\"outline\" className=\"border-white text-primary hover:bg-white hover:text-gray-900 px-8 py-4\">\r\n                <Link href=\"/contact\">\r\n                  Contactez-nous\r\n                </Link>\r\n              </Button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;;;AAPA;;;;;;AAaO,SAAS,YAAY,EAAE,OAAO,EAAoB;;IACvD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,4RAAA,CAAA,YAAS,AAAD;iCAAE;YACR,aAAa;QACf;gCAAG,EAAE;IAEL,qBACE,4TAAC;QAAQ,WAAU;QAA2I,OAAO;YAAC,iBAAiB,CAAC,iCAAiC,CAAC;YAAE,gBAAgB;YAAS,oBAAoB;QAAQ;;0BAC/Q,4TAAC;gBAAI,WAAU;;;;;;0BACf,4TAAC;gBAAI,WAAU;0BACb,cAAA,4TAAC;oBAAI,WAAU;;sCAEb,4TAAC;4BAAI,WAAW,CAAC,kCAAkC,EAAE,YAAY,8BAA8B,2BAA2B;sCACxH,cAAA,4TAAC;gCAAI,WAAU;;kDACb,4TAAC,8RAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;;0DACvB,4TAAC,0RAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGnC,4TAAC,6SAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;kDACxB,4TAAC,8RAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAAqC;;;;;;kDAGrE,4TAAC,6SAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;kDACxB,4TAAC,8RAAA,CAAA,UAAI;wCAAC,MAAK;wCAAoB,WAAU;kDAAqC;;;;;;kDAG9E,4TAAC,6SAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;kDACxB,4TAAC;wCAAK,WAAU;kDAAgB,QAAQ,KAAK;;;;;;;;;;;;;;;;;sCAIjD,4TAAC;4BAAI,WAAW,CAAC,iDAAiD,EAAE,YAAY,8BAA8B,2BAA2B;;8CAEvI,4TAAC;oCAAI,WAAU;;sDACb,4TAAC,6HAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAY,WAAU;sDAClC,QAAQ,QAAQ;;;;;;wCAElB,QAAQ,QAAQ,kBACf,4TAAC,6HAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAY,WAAU;;8DACnC,4TAAC,yRAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAA8B;;;;;;;;;;;;;8CAOpD,4TAAC;oCAAI,WAAU;;sDACb,4TAAC;4CAAG,WAAU;sDACX,QAAQ,KAAK;;;;;;sDAEhB,4TAAC;4CAAE,WAAU;sDACV,QAAQ,gBAAgB;;;;;;;;;;;;8CAK7B,4TAAC;oCAAI,WAAU;;sDACb,4TAAC,8HAAA,CAAA,SAAM;4CAAC,OAAO;4CAAC,MAAK;4CAAK,WAAU;sDAClC,cAAA,4TAAC,8RAAA,CAAA,UAAI;gDAAC,MAAM,CAAC,oBAAoB,EAAE,QAAQ,IAAI,EAAE;gDAAE,WAAU;;oDAAoB;kEAE/E,4TAAC,ySAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;;;;;;;sDAG1B,4TAAC,8HAAA,CAAA,SAAM;4CAAC,OAAO;4CAAC,MAAK;4CAAK,SAAQ;4CAAU,WAAU;sDACpD,cAAA,4TAAC,8RAAA,CAAA,UAAI;gDAAC,MAAK;0DAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUtC;GA3EgB;KAAA", "debugId": null}}, {"offset": {"line": 343, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/charlie-oscar-consulting/website/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card\"\r\n      className={cn(\r\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-header\"\r\n      className={cn(\r\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-title\"\r\n      className={cn(\"leading-none font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-action\"\r\n      className={cn(\r\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-content\"\r\n      className={cn(\"px-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-footer\"\r\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Card,\r\n  CardHeader,\r\n  CardFooter,\r\n  CardTitle,\r\n  CardAction,\r\n  CardDescription,\r\n  CardContent,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,4TAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,4TAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,4TAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,4TAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,4TAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,4TAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,4TAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 463, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/charlie-oscar-consulting/website/app/cms/utils/foncier.ts"], "sourcesContent": ["import foncier from \"@/app/cms/data/foncier.json\";\r\n\r\nexport function getFoncierContent() {\r\n  return foncier;\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;;AAEO,SAAS;IACd,OAAO,qGAAA,CAAA,UAAO;AAChB", "debugId": null}}, {"offset": {"line": 480, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/charlie-oscar-consulting/website/components/foncier/process-section.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect, useRef } from \"react\";\r\nimport Link from \"next/link\";\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport { Card, CardContent } from \"@/components/ui/card\";\r\nimport { Search, MapPin, FileText, Award, ArrowRight, CheckCircle, Clock } from \"lucide-react\";\r\nimport { getFoncierContent } from \"@/app/cms/utils/foncier\";\r\n\r\nexport function ProcessSection() {\r\n  const [isVisible, setIsVisible] = useState(false);\r\n  const [activeStep, setActiveStep] = useState(1);\r\n  const sectionRef = useRef<HTMLElement>(null);\r\n  const processContent = getFoncierContent().process;\r\n\r\n  useEffect(() => {\r\n    const observer = new IntersectionObserver(\r\n      ([entry]) => {\r\n        if (entry.isIntersecting) {\r\n          setIsVisible(true);\r\n        }\r\n      },\r\n      { threshold: 0.2 }\r\n    );\r\n\r\n    if (sectionRef.current) {\r\n      observer.observe(sectionRef.current);\r\n    }\r\n\r\n    return () => observer.disconnect();\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    const interval = setInterval(() => {\r\n      setActiveStep(prev => prev === 4 ? 1 : prev + 1);\r\n    }, 3000);\r\n\r\n    return () => clearInterval(interval);\r\n  }, []);\r\n\r\n  const iconMap = { Search, MapPin, FileText, Award };\r\n  const steps = processContent?.steps?.map((step: any) => ({\r\n    ...step,\r\n    icon: iconMap[step.icon] || FileText\r\n  })) || [];\r\n\r\n  return (\r\n    <section ref={sectionRef} id=\"processus\" className=\"py-20 bg-white\">\r\n      <div className=\"container mx-auto px-4\">\r\n        <div className=\"max-w-6xl mx-auto\">\r\n          {/* Section Header */}\r\n          <div className={`text-center space-y-4 mb-16 transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>\r\n            <div className=\"inline-flex items-center px-4 py-2 bg-primary/10 text-primary rounded-full text-sm font-medium\">\r\n              Notre méthode\r\n            </div>\r\n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900\">\r\n              {processContent?.title || \"Comment ça marche ?\"}\r\n            </h2>\r\n            <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\r\n              {processContent?.subtitle || \"Notre processus éprouvé en 4 étapes vous garantit l'obtention de votre titre foncier dans les meilleures conditions.\"}\r\n            </p>\r\n          </div>\r\n\r\n          {/* Timeline */}\r\n          <div className={`mb-16 transition-all duration-1000 delay-300 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>\r\n            {/* Desktop Timeline */}\r\n            <div className=\"hidden lg:block\">\r\n              <div className=\"relative \">\r\n                {/* Timeline Line */}\r\n                <div className=\"absolute top-1/2 my-5 left-0 right-0 h-1 bg-gray-200 transform -translate-y-1/2\"></div>\r\n                <div \r\n                  className=\"absolute top-1/2 left-0 my-5 h-1 bg-primary transform -translate-y-1/2 transition-all duration-1000\"\r\n                  style={{ width: `${(activeStep / 4) * 100}%` }}\r\n                ></div>\r\n\r\n                {/* Steps */}\r\n                <div className=\"relative flex justify-between\">\r\n                  {steps.map((step, index) => {\r\n                    const Icon = step.icon;\r\n                    const isActive = activeStep >= step.id;\r\n                    const isCurrent = activeStep === step.id;\r\n                    \r\n                    return (\r\n                      <div\r\n                        key={step.id}\r\n                        className=\"flex flex-col items-center cursor-pointer group\"\r\n                        onClick={() => setActiveStep(step.id)}\r\n                      >\r\n                        {/* Step Circle */}\r\n                        <div className={`w-16 h-16 rounded-full flex items-center justify-center transition-all duration-300 ${\r\n                          isActive \r\n                            ? 'bg-primary text-white shadow-lg scale-110' \r\n                            : 'bg-white border-2 border-gray-300 text-gray-400 group-hover:border-primary group-hover:text-primary'\r\n                        }`}>\r\n                          <Icon className=\"w-7 h-7\" />\r\n                        </div>\r\n\r\n                        {/* Step Info */}\r\n                        <div className=\"mt-4 text-center max-w-xs\">\r\n                          <h3 className={`font-bold text-md transition-colors duration-300 ${\r\n                            isCurrent ? 'text-primary' : 'text-gray-900'\r\n                          }`}>\r\n                            {step.title}\r\n                          </h3>\r\n                          <p className=\"text-sm text-gray-600 mt-2 leading-relaxed\">\r\n                            {step.description.slice(0, 100)}{step.description.length > 100 ? '...' : ''}\r\n                          </p>\r\n                          {/* <div className=\"flex items-center justify-center mt-2 text-xs text-gray-500\">\r\n                            <Clock className=\"w-3 h-3 mr-1\" />\r\n                            {step.duration}\r\n                          </div> */}\r\n                        </div>\r\n                      </div>\r\n                    );\r\n                  })}\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Mobile Timeline */}\r\n            <div className=\"lg:hidden space-y-6\">\r\n              {steps.map((step, index) => {\r\n                const Icon = step.icon;\r\n                const isActive = activeStep >= step.id;\r\n                const isCurrent = activeStep === step.id;\r\n                \r\n                return (\r\n                  <div\r\n                    key={step.id}\r\n                    className={`relative transition-all duration-300 ${\r\n                      isCurrent ? 'scale-105' : ''\r\n                    }`}\r\n                  >\r\n                    <Card className={`border-2 ${isCurrent ? step.borderColor : 'border-gray-100'}`}>\r\n                      <CardContent className=\"p-6\">\r\n                        <div className=\"flex items-start space-x-4\">\r\n                          <div className={`w-12 h-12 rounded-full flex items-center justify-center flex-shrink-0 ${\r\n                            isActive ? 'bg-primary text-white' : 'bg-gray-100 text-gray-400'\r\n                          }`}>\r\n                            <Icon className=\"w-6 h-6\" />\r\n                          </div>\r\n                          \r\n                          <div className=\"flex-1\">\r\n                            <div className=\"flex items-center justify-between mb-2\">\r\n                              <h3 className={`font-bold text-lg ${isCurrent ? 'text-primary' : 'text-gray-900'}`}>\r\n                                {step.title}\r\n                              </h3>\r\n                              {/* <span className=\"text-xs text-gray-500 flex items-center\">\r\n                                <Clock className=\"w-3 h-3 mr-1\" />\r\n                                {step.duration}\r\n                              </span> */}\r\n                            </div>\r\n                            <p className=\"text-gray-600 text-sm leading-relaxed\">\r\n                              {step.description}\r\n                            </p>\r\n                          </div>\r\n                        </div>\r\n                      </CardContent>\r\n                    </Card>\r\n\r\n                    {/* Connection Line */}\r\n                    {index < steps.length - 1 && (\r\n                      <div className=\"flex justify-center\">\r\n                        <div className={`w-1 h-6 ${isActive ? 'bg-primary' : 'bg-gray-200'}`}></div>\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                );\r\n              })}\r\n            </div>\r\n          </div>\r\n\r\n          {/* Active Step Details */}\r\n          <div className={`transition-all duration-1000 delay-600 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>\r\n            {steps.map((step) => {\r\n              if (step.id !== activeStep) return null;\r\n              \r\n              return (\r\n                <Card key={step.id} className={`border-2 ${step.borderColor} ${step.bgColor}`}>\r\n                  <CardContent className=\"p-8\">\r\n                    <div className=\"grid md:grid-cols-2 gap-8 items-center\">\r\n                      <div className=\"space-y-6\">\r\n                        <div className=\"space-y-3\">\r\n                          <div className=\"flex items-center space-x-3\">\r\n                            <div className={`w-12 h-12 bg-white rounded-lg flex items-center justify-center`}>\r\n                              <step.icon className={`w-6 h-6 ${step.color}`} />\r\n                            </div>\r\n                            <div>\r\n                              <h3 className=\"text-3xl font-bold text-gray-900\">\r\n                                Étape {step.id}: {step.title}\r\n                              </h3>\r\n                              {/* <div className=\"flex items-center text-sm text-gray-600\">\r\n                                <Clock className=\"w-4 h-4 mr-1\" />\r\n                                Durée: {step.duration}\r\n                              </div> */}\r\n                            </div>\r\n                          </div>\r\n                          \r\n                          <p className=\"text-gray-700 leading-relaxed\">\r\n                            {step.description}\r\n                          </p>\r\n                        </div>\r\n\r\n                        <div className=\"space-y-3\">\r\n                          <h4 className=\"font-semibold text-gray-900\">Actions réalisées :</h4>\r\n                          <ul className=\"space-y-2\">\r\n                            {step.details.map((detail, detailIndex) => (\r\n                              <li key={detailIndex} className=\"flex items-start space-x-3 text-sm text-gray-700\">\r\n                                <CheckCircle className={`w-4 h-4 ${step.color} flex-shrink-0 mt-0.5`} />\r\n                                <span>{detail}</span>\r\n                              </li>\r\n                            ))}\r\n                          </ul>\r\n                        </div>\r\n                      </div>\r\n\r\n                      <div className=\"text-center\">\r\n                        <div className=\"w-48 h-48 bg-white/50 rounded-full flex items-center justify-center mx-auto mb-6\">\r\n                          <step.icon className={`w-24 h-24 ${step.color}`} />\r\n                        </div>\r\n                        <p className=\"text-sm text-gray-600\">\r\n                          Étape {step.id} sur 4\r\n                        </p>\r\n                      </div>\r\n                    </div>\r\n                  </CardContent>\r\n                </Card>\r\n              );\r\n            })}\r\n          </div>\r\n\r\n          {/* CTA Section */}\r\n          <div className={`text-center mt-16 transition-all duration-1000 delay-800 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>\r\n            <div className=\"bg-gray-50 rounded-2xl p-8\">\r\n              <h3 className=\"text-2xl font-bold text-gray-900 mb-4\">\r\n                Prêt à commencer votre démarche ?\r\n              </h3>\r\n              <p className=\"text-gray-600 mb-6 max-w-2xl mx-auto\">\r\n                Nos experts vous accompagnent à chaque étape pour garantir \r\n                le succès de votre projet foncier.\r\n              </p>\r\n              <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\r\n                <Button asChild size=\"lg\" className=\"group\">\r\n                  <Link href=\"/formulaire\" className=\"flex items-center\">\r\n                    Commencer maintenant\r\n                    <ArrowRight className=\"ml-2 h-4 w-4 transition-transform group-hover:translate-x-1\" />\r\n                  </Link>\r\n                </Button>\r\n                {/* <Button asChild variant=\"outline\" size=\"lg\">\r\n                  <Link href=\"#ressources\">\r\n                    Télécharger le guide\r\n                  </Link>\r\n                </Button> */}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;;;AAPA;;;;;;;AASO,SAAS;;IACd,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,aAAa,CAAA,GAAA,4RAAA,CAAA,SAAM,AAAD,EAAe;IACvC,MAAM,iBAAiB,CAAA,GAAA,iIAAA,CAAA,oBAAiB,AAAD,IAAI,OAAO;IAElD,CAAA,GAAA,4RAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM,WAAW,IAAI;4CACnB,CAAC,CAAC,MAAM;oBACN,IAAI,MAAM,cAAc,EAAE;wBACxB,aAAa;oBACf;gBACF;2CACA;gBAAE,WAAW;YAAI;YAGnB,IAAI,WAAW,OAAO,EAAE;gBACtB,SAAS,OAAO,CAAC,WAAW,OAAO;YACrC;YAEA;4CAAO,IAAM,SAAS,UAAU;;QAClC;mCAAG,EAAE;IAEL,CAAA,GAAA,4RAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM,WAAW;qDAAY;oBAC3B;6DAAc,CAAA,OAAQ,SAAS,IAAI,IAAI,OAAO;;gBAChD;oDAAG;YAEH;4CAAO,IAAM,cAAc;;QAC7B;mCAAG,EAAE;IAEL,MAAM,UAAU;QAAE,QAAA,6RAAA,CAAA,SAAM;QAAE,QAAA,iSAAA,CAAA,SAAM;QAAE,UAAA,qSAAA,CAAA,WAAQ;QAAE,OAAA,2RAAA,CAAA,QAAK;IAAC;IAClD,MAAM,QAAQ,gBAAgB,OAAO,IAAI,CAAC,OAAc,CAAC;YACvD,GAAG,IAAI;YACP,MAAM,OAAO,CAAC,KAAK,IAAI,CAAC,IAAI,qSAAA,CAAA,WAAQ;QACtC,CAAC,MAAM,EAAE;IAET,qBACE,4TAAC;QAAQ,KAAK;QAAY,IAAG;QAAY,WAAU;kBACjD,cAAA,4TAAC;YAAI,WAAU;sBACb,cAAA,4TAAC;gBAAI,WAAU;;kCAEb,4TAAC;wBAAI,WAAW,CAAC,yDAAyD,EAAE,YAAY,8BAA8B,2BAA2B;;0CAC/I,4TAAC;gCAAI,WAAU;0CAAiG;;;;;;0CAGhH,4TAAC;gCAAG,WAAU;0CACX,gBAAgB,SAAS;;;;;;0CAE5B,4TAAC;gCAAE,WAAU;0CACV,gBAAgB,YAAY;;;;;;;;;;;;kCAKjC,4TAAC;wBAAI,WAAW,CAAC,6CAA6C,EAAE,YAAY,8BAA8B,2BAA2B;;0CAEnI,4TAAC;gCAAI,WAAU;0CACb,cAAA,4TAAC;oCAAI,WAAU;;sDAEb,4TAAC;4CAAI,WAAU;;;;;;sDACf,4TAAC;4CACC,WAAU;4CACV,OAAO;gDAAE,OAAO,GAAG,AAAC,aAAa,IAAK,IAAI,CAAC,CAAC;4CAAC;;;;;;sDAI/C,4TAAC;4CAAI,WAAU;sDACZ,MAAM,GAAG,CAAC,CAAC,MAAM;gDAChB,MAAM,OAAO,KAAK,IAAI;gDACtB,MAAM,WAAW,cAAc,KAAK,EAAE;gDACtC,MAAM,YAAY,eAAe,KAAK,EAAE;gDAExC,qBACE,4TAAC;oDAEC,WAAU;oDACV,SAAS,IAAM,cAAc,KAAK,EAAE;;sEAGpC,4TAAC;4DAAI,WAAW,CAAC,oFAAoF,EACnG,WACI,8CACA,uGACJ;sEACA,cAAA,4TAAC;gEAAK,WAAU;;;;;;;;;;;sEAIlB,4TAAC;4DAAI,WAAU;;8EACb,4TAAC;oEAAG,WAAW,CAAC,iDAAiD,EAC/D,YAAY,iBAAiB,iBAC7B;8EACC,KAAK,KAAK;;;;;;8EAEb,4TAAC;oEAAE,WAAU;;wEACV,KAAK,WAAW,CAAC,KAAK,CAAC,GAAG;wEAAM,KAAK,WAAW,CAAC,MAAM,GAAG,MAAM,QAAQ;;;;;;;;;;;;;;mDArBxE,KAAK,EAAE;;;;;4CA8BlB;;;;;;;;;;;;;;;;;0CAMN,4TAAC;gCAAI,WAAU;0CACZ,MAAM,GAAG,CAAC,CAAC,MAAM;oCAChB,MAAM,OAAO,KAAK,IAAI;oCACtB,MAAM,WAAW,cAAc,KAAK,EAAE;oCACtC,MAAM,YAAY,eAAe,KAAK,EAAE;oCAExC,qBACE,4TAAC;wCAEC,WAAW,CAAC,qCAAqC,EAC/C,YAAY,cAAc,IAC1B;;0DAEF,4TAAC,4HAAA,CAAA,OAAI;gDAAC,WAAW,CAAC,SAAS,EAAE,YAAY,KAAK,WAAW,GAAG,mBAAmB;0DAC7E,cAAA,4TAAC,4HAAA,CAAA,cAAW;oDAAC,WAAU;8DACrB,cAAA,4TAAC;wDAAI,WAAU;;0EACb,4TAAC;gEAAI,WAAW,CAAC,sEAAsE,EACrF,WAAW,0BAA0B,6BACrC;0EACA,cAAA,4TAAC;oEAAK,WAAU;;;;;;;;;;;0EAGlB,4TAAC;gEAAI,WAAU;;kFACb,4TAAC;wEAAI,WAAU;kFACb,cAAA,4TAAC;4EAAG,WAAW,CAAC,kBAAkB,EAAE,YAAY,iBAAiB,iBAAiB;sFAC/E,KAAK,KAAK;;;;;;;;;;;kFAOf,4TAAC;wEAAE,WAAU;kFACV,KAAK,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;4CAQ1B,QAAQ,MAAM,MAAM,GAAG,mBACtB,4TAAC;gDAAI,WAAU;0DACb,cAAA,4TAAC;oDAAI,WAAW,CAAC,QAAQ,EAAE,WAAW,eAAe,eAAe;;;;;;;;;;;;uCAnCnE,KAAK,EAAE;;;;;gCAwClB;;;;;;;;;;;;kCAKJ,4TAAC;wBAAI,WAAW,CAAC,uCAAuC,EAAE,YAAY,8BAA8B,2BAA2B;kCAC5H,MAAM,GAAG,CAAC,CAAC;4BACV,IAAI,KAAK,EAAE,KAAK,YAAY,OAAO;4BAEnC,qBACE,4TAAC,4HAAA,CAAA,OAAI;gCAAe,WAAW,CAAC,SAAS,EAAE,KAAK,WAAW,CAAC,CAAC,EAAE,KAAK,OAAO,EAAE;0CAC3E,cAAA,4TAAC,4HAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,4TAAC;wCAAI,WAAU;;0DACb,4TAAC;gDAAI,WAAU;;kEACb,4TAAC;wDAAI,WAAU;;0EACb,4TAAC;gEAAI,WAAU;;kFACb,4TAAC;wEAAI,WAAW,CAAC,8DAA8D,CAAC;kFAC9E,cAAA,4TAAC,KAAK,IAAI;4EAAC,WAAW,CAAC,QAAQ,EAAE,KAAK,KAAK,EAAE;;;;;;;;;;;kFAE/C,4TAAC;kFACC,cAAA,4TAAC;4EAAG,WAAU;;gFAAmC;gFACxC,KAAK,EAAE;gFAAC;gFAAG,KAAK,KAAK;;;;;;;;;;;;;;;;;;0EASlC,4TAAC;gEAAE,WAAU;0EACV,KAAK,WAAW;;;;;;;;;;;;kEAIrB,4TAAC;wDAAI,WAAU;;0EACb,4TAAC;gEAAG,WAAU;0EAA8B;;;;;;0EAC5C,4TAAC;gEAAG,WAAU;0EACX,KAAK,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,4BACzB,4TAAC;wEAAqB,WAAU;;0FAC9B,4TAAC,kTAAA,CAAA,cAAW;gFAAC,WAAW,CAAC,QAAQ,EAAE,KAAK,KAAK,CAAC,qBAAqB,CAAC;;;;;;0FACpE,4TAAC;0FAAM;;;;;;;uEAFA;;;;;;;;;;;;;;;;;;;;;;0DASjB,4TAAC;gDAAI,WAAU;;kEACb,4TAAC;wDAAI,WAAU;kEACb,cAAA,4TAAC,KAAK,IAAI;4DAAC,WAAW,CAAC,UAAU,EAAE,KAAK,KAAK,EAAE;;;;;;;;;;;kEAEjD,4TAAC;wDAAE,WAAU;;4DAAwB;4DAC5B,KAAK,EAAE;4DAAC;;;;;;;;;;;;;;;;;;;;;;;;+BA3Cd,KAAK,EAAE;;;;;wBAkDtB;;;;;;kCAIF,4TAAC;wBAAI,WAAW,CAAC,yDAAyD,EAAE,YAAY,8BAA8B,2BAA2B;kCAC/I,cAAA,4TAAC;4BAAI,WAAU;;8CACb,4TAAC;oCAAG,WAAU;8CAAwC;;;;;;8CAGtD,4TAAC;oCAAE,WAAU;8CAAuC;;;;;;8CAIpD,4TAAC;oCAAI,WAAU;8CACb,cAAA,4TAAC,8HAAA,CAAA,SAAM;wCAAC,OAAO;wCAAC,MAAK;wCAAK,WAAU;kDAClC,cAAA,4TAAC,8RAAA,CAAA,UAAI;4CAAC,MAAK;4CAAc,WAAU;;gDAAoB;8DAErD,4TAAC,ySAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAe1C;GA3PgB;KAAA", "debugId": null}}]}